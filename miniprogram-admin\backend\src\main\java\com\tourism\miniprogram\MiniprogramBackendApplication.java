package com.tourism.miniprogram;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 旅游讲解小程序后端服务启动类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@SpringBootApplication
@MapperScan("com.tourism.miniprogram.mapper")
@EnableScheduling
public class MiniprogramBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(MiniprogramBackendApplication.class, args);
        System.out.println("旅游讲解小程序后端服务启动成功！");
        System.out.println("API文档地址: http://localhost:8080/api/doc.html");
    }
}
