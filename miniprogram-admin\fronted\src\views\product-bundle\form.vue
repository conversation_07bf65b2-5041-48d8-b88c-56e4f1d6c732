<template>
  <div class="product-bundle-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑组合包' : '新增组合包' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="组合包名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入组合包名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="组合包长图" prop="description">
          <div class="image-upload-container">
            <div v-if="form.description" class="image-preview">
              <img :src="form.description" alt="组合包长图" class="preview-image" />
              <div class="image-overlay">
                <el-button type="primary" size="small" @click="handleImageUpload">更换图片</el-button>
                <el-button type="danger" size="small" @click="removeImage">删除图片</el-button>
              </div>
            </div>
            <div v-else class="upload-placeholder" @click="handleImageUpload">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">点击上传组合包长图</div>
              <div class="upload-hint">建议尺寸：750x400px，支持 JPG、PNG 格式</div>
            </div>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="onFileChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="优惠金额" prop="discount">
          <el-input-number
            v-model="form.discount"
            :min="0"
            :precision="2"
            placeholder="请输入优惠金额"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="包含景区" prop="scenicIds">
          <el-select
            v-model="form.scenicIds"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入景区名称进行搜索"
            :remote-method="searchScenics"
            :loading="scenicLoading"
            style="width: 100%"
            @change="onScenicChange"
            clearable
            no-data-text="请输入关键词搜索景区"
            loading-text="搜索中..."
          >
            <el-option
              v-for="scenic in scenicOptions"
              :key="scenic.scenicId"
              :label="`${scenic.title} (${scenic.provinceName || ''}${scenic.cityName || ''})`"
              :value="scenic.scenicId"
            >
              <span style="float: left">{{ scenic.title }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ scenic.provinceName || '' }}{{ scenic.cityName || '' }}
              </span>
            </el-option>
          </el-select>
          <div v-if="form.scenicIds.length > 0" class="selected-scenics">
            <el-tag
              v-for="scenicId in form.scenicIds"
              :key="scenicId"
              closable
              @close="removeScenicId(scenicId)"
              style="margin: 4px 4px 0 0"
            >
              {{ getScenicName(scenicId) }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getProductBundleById, createProductBundle, updateProductBundle } from '@/api/productBundle'
import { getScenicPage, getScenicDetail } from '@/api/scenic'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const formRef = ref()
const fileInput = ref()
const uploadLoading = ref(false)
const scenicLoading = ref(false)
const scenicOptions = ref([])

const form = reactive({
  name: '',
  description: '', // 现在存储图片URL
  discount: 0,
  scenicIds: [], // 现在存储景区ID数组
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入组合包名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请上传组合包长图', trigger: 'change' }
  ],
  discount: [
    { required: true, message: '请输入优惠金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '优惠金额不能小于0', trigger: 'blur' }
  ],
  scenicIds: [
    {
      required: true,
      message: '请选择包含的景区',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          callback(new Error('请选择包含的景区'))
        } else {
          callback()
        }
      }
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 图片上传相关方法
const handleImageUpload = () => {
  fileInput.value?.click()
}

const onFileChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  try {
    uploadLoading.value = true
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const imageUrl = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()
    form.description = imageUrl
    ElMessage.success('图片上传成功')
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
    // 清空文件输入框
    event.target.value = ''
  }
}

const removeImage = () => {
  form.description = ''
  ElMessage.success('图片已删除')
}

// 景区搜索相关方法
const searchScenics = async (query) => {
  if (!query || query.trim().length < 1) {
    return
  }

  try {
    scenicLoading.value = true
    console.log('搜索景区，关键词:', query)

    const response = await getScenicPage({
      title: query.trim(),
      current: 1,
      size: 50
    })

    console.log('景区搜索响应:', response)

    if (response && response.data && response.data.records) {
      scenicOptions.value = response.data.records
      console.log('搜索到的景区:', scenicOptions.value)
    } else if (response && response.records) {
      // 兼容不同的响应格式
      scenicOptions.value = response.records
    } else {
      scenicOptions.value = []
      console.log('未搜索到景区')
    }
  } catch (error) {
    console.error('搜索景区失败:', error)
    ElMessage.error(`搜索景区失败: ${error.message || '网络错误'}`)
    scenicOptions.value = []
  } finally {
    scenicLoading.value = false
  }
}

const onScenicChange = (values) => {
  // values 已经直接绑定到 form.scenicIds，无需额外处理
  console.log('景区选择变化:', values)
}

const removeScenicId = (scenicId) => {
  const index = form.scenicIds.indexOf(scenicId)
  if (index > -1) {
    form.scenicIds.splice(index, 1)
  }
}

const getScenicName = (scenicId) => {
  const scenic = scenicOptions.value.find(s => s.scenicId === scenicId)
  return scenic ? scenic.title : scenicId
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const response = await getProductBundleById(route.params.id)
    console.log('获取组合包详情响应:', response)

    const data = response.data
    console.log('组合包详情数据:', data)
    console.log('景区ID数据:', {
      scenicIds: data.scenicIds,
      type: typeof data.scenicIds,
      isArray: Array.isArray(data.scenicIds)
    })

    Object.assign(form, data)

    // 处理景区ID数组
    if (data.scenicIds && Array.isArray(data.scenicIds)) {
      console.log('景区ID是数组格式:', data.scenicIds)
      // form.scenicIds 已经通过 Object.assign 赋值了
      // 预加载已选择的景区信息
      await loadSelectedScenics()
    } else if (data.scenicIds && typeof data.scenicIds === 'string') {
      console.log('景区ID是字符串格式，尝试解析:', data.scenicIds)
      try {
        // 尝试解析JSON字符串
        const parsed = JSON.parse(data.scenicIds)
        if (Array.isArray(parsed)) {
          form.scenicIds = parsed
          console.log('JSON解析成功:', parsed)
        } else {
          // 按逗号分割
          form.scenicIds = data.scenicIds.split(',').filter(id => id.trim())
          console.log('按逗号分割:', form.scenicIds)
        }
      } catch (error) {
        // 按逗号分割
        form.scenicIds = data.scenicIds.split(',').filter(id => id.trim())
        console.log('JSON解析失败，按逗号分割:', form.scenicIds)
      }
      await loadSelectedScenics()
    } else {
      console.log('景区ID数据格式未知或为空:', data.scenicIds)
    }
  } catch (error) {
    ElMessage.error('获取组合包详情失败')
    console.error(error)
  }
}

// 加载已选择的景区信息
const loadSelectedScenics = async () => {
  if (form.scenicIds.length === 0) return

  try {
    console.log('加载已选择的景区信息，景区IDs:', form.scenicIds)

    // 使用 getScenicDetail 方法获取每个景区的详细信息
    const scenicPromises = form.scenicIds.map(async (scenicId) => {
      try {
        const response = await getScenicDetail(scenicId)
        console.log(`景区${scenicId}详情响应:`, response)

        if (response && response.data) {
          return response.data
        } else if (response) {
          return response
        }
        return null
      } catch (error) {
        console.error(`获取景区${scenicId}信息失败:`, error)
        // 如果单个景区获取失败，创建一个基本的景区对象
        return {
          scenicId: scenicId,
          title: scenicId,
          provinceName: '',
          cityName: ''
        }
      }
    })

    const scenics = await Promise.all(scenicPromises)
    const validScenics = scenics.filter(s => s !== null)

    console.log('加载到的景区信息:', validScenics)

    // 合并到选项中，避免重复
    const existingIds = scenicOptions.value.map(s => s.scenicId)
    const newScenics = validScenics.filter(s => !existingIds.includes(s.scenicId))
    scenicOptions.value = [...scenicOptions.value, ...newScenics]

    console.log('合并后的景区选项:', scenicOptions.value)
  } catch (error) {
    console.error('加载已选择景区信息失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 确保景区ID是数组格式
    if (!Array.isArray(form.scenicIds)) {
      form.scenicIds = []
    }

    // 调试信息
    console.log('提交的表单数据:', form)
    console.log('景区ID数组:', form.scenicIds)
    console.log('景区ID数组类型:', typeof form.scenicIds)
    console.log('是否为数组:', Array.isArray(form.scenicIds))
    console.log('景区ID数组长度:', form.scenicIds.length)

    // 验证景区ID不为空
    if (form.scenicIds.length === 0) {
      ElMessage.error('请至少选择一个景区')
      return
    }

    const submitData = {
      ...form,
      scenicIds: form.scenicIds // 确保是数组格式
    }

    console.log('最终提交数据:', submitData)

    if (isEdit.value) {
      await updateProductBundle(route.params.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await createProductBundle(submitData)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error('提交错误:', error)

      // 显示详细错误信息
      if (error.response && error.response.data && error.response.data.message) {
        ElMessage.error(`错误详情: ${error.response.data.message}`)
      }
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/product-bundle')
}

// 初始化加载一些景区选项
const initScenics = async () => {
  try {
    const response = await getScenicPage({
      current: 1,
      size: 20
    })

    if (response && response.data && response.data.records) {
      scenicOptions.value = response.data.records
    } else if (response && response.records) {
      scenicOptions.value = response.records
    }

    console.log('初始化景区选项:', scenicOptions.value)
  } catch (error) {
    console.error('初始化景区选项失败:', error)
  }
}

// 初始化
onMounted(async () => {
  // 先加载一些默认的景区选项
  await initScenics()

  if (isEdit.value) {
    await fetchDetail()
  }
})
</script>

<style scoped>
.product-bundle-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

/* 图片上传样式 */
.image-upload-container {
  width: 100%;
}

.image-preview {
  position: relative;
  width: 400px;
  height: 200px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-placeholder {
  width: 400px;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

/* 景区选择样式 */
.selected-scenics {
  margin-top: 8px;
  min-height: 32px;
}

.el-select {
  width: 100%;
}

.el-option {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}
</style>
