package com.tourism.miniprogram.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.StrUtil;
import com.tourism.miniprogram.dto.LoginResponse;
import com.tourism.miniprogram.dto.PhoneDecryptRequest;
import com.tourism.miniprogram.dto.PhoneDecryptResponse;
import com.tourism.miniprogram.dto.WechatLoginRequest;
import com.tourism.miniprogram.entity.User;
import com.tourism.miniprogram.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 微信服务类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class WechatService {

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 微信小程序登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse login(WechatLoginRequest request) {
        try {
            // 1. 调用微信接口获取session信息
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService()
                    .getSessionInfo(request.getCode());
            
            if (sessionInfo == null || StrUtil.isBlank(sessionInfo.getOpenid())) {
                throw new RuntimeException("微信登录失败，无法获取用户信息");
            }

            String openid = sessionInfo.getOpenid();
            log.info("微信登录，openid: {}", openid);

            // 2. 查询用户是否存在
            User existUser = userService.getUserByOpenid(openid);
            boolean isNewUser = false;

            if (existUser == null) {
                // 3. 新用户，创建用户记录
                existUser = new User();
                existUser.setOpenid(openid);
                existUser.setNickname(request.getNickname());
                existUser.setAvatar(request.getAvatarUrl());
                existUser.setProvince(request.getRegion());
                existUser.setPhone(request.getPhone());
                existUser.setCreatedAt(LocalDateTime.now());
                existUser.setStatus(1);

                userService.save(existUser);
                isNewUser = true;
                log.info("创建新用户，userId: {}", existUser.getId());
            } else {
                // 4. 老用户，更新用户信息
                if (StrUtil.isNotBlank(request.getNickname())) {
                    existUser.setNickname(request.getNickname());
                }
                if (StrUtil.isNotBlank(request.getAvatarUrl())) {
                    existUser.setAvatar(request.getAvatarUrl());
                }
                if (StrUtil.isNotBlank(request.getRegion())) {
                    existUser.setProvince(request.getRegion());
                }
                if (StrUtil.isNotBlank(request.getPhone())) {
                    existUser.setPhone(request.getPhone());
                }

                userService.updateById(existUser);
                log.info("更新用户信息，userId: {}", existUser.getId());
            }

            // 5. 生成JWT token
            String token = jwtUtil.generateToken(existUser.getId(), openid);

            // 6. 构建响应
            LoginResponse response = new LoginResponse();
            response.setUserId(existUser.getId());
            response.setToken(token);
            response.setNickname(existUser.getNickname());
            response.setAvatarUrl(existUser.getAvatar());
            response.setRegion(existUser.getProvince());
            response.setPhone(existUser.getPhone());
            response.setIsNewUser(isNewUser);

            return response;

        } catch (WxErrorException e) {
            log.error("微信登录异常", e);
            throw new RuntimeException("微信登录失败：" + e.getError().getErrorMsg());
        } catch (Exception e) {
            log.error("登录处理异常", e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }

    /**
     * 微信小程序手机号解密
     *
     * @param request 解密请求
     * @return 解密响应
     */
    @Transactional(rollbackFor = Exception.class)
    public PhoneDecryptResponse decryptPhone(PhoneDecryptRequest request) {
        try {
            // 1. 通过code获取session信息
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService()
                    .getSessionInfo(request.getCode());

            if (sessionInfo == null || StrUtil.isBlank(sessionInfo.getSessionKey())) {
                throw new RuntimeException("获取session信息失败");
            }

            // 2. 解密手机号
            WxMaPhoneNumberInfo phoneInfo = wxMaService.getUserService()
                    .getPhoneNoInfo(sessionInfo.getSessionKey(), request.getEncryptedData(), request.getIv());

            if (phoneInfo == null || StrUtil.isBlank(phoneInfo.getPhoneNumber())) {
                throw new RuntimeException("手机号解密失败");
            }

            log.info("手机号解密成功，openid: {}, phone: {}", sessionInfo.getOpenid(), phoneInfo.getPhoneNumber());

            // 3. 查找用户并更新手机号
            User user = userService.getUserByOpenid(sessionInfo.getOpenid());
            boolean bindSuccess = false;

            if (user != null) {
                user.setPhone(phoneInfo.getPhoneNumber());
                userService.updateById(user);
                bindSuccess = true;
                log.info("用户手机号绑定成功，userId: {}, phone: {}", user.getId(), phoneInfo.getPhoneNumber());
            } else {
                log.warn("未找到对应用户，openid: {}", sessionInfo.getOpenid());
            }

            // 4. 构建响应
            PhoneDecryptResponse response = new PhoneDecryptResponse();
            response.setPhoneNumber(phoneInfo.getPhoneNumber());
            response.setPurePhoneNumber(phoneInfo.getPurePhoneNumber());
            response.setCountryCode(phoneInfo.getCountryCode());
            response.setUserId(user != null ? user.getId() : null);
            response.setBindSuccess(bindSuccess);

            return response;

        } catch (WxErrorException e) {
            log.error("微信手机号解密异常", e);
            throw new RuntimeException("手机号解密失败：" + e.getError().getErrorMsg());
        } catch (Exception e) {
            log.error("手机号解密处理异常", e);
            throw new RuntimeException("手机号解密失败：" + e.getMessage());
        }
    }
}
