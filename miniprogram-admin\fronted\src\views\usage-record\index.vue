<template>
  <div class="usage-record-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>使用记录</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input-number
            v-model="searchForm.userId"
            placeholder="请输入用户ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="景区ID">
          <el-input-number
            v-model="searchForm.scenicId"
            placeholder="请输入景区ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="卡券ID">
          <el-input-number
            v-model="searchForm.couponId"
            placeholder="请输入卡券ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="使用时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="scenicId" label="景区ID" width="100" />
        <el-table-column prop="couponId" label="卡券ID" width="100" />
        <el-table-column prop="productId" label="产品ID" width="100" />
        <el-table-column prop="bundleId" label="组合包ID" width="100" />
        <el-table-column prop="activationCodeId" label="激活码ID" width="120" />
        <el-table-column prop="usedAt" label="使用时间" width="180" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUsageRecordPage } from '@/api/usageRecord'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dateRange = ref([])

const searchForm = reactive({
  userId: null,
  scenicId: null,
  couponId: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算属性
const searchParams = computed(() => {
  const params = { ...searchForm }
  if (dateRange.value && dateRange.value.length === 2) {
    params.startTime = dateRange.value[0]
    params.endTime = dateRange.value[1]
  }
  return params
})

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchParams.value
    }
    
    const { data } = await getUsageRecordPage(params)
    tableData.value = data.records
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取使用记录列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    userId: null,
    scenicId: null,
    couponId: null
  })
  dateRange.value = []
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 操作
const handleDetail = (row) => {
  router.push(`/usage-record/detail/${row.id}`)
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.usage-record-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
