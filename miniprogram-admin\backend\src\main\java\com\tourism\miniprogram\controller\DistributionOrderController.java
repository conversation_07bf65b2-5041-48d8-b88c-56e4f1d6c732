package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.DistributionOrder;
import com.tourism.miniprogram.service.DistributionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销订单控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/distribution/order")
@Api(tags = "分销订单管理")
public class DistributionOrderController {

    @Autowired
    private DistributionOrderService distributionOrderService;

    /**
     * 获取分销订单列表（分页查询）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param distributorId 分销员ID
     * @param orderId 订单ID
     * @param status 结算状态
     * @return 分销订单列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取分销订单列表", notes = "分页查询分销订单，支持按分销员、订单、状态筛选")
    public Result<IPage<DistributionOrder>> getDistributionOrderList(
            @ApiParam(value = "当前页码", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "分销员ID") @RequestParam(required = false) Integer distributorId,
            @ApiParam(value = "订单ID") @RequestParam(required = false) Integer orderId,
            @ApiParam(value = "结算状态") @RequestParam(required = false) String status) {
        try {
            Page<DistributionOrder> page = new Page<>(current, size);
            QueryWrapper<DistributionOrder> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (distributorId != null) {
                queryWrapper.eq("distributor_id", distributorId);
            }
            if (orderId != null) {
                queryWrapper.eq("order_id", orderId);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_at");

            IPage<DistributionOrder> orderPage = distributionOrderService.page(page, queryWrapper);
            return Result.success(orderPage);
        } catch (Exception e) {
            log.error("获取分销订单列表失败", e);
            return Result.error("获取分销订单列表失败");
        }
    }

    /**
     * 获取分销订单详情
     *
     * @param distributionOrderId 分销订单ID
     * @return 分销订单信息
     */
    @GetMapping("/{distributionOrderId}")
    @ApiOperation(value = "获取分销订单详情", notes = "根据分销订单ID获取详细信息")
    public Result<DistributionOrder> getDistributionOrderInfo(
            @ApiParam(value = "分销订单ID", required = true) @PathVariable Integer distributionOrderId) {
        try {
            DistributionOrder distributionOrder = distributionOrderService.getById(distributionOrderId);
            if (distributionOrder == null) {
                return Result.error(404, "分销订单不存在");
            }
            return Result.success(distributionOrder);
        } catch (Exception e) {
            log.error("获取分销订单详情失败，distributionOrderId: {}", distributionOrderId, e);
            return Result.error("获取分销订单详情失败");
        }
    }

    /**
     * 根据分销员ID获取分销订单列表
     *
     * @param distributorId 分销员ID
     * @return 分销订单列表
     */
    @GetMapping("/distributor/{distributorId}")
    @ApiOperation(value = "获取分销员订单列表", notes = "获取指定分销员的所有分销订单")
    public Result<List<DistributionOrder>> getDistributionOrdersByDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            List<DistributionOrder> orders = distributionOrderService.getByDistributorId(distributorId);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("获取分销员订单列表失败，distributorId: {}", distributorId, e);
            return Result.error("获取分销订单列表失败");
        }
    }

    /**
     * 根据订单ID获取分销订单
     *
     * @param orderId 订单ID
     * @return 分销订单信息
     */
    @GetMapping("/order/{orderId}")
    @ApiOperation(value = "根据订单ID获取分销订单", notes = "通过原始订单ID查询对应的分销订单")
    public Result<DistributionOrder> getDistributionOrderByOrderId(
            @ApiParam(value = "订单ID", required = true) @PathVariable Integer orderId) {
        try {
            DistributionOrder distributionOrder = distributionOrderService.getByOrderId(orderId);
            if (distributionOrder == null) {
                return Result.error(404, "该订单没有分销记录");
            }
            return Result.success(distributionOrder);
        } catch (Exception e) {
            log.error("根据订单ID获取分销订单失败，orderId: {}", orderId, e);
            return Result.error("获取分销订单失败");
        }
    }

    /**
     * 根据状态获取分销订单列表
     *
     * @param status 结算状态
     * @return 分销订单列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态获取分销订单", notes = "获取指定状态的分销订单列表")
    public Result<List<DistributionOrder>> getDistributionOrdersByStatus(
            @ApiParam(value = "结算状态", required = true) @PathVariable String status) {
        try {
            List<DistributionOrder> orders = distributionOrderService.getByStatus(status);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("根据状态获取分销订单失败，status: {}", status, e);
            return Result.error("获取分销订单失败");
        }
    }

    /**
     * 创建分销订单记录
     *
     * @param distributorId 分销员ID
     * @param orderId 订单ID
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @param baseRate 基础佣金比例
     * @return 创建结果
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建分销订单记录", notes = "手动创建分销订单记录")
    public Result<DistributionOrder> createDistributionOrder(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "订单ID", required = true) @RequestParam Integer orderId,
            @ApiParam(value = "订单金额", required = true) @RequestParam BigDecimal orderAmount,
            @ApiParam(value = "佣金比例", required = true) @RequestParam BigDecimal commissionRate,
            @ApiParam(value = "基础佣金比例") @RequestParam(required = false, defaultValue = "10.00") BigDecimal baseRate) {
        try {
            // 检查订单是否已存在分销记录
            if (distributionOrderService.hasDistributionRecord(orderId)) {
                return Result.error(400, "订单已存在分销记录");
            }

            DistributionOrder distributionOrder = distributionOrderService.createDistributionOrder(
                    distributorId, orderId, orderAmount, commissionRate, baseRate);
            return Result.success(distributionOrder);
        } catch (Exception e) {
            log.error("创建分销订单记录失败，distributorId: {}, orderId: {}", distributorId, orderId, e);
            return Result.error("创建分销订单记录失败: " + e.getMessage());
        }
    }

    /**
     * 结算分销订单
     *
     * @param distributionOrderId 分销订单ID
     * @return 结算结果
     */
    @PutMapping("/{distributionOrderId}/settle")
    @ApiOperation(value = "结算分销订单", notes = "将分销订单标记为已结算状态")
    public Result<String> settleDistributionOrder(
            @ApiParam(value = "分销订单ID", required = true) @PathVariable Integer distributionOrderId) {
        try {
            boolean success = distributionOrderService.settleDistributionOrder(distributionOrderId);
            if (success) {
                return Result.success("分销订单结算成功");
            } else {
                return Result.error("分销订单结算失败");
            }
        } catch (Exception e) {
            log.error("结算分销订单失败，distributionOrderId: {}", distributionOrderId, e);
            return Result.error("结算分销订单失败");
        }
    }

    /**
     * 批量结算分销订单
     *
     * @param distributionOrderIds 分销订单ID列表
     * @return 结算结果
     */
    @PutMapping("/batch-settle")
    @ApiOperation(value = "批量结算分销订单", notes = "批量将分销订单标记为已结算状态")
    public Result<Integer> batchSettleDistributionOrders(
            @ApiParam(value = "分销订单ID列表", required = true) @RequestBody List<Integer> distributionOrderIds) {
        try {
            int successCount = distributionOrderService.batchSettleDistributionOrders(distributionOrderIds);
            return Result.success(successCount);
        } catch (Exception e) {
            log.error("批量结算分销订单失败，distributionOrderIds: {}", distributionOrderIds, e);
            return Result.error("批量结算分销订单失败");
        }
    }

    /**
     * 取消分销订单
     *
     * @param distributionOrderId 分销订单ID
     * @return 取消结果
     */
    @PutMapping("/{distributionOrderId}/cancel")
    @ApiOperation(value = "取消分销订单", notes = "将分销订单标记为已取消状态")
    public Result<String> cancelDistributionOrder(
            @ApiParam(value = "分销订单ID", required = true) @PathVariable Integer distributionOrderId) {
        try {
            boolean success = distributionOrderService.cancelDistributionOrder(distributionOrderId);
            if (success) {
                return Result.success("分销订单取消成功");
            } else {
                return Result.error("分销订单取消失败");
            }
        } catch (Exception e) {
            log.error("取消分销订单失败，distributionOrderId: {}", distributionOrderId, e);
            return Result.error("取消分销订单失败");
        }
    }

    /**
     * 获取分销员佣金统计信息
     *
     * @param distributorId 分销员ID
     * @return 统计信息
     */
    @GetMapping("/statistics/{distributorId}")
    @ApiOperation(value = "获取分销员佣金统计", notes = "获取分销员的佣金相关统计数据")
    public Result<Object> getDistributorCommissionStatistics(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            BigDecimal pendingAmount = distributionOrderService.getPendingCommissionAmount(distributorId);
            BigDecimal settledAmount = distributionOrderService.getSettledCommissionAmount(distributorId);
            BigDecimal totalAmount = distributionOrderService.getTotalCommissionAmount(distributorId);
            Integer orderCountValue = distributionOrderService.getOrderCountByDistributorId(distributorId);

            return Result.success(new Object() {
                public final BigDecimal pendingCommissionAmount = pendingAmount;
                public final BigDecimal settledCommissionAmount = settledAmount;
                public final BigDecimal totalCommissionAmount = totalAmount;
                public final Integer orderCount = orderCountValue;
            });
        } catch (Exception e) {
            log.error("获取分销员佣金统计失败，distributorId: {}", distributorId, e);
            return Result.error("获取佣金统计失败");
        }
    }

    /**
     * 获取系统分销订单统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取系统分销订单统计", notes = "获取系统整体分销订单统计数据")
    public Result<Object> getSystemDistributionOrderStatistics() {
        try {
            Integer totalOrderCount = distributionOrderService.getTotalDistributionOrderCount();
            BigDecimal totalCommissionAmount = distributionOrderService.getTotalSystemCommissionAmount();
            
            return Result.success(new Object() {
                public final Integer totalDistributionOrderCount = totalOrderCount;
                public final BigDecimal totalSystemCommissionAmount = totalCommissionAmount;
            });
        } catch (Exception e) {
            log.error("获取系统分销订单统计失败", e);
            return Result.error("获取分销订单统计失败");
        }
    }

    /**
     * 处理订单分销逻辑
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param orderAmount 订单金额
     * @return 处理结果
     */
    @PostMapping("/process")
    @ApiOperation(value = "处理订单分销逻辑", notes = "自动处理订单的分销逻辑，检查用户关系并创建分销记录")
    public Result<Boolean> processOrderDistribution(
            @ApiParam(value = "订单ID", required = true) @RequestParam Integer orderId,
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "订单金额", required = true) @RequestParam BigDecimal orderAmount) {
        try {
            boolean processed = distributionOrderService.processOrderDistribution(orderId, userId, orderAmount);
            return Result.success(processed);
        } catch (Exception e) {
            log.error("处理订单分销逻辑失败，orderId: {}, userId: {}", orderId, userId, e);
            return Result.error("处理订单分销逻辑失败");
        }
    }

    /**
     * 计算佣金金额
     *
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @return 佣金金额
     */
    @GetMapping("/calculate-commission")
    @ApiOperation(value = "计算佣金金额", notes = "根据订单金额和佣金比例计算佣金金额")
    public Result<BigDecimal> calculateCommissionAmount(
            @ApiParam(value = "订单金额", required = true) @RequestParam BigDecimal orderAmount,
            @ApiParam(value = "佣金比例", required = true) @RequestParam BigDecimal commissionRate) {
        try {
            BigDecimal commissionAmount = distributionOrderService.calculateCommissionAmount(orderAmount, commissionRate);
            return Result.success(commissionAmount);
        } catch (Exception e) {
            log.error("计算佣金金额失败，orderAmount: {}, commissionRate: {}", orderAmount, commissionRate, e);
            return Result.error("计算佣金金额失败");
        }
    }

    /**
     * 检查订单是否存在分销记录
     *
     * @param orderId 订单ID
     * @return 检查结果
     */
    @GetMapping("/check/{orderId}")
    @ApiOperation(value = "检查分销记录", notes = "检查订单是否已存在分销记录")
    public Result<Boolean> hasDistributionRecord(
            @ApiParam(value = "订单ID", required = true) @PathVariable Integer orderId) {
        try {
            boolean hasRecord = distributionOrderService.hasDistributionRecord(orderId);
            return Result.success(hasRecord);
        } catch (Exception e) {
            log.error("检查分销记录失败，orderId: {}", orderId, e);
            return Result.error("检查分销记录失败");
        }
    }
}
