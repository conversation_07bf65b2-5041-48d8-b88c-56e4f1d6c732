package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分销提现记录实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("distribution_withdrawal_records")
@ApiModel(value = "DistributionWithdrawalRecord对象", description = "分销提现记录信息")
public class DistributionWithdrawalRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "提现记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分销员ID")
    @TableField("distributor_id")
    @NotNull(message = "分销员ID不能为空")
    private Integer distributorId;

    @ApiModelProperty(value = "提现金额")
    @TableField("amount")
    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0.01", message = "提现金额必须大于0")
    private BigDecimal amount;

    @ApiModelProperty(value = "交易流水号")
    @TableField("transaction_no")
    private String transactionNo;

    @ApiModelProperty(value = "提现状态：pending-待处理，completed-已完成，rejected-已拒绝")
    @TableField("status")
    @NotBlank(message = "提现状态不能为空")
    private String status;

    @ApiModelProperty(value = "拒绝原因")
    @TableField("reject_reason")
    private String rejectReason;

    @ApiModelProperty(value = "申请时间")
    @TableField("applied_at")
    @NotNull(message = "申请时间不能为空")
    private LocalDateTime appliedAt;

    @ApiModelProperty(value = "完成时间")
    @TableField("completed_at")
    private LocalDateTime completedAt;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
