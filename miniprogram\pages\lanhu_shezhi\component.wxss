.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.box_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_2 {
  width: 378rpx;
  height: 48rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 46rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 14rpx;
}
.text_1 {
  width: 80rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 40rpx;
  letter-spacing: 0rpx;
  font-family: Inter-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 40rpx;
}
.box_3 {
  width: 750rpx;
  height: 1450rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.list_1 {
  width: 710rpx;
  height: 460rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 18rpx 0 0 20rpx;
}
.list-items_1-0 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  position: relative;
  width: 710rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  flex-direction: row;
  display: flex;
}
.text_2-0 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 30rpx 0 0 40rpx;
}
.label_1-0 {
  position: absolute;
  left: 580rpx;
  top: 14rpx;
  width: 72rpx;
  height: 72rpx;
}
.thumbnail_4-0 {
  width: 28rpx;
  height: 16rpx;
  margin: 66rpx 28rpx 0 558rpx;
}
.list-items_1-1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  position: relative;
  width: 710rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  flex-direction: row;
  display: flex;
}
.text_2-1 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 30rpx 0 0 40rpx;
}
.text_3-1 {
  position: absolute;
  left: 500rpx;
  top: 30rpx;
  width: 140rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: right;
  white-space: nowrap;
  line-height: 28rpx;
}
.thumbnail_4-1 {
  width: 28rpx;
  height: 16rpx;
  margin: 66rpx 28rpx 0 558rpx;
}
.list-items_1-2 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  position: relative;
  width: 710rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  flex-direction: row;
  display: flex;
}
.text_2-2 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 30rpx 0 0 40rpx;
}
.thumbnail_4-2 {
  width: 28rpx;
  height: 16rpx;
  margin: 66rpx 28rpx 0 558rpx;
}
.list-items_1-3 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  position: relative;
  width: 710rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  flex-direction: row;
  display: flex;
}
.text_2-3 {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 30rpx 0 0 40rpx;
}
.thumbnail_4-3 {
  width: 28rpx;
  height: 16rpx;
  margin: 66rpx 28rpx 0 558rpx;
}
.text-wrapper_1 {
  background-color: rgba(229,230,235,1.000000);
  border-radius: 16rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  width: 690rpx;
  margin: 720rpx 0 0 30rpx;
}
.text_4 {
  width: 128rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 32rpx 0 0 282rpx;
}
.group_1 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 130rpx 0 14rpx 232rpx;
}

/* 头像按钮样式 */
.avatar-button {
  background: transparent;
  border: none;
  padding: 0;
  position: absolute;
  right: 28rpx;
  top: 14rpx;
  width: 72rpx;
  height: 72rpx;
  overflow: hidden;
}

.avatar-button::after {
  border: none;
}

.label_1-0-1 {
  position: static;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

/* 昵称输入框样式 */
.nickname-input {
  position: absolute;
  right: 28rpx;
  top: 30rpx;
  width: 200rpx;
  height: 40rpx;
  text-align: right;
  font-size: 28rpx;
  color: rgba(0,0,0,1.000000);
  background: transparent;
  border: none;
  outline: none;
}

.nickname-input::placeholder {
  color: rgba(0,0,0,0.4);
}

/* 点击效果 */
.list-items_1-2:active,
.list-items_1-3:active {
  background-color: rgba(240, 240, 240, 1.000000);
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/* 退出登录按钮样式优化 */
.text-wrapper_1:active {
  background-color: rgba(200, 200, 200, 1.000000);
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: #333;
  font-size: 32rpx;
}