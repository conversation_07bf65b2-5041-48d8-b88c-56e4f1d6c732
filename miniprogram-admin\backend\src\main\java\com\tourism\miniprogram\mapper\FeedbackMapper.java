package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Feedback;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 意见反馈Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback> {

    /**
     * 根据用户ID获取反馈列表
     *
     * @param userId 用户ID
     * @return 反馈列表
     */
    @Select("SELECT * FROM feedback WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Feedback> selectFeedbacksByUserId(@Param("userId") Integer userId);

    /**
     * 根据状态获取反馈列表
     *
     * @param status 状态
     * @return 反馈列表
     */
    @Select("SELECT * FROM feedback WHERE status = #{status} ORDER BY created_at DESC")
    List<Feedback> selectFeedbacksByStatus(@Param("status") String status);

    /**
     * 获取待处理的反馈数量
     *
     * @return 待处理数量
     */
    @Select("SELECT COUNT(*) FROM feedback WHERE status = 'pending'")
    Integer countPendingFeedbacks();

    /**
     * 根据关键词搜索反馈
     *
     * @param keyword 关键词
     * @return 反馈列表
     */
    @Select("SELECT * FROM feedback WHERE name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR phone LIKE CONCAT('%', #{keyword}, '%') " +
            "OR content LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY created_at DESC")
    List<Feedback> searchFeedbacks(@Param("keyword") String keyword);
}
