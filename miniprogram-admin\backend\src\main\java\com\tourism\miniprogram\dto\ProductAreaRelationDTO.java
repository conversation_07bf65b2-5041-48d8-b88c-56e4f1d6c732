package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品区域关系DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "ProductAreaRelationDTO对象", description = "产品区域关系详细信息")
public class ProductAreaRelationDTO {

    @ApiModelProperty(value = "关系ID")
    private Integer relationId;

    @ApiModelProperty(value = "产品ID")
    private Integer productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "区域ID")
    private Integer areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
}
