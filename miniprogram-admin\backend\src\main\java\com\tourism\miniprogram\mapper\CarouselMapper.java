package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Carousel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 轮播图Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface CarouselMapper extends BaseMapper<Carousel> {

    /**
     * 根据条件获取轮播图列表
     *
     * @param provinceId 省份ID
     * @return 轮播图列表
     */
    List<Carousel> selectCarouselsByCondition(@Param("provinceId") Integer provinceId);
}
