package com.tourism.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件工具类测试
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FileUtilsTest {

    @Test
    public void testGetFileTypeForImages() {
        assertEquals("image", FileUtils.getFileType("test.jpg"));
        assertEquals("image", FileUtils.getFileType("test.jpeg"));
        assertEquals("image", FileUtils.getFileType("test.png"));
        assertEquals("image", FileUtils.getFileType("test.gif"));
        assertEquals("image", FileUtils.getFileType("test.bmp"));
        assertEquals("image", FileUtils.getFileType("test.webp"));
    }

    @Test
    public void testGetFileTypeForVideos() {
        assertEquals("video", FileUtils.getFileType("test.mp4"));
        assertEquals("video", FileUtils.getFileType("test.avi"));
        assertEquals("video", FileUtils.getFileType("test.mov"));
        assertEquals("video", FileUtils.getFileType("test.wmv"));
        assertEquals("video", FileUtils.getFileType("test.flv"));
        assertEquals("video", FileUtils.getFileType("test.3gp"));
        assertEquals("video", FileUtils.getFileType("test.mkv"));
    }

    @Test
    public void testGetFileTypeForAudios() {
        assertEquals("audio", FileUtils.getFileType("test.mp3"));
        assertEquals("audio", FileUtils.getFileType("test.wav"));
        assertEquals("audio", FileUtils.getFileType("test.flac"));
        assertEquals("audio", FileUtils.getFileType("test.aac"));
        assertEquals("audio", FileUtils.getFileType("test.ogg"));
        assertEquals("audio", FileUtils.getFileType("test.wma"));
        assertEquals("audio", FileUtils.getFileType("test.m4a"));
    }

    @Test
    public void testGetFileTypeForOthers() {
        assertEquals("other", FileUtils.getFileType("test.txt"));
        assertEquals("other", FileUtils.getFileType("test.doc"));
        assertEquals("other", FileUtils.getFileType("test.pdf"));
        assertEquals("other", FileUtils.getFileType("test.zip"));
    }

    @Test
    public void testGetFileTypeCaseInsensitive() {
        assertEquals("audio", FileUtils.getFileType("test.MP3"));
        assertEquals("audio", FileUtils.getFileType("test.WAV"));
        assertEquals("image", FileUtils.getFileType("test.JPG"));
        assertEquals("video", FileUtils.getFileType("test.MP4"));
    }

    @Test
    public void testGetFileExtension() {
        assertEquals("jpg", FileUtils.getFileExtension("test.jpg"));
        assertEquals("mp3", FileUtils.getFileExtension("test.mp3"));
        assertEquals("", FileUtils.getFileExtension("test"));
        assertEquals("", FileUtils.getFileExtension(""));
        assertEquals("", FileUtils.getFileExtension(null));
    }
}
