import request from '@/utils/request'

/**
 * 分页获取讲解音频列表
 * @param {Object} params 查询参数
 */
export function getExplanationAudioPage(params) {
  return request({
    url: '/explanation-audios/page',
    method: 'get',
    params
  })
}

/**
 * 获取讲解音频详情
 * @param {number} id 音频ID
 */
export function getExplanationAudioById(id) {
  return request({
    url: `/explanation-audios/${id}`,
    method: 'get'
  })
}

/**
 * 根据讲解点ID获取音频列表
 * @param {number} pointId 讲解点ID
 */
export function getAudiosByPointId(pointId) {
  return request({
    url: `/explanation-audios/point/${pointId}`,
    method: 'get'
  })
}

/**
 * 创建讲解音频
 * @param {Object} data 音频数据
 */
export function createExplanationAudio(data) {
  return request({
    url: '/explanation-audios',
    method: 'post',
    data
  })
}

/**
 * 更新讲解音频
 * @param {number} id 音频ID
 * @param {Object} data 音频数据
 */
export function updateExplanationAudio(id, data) {
  return request({
    url: `/explanation-audios/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除讲解音频
 * @param {number} id 音频ID
 */
export function deleteExplanationAudio(id) {
  return request({
    url: `/explanation-audios/${id}`,
    method: 'delete'
  })
}
