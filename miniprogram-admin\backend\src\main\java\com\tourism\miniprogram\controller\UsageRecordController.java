package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.UsageRecord;
import com.tourism.miniprogram.service.UsageRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 使用记录控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/usage-records")
@Api(tags = "使用记录管理")
public class UsageRecordController {

    @Autowired
    private UsageRecordService usageRecordService;

    /**
     * 分页获取使用记录列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取使用记录列表", notes = "分页获取使用记录列表，支持按用户ID、景区ID、卡券ID筛选")
    public Result<IPage<UsageRecord>> getUsageRecordPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "景区ID") @RequestParam(required = false) Integer scenicId,
            @ApiParam(value = "卡券ID") @RequestParam(required = false) Integer couponId,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime) {
        try {
            Page<UsageRecord> page = new Page<>(current, size);
            QueryWrapper<UsageRecord> queryWrapper = new QueryWrapper<>();

            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (scenicId != null) {
                queryWrapper.eq("scenic_id", scenicId);
            }
            if (couponId != null) {
                queryWrapper.eq("coupon_id", couponId);
            }
            if (startTime != null) {
                queryWrapper.ge("used_at", startTime);
            }
            if (endTime != null) {
                queryWrapper.le("used_at", endTime);
            }
            queryWrapper.orderByDesc("id");

            IPage<UsageRecord> recordPage = usageRecordService.page(page, queryWrapper);
            return Result.success(recordPage);
        } catch (Exception e) {
            log.error("分页获取使用记录列表失败", e);
            return Result.error("获取使用记录列表失败");
        }
    }

    /**
     * 获取使用记录列表
     */
    @GetMapping
    @ApiOperation(value = "获取使用记录列表", notes = "获取所有使用记录列表")
    public Result<List<UsageRecord>> getUsageRecords() {
        try {
            QueryWrapper<UsageRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<UsageRecord> records = usageRecordService.list(queryWrapper);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取使用记录列表失败", e);
            return Result.error("获取使用记录列表失败");
        }
    }

    /**
     * 获取使用记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取使用记录详情", notes = "根据ID获取使用记录详情")
    public Result<UsageRecord> getUsageRecordById(@ApiParam(value = "记录ID", required = true) @PathVariable Integer id) {
        try {
            UsageRecord record = usageRecordService.getById(id);
            if (record == null) {
                return Result.error(404, "使用记录不存在");
            }
            return Result.success(record);
        } catch (Exception e) {
            log.error("获取使用记录详情失败，id: {}", id, e);
            return Result.error("获取使用记录详情失败");
        }
    }

    /**
     * 根据用户ID获取使用记录
     */
    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID获取使用记录", notes = "根据用户ID获取该用户的所有使用记录")
    public Result<List<UsageRecord>> getUsageRecordsByUserId(@ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            QueryWrapper<UsageRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.orderByDesc("used_at");
            List<UsageRecord> records = usageRecordService.list(queryWrapper);
            return Result.success(records);
        } catch (Exception e) {
            log.error("根据用户ID获取使用记录失败，userId: {}", userId, e);
            return Result.error("获取使用记录失败");
        }
    }

    /**
     * 创建使用记录
     */
    @PostMapping
    @ApiOperation(value = "创建使用记录", notes = "创建新的使用记录")
    public Result<UsageRecord> createUsageRecord(@RequestBody @Valid UsageRecord record) {
        try {
            if (record.getUsedAt() == null) {
                record.setUsedAt(LocalDateTime.now());
            }
            boolean success = usageRecordService.save(record);
            if (success) {
                // 重新查询获取完整的使用记录信息（包含自动生成的ID和时间戳）
                UsageRecord createdRecord = usageRecordService.getById(record.getId());
                return Result.success(createdRecord);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建使用记录失败", e);
            return Result.error("创建使用记录失败");
        }
    }

    /**
     * 更新使用记录
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新使用记录", notes = "根据ID更新使用记录信息")
    public Result<String> updateUsageRecord(
            @ApiParam(value = "记录ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid UsageRecord record) {
        try {
            record.setId(id);
            boolean success = usageRecordService.updateById(record);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新使用记录失败，id: {}", id, e);
            return Result.error("更新使用记录失败");
        }
    }

    /**
     * 删除使用记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除使用记录", notes = "根据ID删除使用记录")
    public Result<String> deleteUsageRecord(@ApiParam(value = "记录ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = usageRecordService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除使用记录失败，id: {}", id, e);
            return Result.error("删除使用记录失败");
        }
    }
}
