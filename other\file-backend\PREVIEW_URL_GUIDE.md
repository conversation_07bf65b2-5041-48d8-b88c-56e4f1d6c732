# 文件预览URL使用指南

## ✅ 最新解决方案：ResponseHeaderOverrides

### 核心原理

使用腾讯云COS SDK的 `ResponseHeaderOverrides` 类来强制设置HTTP响应头，确保浏览器按照指定方式处理文件。

### 关键实现

```java
// ⭐️ 预览URL：强制浏览器预览
ResponseHeaderOverrides previewHeaders = new ResponseHeaderOverrides();
previewHeaders.setContentDisposition("inline"); // 内联预览
request.setResponseHeaders(previewHeaders);

// ⭐️ 下载URL：强制浏览器下载
ResponseHeaderOverrides downloadHeaders = new ResponseHeaderOverrides();
downloadHeaders.setContentDisposition("attachment"); // 附件下载
request.setResponseHeaders(downloadHeaders);
```

## 修复内容

### 1. 预览URL生成 (`generatePreviewUrl`)
- **CDN优先**: 配置了CDN域名时优先使用CDN URL
- **预签名URL**: 使用 `ResponseHeaderOverrides` 设置 `Content-Disposition: inline`
- **图片优化**: 对腾讯云CDN添加图片处理参数

### 2. 下载URL生成 (`generateDownloadUrlInternal`)
- **强制下载**: 使用 `ResponseHeaderOverrides` 设置 `Content-Disposition: attachment`
- **文件名保持**: 保持原始文件名用于下载

### 3. 上传时元数据设置
- 设置 `Content-Disposition: inline` 作为默认行为
- 正确设置 `Content-Type` 确保浏览器识别
- 添加缓存控制提高性能

## 使用方法

### 1. 配置CDN域名
```yaml
tencent:
  cos:
    cdn-domain: https://www.gobaweb.com  # 您的CDN域名
```

### 2. 上传文件测试
```bash
curl -X POST \
  http://localhost:8080/api/test/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test-image.jpg'
```

### 3. 响应示例
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "abc123",
    "originalFileName": "test-image.jpg",
    "fileType": "image",
    "fileSize": 102400,
    "downloadUrl": "https://travel-1315014578.cos.ap-guangzhou.myqcloud.com/tourism/files/image/20231201_123456_abc123.jpg?response-content-disposition=attachment",
    "previewUrl": "https://www.gobaweb.com/tourism/files/image/20231201_123456_abc123.jpg",
    "cdnUrl": "https://www.gobaweb.com/tourism/files/image/20231201_123456_abc123.jpg",
    "uploadTime": "2023-12-01 12:34:56",
    "urlExpireTime": 1576800000
  }
}
```

### 4. URL行为对比

| URL类型 | 生成方式 | 浏览器行为 | 适用场景 |
|---------|----------|------------|----------|
| `downloadUrl` | 预签名URL + `attachment` | 🔽 **强制下载** | 文件下载功能 |
| `previewUrl` | CDN URL 或 预签名URL + `inline` | 👁️ **直接预览** | 图片/视频展示 |
| `cdnUrl` | CDN域名 + 文件路径 | 📱 **CDN加速** | 高性能访问 |

## 测试方法

### 1. 浏览器测试
1. 上传一个图片文件
2. 复制响应中的 `previewUrl`
3. 在浏览器中打开该URL
4. 应该直接显示图片，而不是下载

### 2. 对比测试
1. 同时打开 `downloadUrl` 和 `previewUrl`
2. `downloadUrl` 应该触发下载
3. `previewUrl` 应该直接显示

## 配置建议

### 1. CDN配置
```yaml
tencent:
  cos:
    cdn-domain: https://your-cdn-domain.com
```

### 2. 腾讯云COS设置
- 确保存储桶允许公共读取（如果需要）
- 配置CORS允许跨域访问
- 启用CDN加速

### 3. 浏览器兼容性
- 现代浏览器都支持inline预览
- 移动端浏览器可能有不同行为
- 建议提供降级方案

## 故障排除

### 1. 仍然触发下载
- 检查CDN配置是否正确
- 确认文件Content-Type设置
- 查看浏览器开发者工具中的响应头

### 2. 预览失败
- 检查文件格式是否支持预览
- 确认URL参数是否正确添加
- 查看网络请求是否成功

### 3. CDN缓存问题
- 清除CDN缓存
- 等待缓存更新
- 使用版本参数强制刷新
