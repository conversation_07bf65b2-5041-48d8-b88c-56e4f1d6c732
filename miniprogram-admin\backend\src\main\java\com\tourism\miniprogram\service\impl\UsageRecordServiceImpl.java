package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.UsageRecord;
import com.tourism.miniprogram.mapper.UsageRecordMapper;
import com.tourism.miniprogram.service.UsageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 使用记录服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class UsageRecordServiceImpl extends ServiceImpl<UsageRecordMapper, UsageRecord> implements UsageRecordService {

    @Override
    public List<UsageRecord> getUsageRecordsByUserId(Integer userId) {
        try {
            return baseMapper.selectUsageRecordsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取使用记录失败，userId: {}", userId, e);
            throw new RuntimeException("根据用户ID获取使用记录失败");
        }
    }

    @Override
    public List<UsageRecord> getUsageRecordsByScenicId(String scenicId) {
        try {
            return baseMapper.selectUsageRecordsByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取使用记录失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取使用记录失败");
        }
    }

    @Override
    public List<UsageRecord> getUsageRecordsByCouponId(Integer couponId) {
        try {
            return baseMapper.selectUsageRecordsByCouponId(couponId);
        } catch (Exception e) {
            log.error("根据门票ID获取使用记录失败，couponId: {}", couponId, e);
            throw new RuntimeException("根据门票ID获取使用记录失败");
        }
    }

    @Override
    public boolean createUsageRecord(Integer couponId, Integer userId, String scenicId) {
        try {
            UsageRecord usageRecord = new UsageRecord();
            usageRecord.setCouponId(couponId);
            usageRecord.setUserId(userId);
            usageRecord.setScenicId(scenicId);
            usageRecord.setUsedAt(LocalDateTime.now());
            usageRecord.setCreatedAt(LocalDateTime.now());

            boolean success = save(usageRecord);
            if (success) {
                log.info("创建使用记录成功，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId);
                return true;
            } else {
                log.error("创建使用记录失败");
                return false;
            }
        } catch (Exception e) {
            log.error("创建使用记录失败，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId, e);
            throw new RuntimeException("创建使用记录失败");
        }
    }
}
