package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ExplanationArea;
import com.tourism.miniprogram.service.ExplanationAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 讲解区域控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/explanation-areas")
@Api(tags = "讲解区域管理")
public class ExplanationAreaController {

    @Autowired
    private ExplanationAreaService explanationAreaService;

    /**
     * 分页获取讲解区域列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲解区域列表", notes = "分页获取讲解区域列表，支持按区域名称筛选")
    public Result<IPage<ExplanationArea>> getExplanationAreaPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "区域名称") @RequestParam(required = false) String areaName) {
        try {
            Page<ExplanationArea> page = new Page<>(current, size);
            QueryWrapper<ExplanationArea> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(areaName)) {
                queryWrapper.like("area_name", areaName);
            }
            queryWrapper.orderByAsc("sort_order").orderByDesc("area_id");

            IPage<ExplanationArea> areaPage = explanationAreaService.page(page, queryWrapper);
            return Result.success(areaPage);
        } catch (Exception e) {
            log.error("分页获取讲解区域列表失败", e);
            return Result.error("获取讲解区域列表失败");
        }
    }

    /**
     * 获取讲解区域详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲解区域详情", notes = "根据ID获取讲解区域详细信息")
    public Result<ExplanationArea> getExplanationAreaById(@ApiParam(value = "区域ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationArea area = explanationAreaService.getById(id);
            if (area == null) {
                return Result.error(404, "讲解区域不存在");
            }
            return Result.success(area);
        } catch (Exception e) {
            log.error("获取讲解区域详情失败，id: {}", id, e);
            return Result.error("获取讲解区域详情失败");
        }
    }

    /**
     * 根据产品ID获取区域列表
     * @deprecated 请使用 /relations/product-area/product/{productId} 接口
     */
    @GetMapping("/product/{productId}")
    @ApiOperation(value = "根据产品ID获取区域列表", notes = "根据产品ID获取该产品的所有区域。建议使用关系管理API：/relations/product-area/product/{productId}")
    @Deprecated
    public Result<String> getAreasByProductId(@ApiParam(value = "产品ID", required = true) @PathVariable Integer productId) {
        return Result.error("此接口已废弃，请使用关系管理API：/relations/product-area/product/" + productId);
    }

    /**
     * 创建讲解区域
     */
    @PostMapping
    @ApiOperation(value = "创建讲解区域", notes = "创建新的讲解区域")
    public Result<ExplanationArea> createExplanationArea(@RequestBody @Valid ExplanationArea area) {
        try {
            boolean success = explanationAreaService.save(area);
            if (success) {
                // 重新查询获取完整的讲解区域信息（包含自动生成的ID和时间戳）
                ExplanationArea createdArea = explanationAreaService.getById(area.getAreaId());
                return Result.success(createdArea);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲解区域失败", e);
            return Result.error("创建讲解区域失败");
        }
    }

    /**
     * 更新讲解区域
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲解区域", notes = "更新讲解区域信息")
    public Result<String> updateExplanationArea(
            @ApiParam(value = "区域ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid ExplanationArea area) {
        try {
            ExplanationArea existArea = explanationAreaService.getById(id);
            if (existArea == null) {
                return Result.error(404, "讲解区域不存在");
            }

            area.setAreaId(id);
            boolean success = explanationAreaService.updateById(area);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲解区域失败，id: {}", id, e);
            return Result.error("更新讲解区域失败");
        }
    }

    /**
     * 删除讲解区域
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲解区域", notes = "删除讲解区域")
    public Result<String> deleteExplanationArea(@ApiParam(value = "区域ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationArea area = explanationAreaService.getById(id);
            if (area == null) {
                return Result.error(404, "讲解区域不存在");
            }

            boolean success = explanationAreaService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲解区域失败，id: {}", id, e);
            return Result.error("删除讲解区域失败");
        }
    }
}
