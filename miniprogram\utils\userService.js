const config = require('./config');

// 用户服务模块
class UserService {
  constructor() {
    this.baseUrl = config.api.baseUrl;
    this.tokenKey = 'user_token';
    this.userInfoKey = 'user_info';
  }

  // 检查用户登录状态
  checkLoginStatus() {
    try {
      const token = wx.getStorageSync(this.tokenKey);
      const userInfo = wx.getStorageSync(this.userInfoKey);

      console.log('检查登录状态 - token:', token ? '存在' : '不存在');
      console.log('检查登录状态 - userInfo:', userInfo);

      const isLoggedIn = !!(token && userInfo && (userInfo.id || userInfo.userId));

      // 同步到全局数据
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.userInfo = isLoggedIn ? userInfo : null;
      }

      return {
        isLoggedIn,
        token,
        userInfo
      };
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return {
        isLoggedIn: false,
        token: null,
        userInfo: null
      };
    }
  }

  // 微信登录
  async wechatLogin() {
    try {
      // 1. 获取微信登录code
      const loginResult = await this.getWechatCode();

      // 2. 调用后端登录接口
      const loginResponse = await this.callLoginAPI(loginResult.code);

      console.log('后端登录接口响应:', loginResponse);

      // 3. 保存登录信息 - 直接传递data字段
      await this.saveLoginInfo(loginResponse.data);

      return {
        success: true,
        data: loginResponse.data
      };
    } catch (error) {
      console.error('微信登录失败:', error);

      return {
        success: false,
        error: error.message || '登录失败'
      };
    }
  }

  // 模拟登录（仅用于开发调试）
  async mockLogin() {
    try {
      console.log('执行模拟登录');

      const mockLoginData = {
        token: 'mock_token_' + Date.now(),
        user_info: {
          id: 'mock_user_' + Date.now(),
          userId: 'mock_user_' + Date.now(),
          openid: 'mock_openid_' + Date.now(),
          nickName: '测试用户',
          avatarUrl: 'https://via.placeholder.com/100',
          phone: '',
          createdAt: new Date().toISOString()
        }
      };

      await this.saveLoginInfo(mockLoginData);

      return {
        success: true,
        data: mockLoginData
      };
    } catch (error) {
      console.error('模拟登录失败:', error);
      return {
        success: false,
        error: error.message || '模拟登录失败'
      };
    }
  }

  // 获取微信登录code
  getWechatCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            resolve(res);
          } else {
            reject(new Error('获取微信登录code失败'));
          }
        },
        fail: (err) => {
          reject(new Error('微信登录失败: ' + err.errMsg));
        }
      });
    });
  }

  // 调用后端登录接口
  callLoginAPI(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/api/auth/wechat/login`,
        method: 'POST',
        data: {
          code: code
        },
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.code === 200) {
            resolve(res.data);
          } else {
            reject(new Error(res.data.message || '登录接口调用失败'));
          }
        },
        fail: (err) => {
          reject(new Error('网络请求失败: ' + err.errMsg));
        }
      });
    });
  }

  // 保存登录信息
  async saveLoginInfo(loginData) {
    try {
      console.log('保存登录信息:', loginData);

      // 提取token
      const token = loginData.token || loginData.access_token;
      if (!token) {
        throw new Error('登录响应中缺少token');
      }

      // 提取用户信息 - 支持多种数据结构
      let userInfo = null;

      // 情况1: 用户信息嵌套在特定字段中
      if (loginData.user_info) {
        userInfo = loginData.user_info;
      } else if (loginData.userInfo) {
        userInfo = loginData.userInfo;
      } else if (loginData.user) {
        userInfo = loginData.user;
      }
      // 情况2: 用户信息直接在根级别（当前情况）
      else if (loginData.userId || loginData.nickname || loginData.avatarUrl) {
        // 从根级别提取用户信息
        userInfo = {
          id: loginData.userId || loginData.id,
          userId: loginData.userId || loginData.id,
          nickname: loginData.nickname,
          avatarUrl: loginData.avatarUrl,
          phone: loginData.phone,
          region: loginData.region,
          isNewUser: loginData.isNewUser,
          openid: loginData.openid,
          unionid: loginData.unionid
        };
      }

      if (!userInfo) {
        throw new Error('登录响应中缺少用户信息');
      }

      // 确保用户信息包含必要的ID字段
      if (!userInfo.id && !userInfo.userId) {
        console.warn('用户信息中缺少ID字段，尝试从其他字段获取');
        if (loginData.userId) {
          userInfo.id = loginData.userId;
          userInfo.userId = loginData.userId;
        } else if (userInfo.openid) {
          userInfo.id = userInfo.openid;
        } else if (userInfo.unionid) {
          userInfo.id = userInfo.unionid;
        } else {
          // 生成临时ID
          userInfo.id = 'temp_' + Date.now();
        }
      }

      wx.setStorageSync(this.tokenKey, token);
      wx.setStorageSync(this.userInfoKey, userInfo);

      // 同步到全局数据
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.userInfo = userInfo;
      }

      console.log('登录信息保存成功 - token:', token ? '已保存' : '未保存');
      console.log('登录信息保存成功 - userInfo:', userInfo);

    } catch (error) {
      console.error('保存登录信息失败:', error);
      throw new Error('保存登录信息失败: ' + error.message);
    }
  }

  // 获取手机号授权
  async getPhoneNumber(e) {
    return new Promise((resolve, reject) => {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        // 将加密数据发送到后端解密
        this.decryptPhoneNumber(e.detail.encryptedData, e.detail.iv)
          .then(resolve)
          .catch(reject);
      } else {
        reject(new Error('用户拒绝授权手机号'));
      }
    });
  }

  // 解密手机号（兼容新旧版本）
  async decryptPhoneNumber(params) {
    try {
      // 新版本参数格式
      if (params.code && params.encryptedData && params.iv) {
        return await this.decryptPhoneByServer(params);
      }
      // 旧版本参数格式（向后兼容）
      else if (typeof params === 'string') {
        const encryptedData = params;
        const iv = arguments[1];
        return await this.decryptPhoneByServer({ encryptedData, iv });
      }
      else {
        throw new Error('参数格式错误');
      }
    } catch (error) {
      console.error('解密手机号失败:', error);
      throw error;
    }
  }

  // 通过服务器解密手机号
  async decryptPhoneByServer(params) {
    const { token } = this.checkLoginStatus();
    const { encryptedData, iv } = params;

    return new Promise((resolve, reject) => {
      // 先获取新的登录code，因为手机号解密需要新的code
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            // 使用新的登录code进行手机号解密
            wx.request({
              url: `${this.baseUrl}/api/auth/wechat/decrypt-phone`,
              method: 'POST',
              data: {
                code: loginRes.code,  // 使用新获取的登录code
                encryptedData: encryptedData,  // 使用驼峰命名
                iv: iv
              },
              header: {
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : undefined
              },
              success: (res) => {
                console.log('手机号解密API响应:', res);
                if (res.statusCode === 200 && res.data.code === 200) {
                  resolve(res.data.data);
                } else {
                  console.error('API返回错误:', res.data);
                  reject(new Error(res.data.message || '手机号解密失败'));
                }
              },
              fail: (err) => {
                console.error('手机号解密API请求失败:', err);
                reject(new Error('网络请求失败: ' + err.errMsg));
              }
            });
          } else {
            reject(new Error('获取登录code失败'));
          }
        },
        fail: (err) => {
          console.error('wx.login失败:', err);
          reject(new Error('获取登录code失败: ' + err.errMsg));
        }
      });
    });
  }

  // 获取用户信息
  async getUserInfo(userId) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }

      const { token } = this.checkLoginStatus();
      if (!token) {
        throw new Error('用户未登录');
      }

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/api/user/${userId}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.code === 200) {
              resolve(res.data.data);
            } else if (res.statusCode === 401) {
              reject(new Error('用户未授权，请重新登录'));
            } else if (res.statusCode === 404) {
              reject(new Error('用户不存在'));
            } else {
              reject(new Error(res.data.message || '获取用户信息失败'));
            }
          },
          fail: (err) => {
            reject(new Error('网络请求失败: ' + err.errMsg));
          }
        });
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // 获取本地存储的用户信息
  getLocalUserInfo() {
    try {
      return wx.getStorageSync(this.userInfoKey);
    } catch (error) {
      console.error('获取本地用户信息失败:', error);
      return null;
    }
  }

  // 获取用户信息（简化版本，用于小程序页面）
  async getUserInfo() {
    try {
      // 先尝试获取本地用户信息
      const localUserInfo = this.getLocalUserInfo();
      if (localUserInfo && localUserInfo.id) {
        return localUserInfo;
      }

      // 如果没有本地用户信息，检查登录状态
      const loginStatus = this.checkLoginStatus();
      if (loginStatus.isLoggedIn && loginStatus.userInfo) {
        return loginStatus.userInfo;
      }

      // 如果都没有，返回默认用户信息
      return {
        id: 1,
        nickname: '用户',
        phone: '',
        avatarUrl: ''
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        id: 1,
        nickname: '用户',
        phone: '',
        avatarUrl: ''
      };
    }
  }

  // 检查用户是否需要授权
  checkUserAuth() {
    const userInfo = this.getLocalUserInfo();
    return !!(userInfo && userInfo.avatarUrl && userInfo.nickName);
  }

  // 检查是否需要显示授权弹窗
  needShowAuthModal() {
    const userInfo = this.getLocalUserInfo();
    // 当用户头像或昵称为空时需要显示授权弹窗
    return !(userInfo && userInfo.avatarUrl && userInfo.nickName);
  }

  // 更新用户信息
  async updateUserInfo(userInfo) {
    console.log(userInfo)
    const { token } = this.checkLoginStatus();
    
    return new Promise((resolve, reject) => {
      console.log("11111111"+userInfo)
      wx.request({
        url: `${this.baseUrl}/api/user/${userInfo.id}`,
        method: 'PUT',
        data: userInfo,
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.code === 200) {
            // 更新本地存储的用户信息
            const currentUserInfo = wx.getStorageSync(this.userInfoKey);
            const updatedUserInfo = { ...currentUserInfo, ...userInfo };
            wx.setStorageSync(this.userInfoKey, updatedUserInfo);

            // 同步到全局数据
            const app = getApp();
            if (app && app.globalData) {
              app.globalData.userInfo = updatedUserInfo;
            }

            console.log('用户信息更新成功:', updatedUserInfo);
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '更新用户信息失败'));
          }
        },
        fail: (err) => {
          reject(new Error('网络请求失败: ' + err.errMsg));
        }
      });
    });
  }

  // 退出登录
  logout() {
    try {
      wx.removeStorageSync(this.tokenKey);
      wx.removeStorageSync(this.userInfoKey);

      // 清除全局数据
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.userInfo = null;
      }

      console.log('用户已退出登录');
      return true;
    } catch (error) {
      console.error('退出登录失败:', error);
      return false;
    }
  }

  // 显示登录确认弹窗
  showLoginConfirm() {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '登录提示',
        content: '您还未登录，是否进行微信一键授权登录？',
        confirmText: '立即登录',
        cancelText: '暂不登录',
        success: (res) => {
          if (res.confirm) {
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  // 显示授权提示弹窗
  showAuthConfirm() {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '授权提示',
        content: '为了更好地激活相关服务，请授权手机号和头像昵称',
        confirmText: '立即授权',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
}

// 创建单例实例
const userService = new UserService();

module.exports = userService;
