<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.User">
        <id column="id" property="id" />
        <result column="openid" property="openid" />
        <result column="unionid" property="unionid" />
        <result column="nickname" property="nickname" />
        <result column="avatar" property="avatar" />
        <result column="phone" property="phone" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, openid, unionid, nickname, avatar, phone, province, city, status, created_at, updated_at
    </sql>

    <!-- 根据openid查询用户 -->
    <select id="selectByOpenid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM users
        WHERE openid = #{openid} AND status = 1
    </select>

    <!-- 更新用户信息 -->
    <update id="updateUserInfo">
        UPDATE users
        <set>
            <if test="nickname != null and nickname != ''">
                nickname = #{nickname},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
        </set>
        WHERE id = #{id} AND status = 1
    </update>

</mapper>
