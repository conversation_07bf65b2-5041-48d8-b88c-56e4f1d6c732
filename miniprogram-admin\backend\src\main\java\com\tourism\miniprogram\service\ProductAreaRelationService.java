package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.dto.ProductAreaRelationDTO;
import com.tourism.miniprogram.entity.ProductAreaRelation;

import java.util.List;

/**
 * 产品区域关系服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
public interface ProductAreaRelationService extends IService<ProductAreaRelation> {

    /**
     * 根据产品ID获取区域关系列表
     *
     * @param productId 产品ID
     * @return 区域关系列表
     */
    List<ProductAreaRelation> getByProductId(Integer productId);

    /**
     * 根据产品ID获取区域关系详细列表
     *
     * @param productId 产品ID
     * @return 区域关系详细列表
     */
    List<ProductAreaRelationDTO> getByProductIdWithDetails(Integer productId);

    /**
     * 根据区域ID获取产品关系列表
     *
     * @param areaId 区域ID
     * @return 产品关系列表
     */
    List<ProductAreaRelation> getByAreaId(Integer areaId);

    /**
     * 添加产品区域关系
     *
     * @param productId 产品ID
     * @param areaId 区域ID
     * @param sortOrder 排序
     * @return 是否成功
     */
    boolean addRelation(Integer productId, Integer areaId, Integer sortOrder);

    /**
     * 删除产品区域关系
     *
     * @param productId 产品ID
     * @param areaId 区域ID
     * @return 是否成功
     */
    boolean removeRelation(Integer productId, Integer areaId);

    /**
     * 批量添加关系
     *
     * @param relations 关系列表
     * @return 是否成功
     */
    boolean batchAddRelations(List<ProductAreaRelation> relations);

    /**
     * 根据产品ID删除所有关系
     *
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean removeByProductId(Integer productId);

    /**
     * 根据区域ID删除所有关系
     *
     * @param areaId 区域ID
     * @return 是否成功
     */
    boolean removeByAreaId(Integer areaId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 是否成功
     */
    boolean updateSortOrder(Integer relationId, Integer sortOrder);

    /**
     * 批量更新排序
     *
     * @param relationIds 关系ID列表
     * @param sortOrders 排序值列表
     * @return 是否成功
     */
    boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders);
}
