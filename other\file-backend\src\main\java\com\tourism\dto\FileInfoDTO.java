package com.tourism.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件信息DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "文件信息")
public class FileInfoDTO {

    @ApiModelProperty(value = "文件ID", example = "uuid-123456")
    private String fileId;

    @ApiModelProperty(value = "原始文件名", example = "景区图片.jpg")
    private String originalFileName;

    @ApiModelProperty(value = "存储文件名", example = "20231201_123456_abc123.jpg")
    private String storedFileName;

    @ApiModelProperty(value = "文件类型", example = "image")
    private String fileType;

    @ApiModelProperty(value = "文件扩展名", example = "jpg")
    private String fileExtension;

    @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @ApiModelProperty(value = "文件下载URL", example = "https://example.cos.ap-beijing.myqcloud.com/tourism/files/20231201_123456_abc123.jpg")
    private String downloadUrl;

    @ApiModelProperty(value = "CDN加速URL", example = "https://cdn.example.com/tourism/files/20231201_123456_abc123.jpg")
    private String cdnUrl;

    @ApiModelProperty(value = "上传时间", example = "2023-12-01 12:34:56")
    private String uploadTime;

    @ApiModelProperty(value = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5;

    @ApiModelProperty(value = "文件存储路径", example = "tourism/files/20231201_123456_abc123.jpg")
    private String filePath;
}
