<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>关系管理</h2>
      <p>管理产品、区域、讲解点和音频之间的多对多关系</p>
    </div>

    <!-- 关系类型选择 -->
    <el-card class="filter-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="产品-区域关系" name="product-area">
          <ProductAreaRelation />
        </el-tab-pane>
        <el-tab-pane label="区域-讲解点关系" name="area-point">
          <AreaPointRelation />
        </el-tab-pane>
        <el-tab-pane label="讲解点-音频关系" name="point-audio">
          <PointAudioRelation />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ProductAreaRelation from './components/ProductAreaRelation.vue'
import AreaPointRelation from './components/AreaPointRelation.vue'
import PointAudioRelation from './components/PointAudioRelation.vue'

// 响应式数据
const activeTab = ref('product-area')

// 切换标签页
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.filter-card {
  margin-bottom: 20px;
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
  
  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 500;
  }
  
  :deep(.el-tabs__content) {
    padding: 0;
  }
}
</style>
