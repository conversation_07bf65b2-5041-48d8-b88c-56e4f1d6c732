# 真实API接入指南

## 1. 概述

本指南说明如何在旅游讲解小程序中接入真实的API接口。我们已经移除了所有模拟数据和多环境配置，直接使用真实API。

## 2. 项目结构

```
utils/
├── config.js              # 简化的配置文件
├── httpService.js          # HTTP请求基础服务
├── provinceService.js      # 省份服务
├── cityService.js          # 城市服务
├── scenicService.js        # 景区服务
├── carouselService.js      # 轮播图服务
├── userService.js          # 用户服务
└── apiService.js           # API统一入口
```

## 3. 配置API域名

### 3.1 修改配置文件

打开 `utils/config.js`，修改API域名：

```javascript
const config = {
  // API配置
  api: {
    baseUrl: 'https://your-api-domain.com', // 替换为您的实际API域名
    timeout: 10000
  }
};
```

### 3.2 配置示例

```javascript
// 生产环境配置示例
const config = {
  api: {
    baseUrl: 'https://api.tourism-guide.com',
    timeout: 8000
  }
};
```

## 4. 服务模块说明

### 4.1 省份服务 (provinceService.js)

**主要功能：**
- 获取省份列表
- 根据ID获取省份信息
- 搜索省份
- 获取热门省份

**使用方法：**
```javascript
const provinceService = require('../../utils/provinceService');

// 获取省份列表
const provinces = await provinceService.getProvinces();

// 获取省份信息
const province = await provinceService.getProvinceById(1);
```

### 4.2 城市服务 (cityService.js)

**主要功能：**
- 根据省份获取城市列表
- 根据ID获取城市信息
- 搜索城市
- 获取热门城市

**使用方法：**
```javascript
const cityService = require('../../utils/cityService');

// 获取城市列表
const cities = await cityService.getCitiesByProvince(1);

// 搜索城市
const searchResults = await cityService.searchCities('南京', 1);
```

### 4.3 景区服务 (scenicService.js)

**主要功能：**
- 获取推荐景区
- 获取景区详情
- 搜索景区
- 获取热门景区

**使用方法：**
```javascript
const scenicService = require('../../utils/scenicService');

// 获取推荐景区
const scenics = await scenicService.getRecommendScenics({
  province_id: 1,
  city_id: 2,
  page: 1,
  limit: 10
});

// 获取景区详情
const detail = await scenicService.getScenicDetail('scenic_id_123');
```

### 4.4 轮播图服务 (carouselService.js)

**主要功能：**
- 获取轮播图数据
- 获取首页轮播图
- 获取景区轮播图
- 处理轮播图点击

**使用方法：**
```javascript
const carouselService = require('../../utils/carouselService');

// 获取首页轮播图
const carousels = await carouselService.getHomeCarousels(1);

// 处理轮播图点击
await carouselService.handleCarouselClick(carousel, (carousel) => {
  // 自定义跳转逻辑
});
```

## 5. 统一API入口

### 5.1 使用apiService

```javascript
const apiService = require('../../utils/apiService');

// 在页面中使用
Component({
  methods: {
    async loadData() {
      try {
        // 获取省份列表
        const provinces = await apiService.getProvinces();
        
        // 获取城市列表
        const cities = await apiService.getCities(provinceId);
        
        // 获取景区数据
        const scenics = await apiService.getRecommendScenics({
          province_id: 1,
          city_id: 2
        });
        
        // 获取轮播图
        const carousels = await apiService.getCarousels(provinceId);
        
        this.setData({
          provinces,
          cities,
          scenics: scenics.list || scenics,
          carousels
        });
      } catch (error) {
        apiService.handleError(error);
      }
    }
  }
});
```

### 5.2 批量请求

```javascript
// 并行加载多个接口
const [citiesResult, carouselsResult, scenicsResult] = await apiService.batchRequest([
  apiService.getCities(provinceId),
  apiService.getCarousels(provinceId),
  apiService.getRecommendScenics({ province_id: provinceId })
]);

// 处理结果
if (citiesResult.success) {
  this.setData({ cities: citiesResult.data });
}
```

## 6. 缓存机制

### 6.1 自动缓存

各服务模块会自动缓存数据：
- 省份数据：10分钟
- 城市数据：5分钟
- 轮播图：3分钟
- 景区数据：2分钟

### 6.2 清除缓存

```javascript
// 清除所有缓存
apiService.clearAllCache();

// 清除特定服务的缓存
provinceService.clearCache();
cityService.clearCache(provinceId); // 清除特定省份的城市缓存
```

## 7. 错误处理

### 7.1 统一错误处理

```javascript
try {
  const data = await apiService.getProvinces();
  // 处理成功数据
} catch (error) {
  // 使用统一错误处理
  apiService.handleError(error);
}
```

### 7.2 自定义错误处理

```javascript
try {
  const data = await apiService.getProvinces();
} catch (error) {
  if (error.message.includes('网络')) {
    wx.showModal({
      title: '网络错误',
      content: '请检查网络连接后重试'
    });
  } else {
    wx.showToast({
      title: error.message,
      icon: 'none'
    });
  }
}
```

## 8. 首页完整示例

```javascript
// pages/lanhu_shouye/component.js
const apiService = require('../../utils/apiService');

Component({
  data: {
    provinces: [],
    cities: [],
    carousels: [],
    scenics: []
  },

  lifetimes: {
    attached: function() {
      this.loadInitialData();
    }
  },

  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        // 1. 加载省份数据
        const provinces = await apiService.getProvinces();
        const processedProvinces = provinces.map((item, index) => ({
          ...item,
          selected: index === 0
        }));
        
        this.setData({ provinces: processedProvinces });
        
        // 2. 加载默认省份的相关数据
        if (provinces.length > 0) {
          await this.loadProvinceData(provinces[0].id);
        }
      } catch (error) {
        console.error('初始数据加载失败:', error);
        apiService.handleError(error);
      }
    },

    // 加载省份相关数据
    async loadProvinceData(provinceId) {
      try {
        const [citiesResult, carouselsResult, scenicsResult] = await apiService.batchRequest([
          apiService.getCities(provinceId),
          apiService.getCarousels(provinceId),
          apiService.getRecommendScenics({ province_id: provinceId })
        ]);
        
        if (citiesResult.success) {
          this.setData({
            cities: [{ id: 0, name: '全部' }, ...citiesResult.data]
          });
        }
        
        if (carouselsResult.success) {
          this.setData({
            carousels: carouselsResult.data
          });
        }
        
        if (scenicsResult.success) {
          this.setData({
            scenics: scenicsResult.data.list || scenicsResult.data
          });
        }
      } catch (error) {
        apiService.handleError(error);
      }
    }
  }
});
```

## 9. 部署配置

### 9.1 小程序配置

在微信公众平台配置服务器域名：
1. 登录微信公众平台
2. 进入开发 -> 开发管理 -> 开发设置
3. 在"服务器域名"中添加您的API域名

### 9.2 网络超时配置

在 `app.json` 中配置：

```json
{
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  }
}
```

## 10. 常见问题

### 10.1 域名配置问题

**问题：** 请求失败，提示域名不在白名单
**解决：** 在微信公众平台配置服务器域名白名单

### 10.2 HTTPS要求

**问题：** 小程序要求HTTPS协议
**解决：** 确保API域名使用HTTPS协议

### 10.3 跨域问题

**问题：** 开发工具中请求正常，真机测试失败
**解决：** 检查服务器CORS配置

### 10.4 数据格式问题

**问题：** 接口返回数据格式不匹配
**解决：** 检查API返回格式是否符合约定：

```json
{
  "code": 200,
  "message": "success",
  "data": { ... }
}
```

## 11. 性能优化建议

1. **使用缓存**：合理利用各服务的缓存机制
2. **批量请求**：使用batchRequest并行加载数据
3. **按需加载**：根据用户操作动态加载数据
4. **错误重试**：网络异常时自动重试
5. **预加载**：提前加载可能需要的数据

通过以上配置，您的小程序就可以成功接入真实的API接口了。
