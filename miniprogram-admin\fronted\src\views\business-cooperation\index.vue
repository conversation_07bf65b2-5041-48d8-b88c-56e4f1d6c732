<template>
  <div class="cooperation-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 合作申请表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>商务合作列表</span>
          <div>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              添加合作申请
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="cooperationList"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column prop="name" label="姓名" width="120" align="center" />
        
        <el-table-column prop="phone" label="电话" width="140" align="center" />
        
        <el-table-column prop="content" label="合作内容" min-width="200">
          <template #default="scope">
            <div class="content-text">{{ scope.row.content }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model.number="formData.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="合作内容" prop="content">
          <el-input 
            v-model="formData.content" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入合作内容" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { fetchCooperationList, createCooperation, updateCooperation, deleteCooperation } from '@/api/business-cooperation'
import { formatDate } from '@/utils'

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const cooperationList = ref([])
const loading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()
const formData = reactive({
  id: undefined,
  name: '',
  phone: '',
  content: '',
  userId: undefined
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  content: [{ required: true, message: '请输入合作内容', trigger: 'blur' }],
  userId: [{ required: true, message: '请输入用户ID', trigger: 'blur' }]
}

// 获取合作申请列表
const fetchCooperationListData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await fetchCooperationList(params)
    if (response.code === 200) {
      cooperationList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取合作申请列表失败:', error)
    ElMessage.error('获取合作申请列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchCooperationListData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchCooperationListData()
}

// 刷新
const handleRefresh = () => {
  fetchCooperationListData()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchCooperationListData()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchCooperationListData()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    name: '',
    phone: '',
    content: '',
    userId: undefined
  })
}

// 创建合作申请
const handleCreate = () => {
  resetForm()
  isEdit.value = false
  dialogTitle.value = '添加合作申请'
  dialogVisible.value = true
}

// 编辑合作申请
const handleEdit = (row) => {
  Object.assign(formData, row)
  isEdit.value = true
  dialogTitle.value = '编辑合作申请'
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await updateCooperation(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      await createCooperation(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchCooperationListData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 删除合作申请
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将永久删除该合作申请，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteCooperation(row.id)
    ElMessage.success('删除成功')
    fetchCooperationListData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  fetchCooperationListData()
})
</script>

<style lang="scss" scoped>
.cooperation-list {
  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

.content-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

@media (max-width: 768px) {
  .cooperation-list {
    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 10px;

        .el-input {
          width: 100% !important;
        }
      }
    }

    .pagination-container {
      text-align: center;

      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>
