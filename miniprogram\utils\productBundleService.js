const httpService = require('./httpService');

class ProductBundleService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  // 构建缓存key
  buildCacheKey(method, params = {}) {
    return `productBundle_${method}_${JSON.stringify(params)}`;
  }

  // 设置缓存
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // 获取缓存
  getCache(key) {
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  // 清除缓存
  clearCache() {
    this.cache.clear();
    console.log('组合包服务缓存已清除');
  }

  /**
   * 获取组合包详情
   * @param {number} bundleId 组合包ID
   * @returns {Promise<Object>} 组合包详情
   */
  async getProductBundleDetail(bundleId) {
    try {
      if (!bundleId) {
        throw new Error('组合包ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('detail', { bundleId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的组合包详情数据');
        return cachedData;
      }

      console.log(`从服务器获取组合包详情 (ID: ${bundleId})`);
      
      const bundleDetail = await httpService.get(`/api/product-bundles/${bundleId}`, {}, {
        loadingText: '加载组合包详情中...'
      });

      // 缓存数据
      this.setCache(cacheKey, bundleDetail);

      return bundleDetail;
    } catch (error) {
      console.error('获取组合包详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  /**
   * 获取组合包列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 组合包列表
   */
  async getProductBundleList(params = {}) {
    try {
      const {
        current = 1,
        size = 10,
        name = '',
        status = null
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('list', { current, size, name, status });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的组合包列表数据');
        return cachedData;
      }

      console.log('从服务器获取组合包列表');
      
      const queryParams = {
        current: current.toString(),
        size: size.toString()
      };

      if (name) {
        queryParams.name = name;
      }

      if (status !== null && status !== undefined) {
        queryParams.status = status.toString();
      }

      const bundleList = await httpService.get('/api/product-bundles/page', queryParams, {
        loadingText: '加载组合包列表中...'
      });

      // 缓存数据
      this.setCache(cacheKey, bundleList);

      return bundleList;
    } catch (error) {
      console.error('获取组合包列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  /**
   * 获取启用的组合包列表
   * @param {Object} params 查询参数
   * @returns {Promise<Array>} 启用的组合包列表
   */
  async getEnabledProductBundles(params = {}) {
    try {
      const {
        limit = 20
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('enabled', { limit });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的启用组合包数据');
        return cachedData;
      }

      console.log('从服务器获取启用的组合包列表');
      
      const queryParams = {
        current: '1',
        size: limit.toString(),
        status: '1' // 只获取启用的组合包
      };

      const response = await httpService.get('/api/product-bundles/page', queryParams, {
        loadingText: '加载组合包中...'
      });

      // 提取记录数组
      let bundles = [];
      if (response && response.records) {
        bundles = response.records;
      } else if (Array.isArray(response)) {
        bundles = response;
      }

      // 缓存数据
      this.setCache(cacheKey, bundles);

      return bundles;
    } catch (error) {
      console.error('获取启用组合包列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  /**
   * 根据景区ID获取相关组合包
   * @param {Array} scenicIds 景区ID数组
   * @returns {Promise<Array>} 相关组合包列表
   */
  async getProductBundlesByScenicIds(scenicIds) {
    try {
      if (!scenicIds || !Array.isArray(scenicIds) || scenicIds.length === 0) {
        return [];
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('byScenicIds', { scenicIds });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的景区相关组合包数据');
        return cachedData;
      }

      console.log('从服务器获取景区相关组合包');
      
      // 获取所有启用的组合包
      const allBundles = await this.getEnabledProductBundles({ limit: 100 });

      // 筛选包含指定景区的组合包
      const relatedBundles = allBundles.filter(bundle => {
        if (!bundle.scenicIds || !Array.isArray(bundle.scenicIds)) {
          return false;
        }
        
        // 检查是否有交集
        return bundle.scenicIds.some(bundleScenicId => 
          scenicIds.includes(bundleScenicId)
        );
      });

      // 缓存数据
      this.setCache(cacheKey, relatedBundles);

      return relatedBundles;
    } catch (error) {
      console.error('获取景区相关组合包失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  /**
   * 处理组合包数据，确保字段格式正确
   * @param {Object} bundle 原始组合包数据
   * @returns {Object} 处理后的组合包数据
   */
  processProductBundleData(bundle) {
    if (!bundle) return null;

    // 处理景区ID数组
    let scenicIds = [];
    if (bundle.scenicIds) {
      if (Array.isArray(bundle.scenicIds)) {
        scenicIds = bundle.scenicIds;
      } else if (typeof bundle.scenicIds === 'string') {
        try {
          // 尝试解析JSON
          scenicIds = JSON.parse(bundle.scenicIds);
        } catch (error) {
          // 如果不是JSON，按逗号分割
          scenicIds = bundle.scenicIds.split(',').map(id => id.trim()).filter(id => id);
        }
      }
    }

    return {
      ...bundle,
      scenicIds,
      // 确保数值类型正确
      discount: parseFloat(bundle.discount) || 0,
      status: parseInt(bundle.status) || 0,
      // 确保图片URL格式正确
      image: bundle.description || bundle.image || '',
      // 添加显示用的字段
      displayPrice: `¥${(parseFloat(bundle.discount) || 0).toFixed(2)}`,
      statusText: bundle.status === 1 ? '启用' : '禁用'
    };
  }
}

// 创建单例实例
const productBundleService = new ProductBundleService();

module.exports = productBundleService;
