package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.ProductBundle;
import com.tourism.miniprogram.mapper.ProductBundleMapper;
import com.tourism.miniprogram.service.ProductBundleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品组合包服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ProductBundleServiceImpl extends ServiceImpl<ProductBundleMapper, ProductBundle> implements ProductBundleService {

    @Override
    public List<ProductBundle> getEnabledBundles() {
        try {
            return baseMapper.selectEnabledBundles();
        } catch (Exception e) {
            log.error("获取启用组合包列表失败", e);
            throw new RuntimeException("获取启用组合包列表失败");
        }
    }
}
