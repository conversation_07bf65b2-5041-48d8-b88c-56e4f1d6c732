<template>
  <div class="city-detail">
    <div class="page-header">
      <h2>城市详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="城市ID">
          {{ detail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="城市名称">
          {{ detail.name }}
        </el-descriptions-item>
        <el-descriptions-item label="所属省份">
          {{ detail.provinceName }}
        </el-descriptions-item>
        <el-descriptions-item label="城市代码">
          {{ detail.code }}
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCityInfo } from '@/api/city'
import { getProvinceList } from '@/api/province'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const provinceList = ref([])

const detail = reactive({
  id: '',
  name: '',
  provinceId: '',
  provinceName: '',
  code: '',
  sort: 0,
  status: 1,
  createdAt: '',
  updatedAt: ''
})

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getCityInfo(route.params.id)
    Object.assign(detail, data)
    
    // 设置省份名称
    const province = provinceList.value.find(p => p.id === data.provinceId)
    detail.provinceName = province ? province.name : '未知'
  } catch (error) {
    ElMessage.error('获取城市详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/city/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/city/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  fetchDetail()
})
</script>

<style scoped>
.city-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
