// 文件上传服务模块
const httpService = require('./httpService');
const config = require('./config');

class UploadService {
  constructor() {
    this.baseUrl = config.api.baseUrl; 
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  }

  // 上传图片文件
  async uploadImage(filePath, options = {}) {
    try {
      console.log('开始上传图片:', filePath);

      // 验证文件
      await this.validateFile(filePath);

      // 获取用户token
      const token = wx.getStorageSync('user_token');
      if (!token) {
        throw new Error('用户未登录，无法上传文件');
      }

      const {
        showProgress = true
      } = options;

      return new Promise((resolve, reject) => {
        const uploadTask = wx.uploadFile({
          url: `${this.baseUrl}/api/file/upload/image`,
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            console.log('上传响应:', res);
            
            if (res.statusCode === 200) {
              try {
                const data = JSON.parse(res.data);
                if (data.code === 200) {
                  console.log('图片上传成功:', data.data.cdnUrl);
                  resolve(data.data.cdnUrl);
                } else {
                  reject(new Error(data.message || '上传失败'));
                }
              } catch (parseError) {
                console.error('解析上传响应失败:', parseError);
                reject(new Error('服务器响应格式错误'));
              }
            } else {
              reject(new Error(`上传失败，状态码: ${res.statusCode}`));
            }
          },
          fail: (err) => {
            console.error('上传失败:', err);
            reject(new Error(err.errMsg || '上传失败'));
          }
        });

        // 显示上传进度
        if (showProgress) {
          uploadTask.onProgressUpdate((res) => {
            const progress = res.progress;
            console.log('上传进度:', progress + '%');
            
            // 可以在这里更新UI进度条
            wx.showLoading({
              title: `上传中 ${progress}%`
            });
          });
        }
      });

    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  }

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      console.log('上传头像:', filePath);
      
      const result = await this.uploadImage(filePath, {
        showProgress: true
      });

      // 返回上传后的URL
      return result;
    } catch (error) {
      console.error('上传头像失败:', error);
      throw new Error('头像上传失败: ' + error.message);
    }
  }

  // 验证文件
  async validateFile(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().getFileInfo({
        filePath: filePath,
        success: (res) => {
          console.log('文件信息:', res);
          
          // 检查文件大小
          if (res.size > this.maxFileSize) {
            reject(new Error(`文件大小不能超过${this.maxFileSize / 1024 / 1024}MB`));
            return;
          }

          resolve(res);
        },
        fail: (err) => {
          console.error('获取文件信息失败:', err);
          reject(new Error('文件信息获取失败'));
        }
      });
    });
  }

  // 压缩图片
  async compressImage(filePath, quality = 80) {
    return new Promise((resolve, reject) => {
      wx.compressImage({
        src: filePath,
        quality: quality,
        success: (res) => {
          console.log('图片压缩成功:', res);
          resolve(res.tempFilePath);
        },
        fail: (err) => {
          console.error('图片压缩失败:', err);
          // 压缩失败时返回原文件路径
          resolve(filePath);
        }
      });
    });
  }

  // 选择并上传图片
  async chooseAndUploadImage(options = {}) {
    try {
      const {
        count = 1,
        sizeType = ['compressed', 'original'],
        sourceType = ['album', 'camera'],
        compress = true,
        quality = 80
      } = options;

      // 选择图片
      const chooseResult = await this.chooseImage({
        count,
        sizeType,
        sourceType
      });

      const filePath = chooseResult.tempFilePaths[0];
      
      // 压缩图片（如果需要）
      let finalFilePath = filePath;
      if (compress) {
        finalFilePath = await this.compressImage(filePath, quality);
      }

      // 上传图片
      const uploadResult = await this.uploadImage(finalFilePath, options);
      
      return uploadResult;
    } catch (error) {
      console.error('选择并上传图片失败:', error);
      throw error;
    }
  }

  // 选择图片
  chooseImage(options = {}) {
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: options.count || 1,
        sizeType: options.sizeType || ['compressed', 'original'],
        sourceType: options.sourceType || ['album', 'camera'],
        success: resolve,
        fail: reject
      });
    });
  }

  // 批量上传图片
  async uploadMultipleImages(filePaths, options = {}) {
    try {
      const uploadPromises = filePaths.map(filePath => 
        this.uploadImage(filePath, options)
      );

      const results = await Promise.all(uploadPromises);
      console.log('批量上传完成:', results);
      
      return results;
    } catch (error) {
      console.error('批量上传失败:', error);
      throw error;
    }
  }

  // 删除上传的文件
  async deleteFile(fileUrl) {
    try {
      const token = wx.getStorageSync('user_token');
      if (!token) {
        throw new Error('用户未登录');
      }

      const result = await httpService.delete('/api/upload/delete', {
        header: {
          'Authorization': `Bearer ${token}`
        },
        data: {
          fileUrl: fileUrl
        }
      });

      console.log('文件删除成功:', result);
      return result;
    } catch (error) {
      console.error('删除文件失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const uploadService = new UploadService();

module.exports = uploadService;
