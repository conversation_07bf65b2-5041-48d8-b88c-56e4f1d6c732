import request from '@/utils/request'

// 分页获取讲解产品列表
export function getGuideProductPage(params) {
  return request({
    url: '/guide-products/page',
    method: 'get',
    params
  })
}

// 获取讲解产品详情
export function getGuideProductById(id) {
  return request({
    url: `/guide-products/${id}`,
    method: 'get'
  })
}

// 创建讲解产品
export function createGuideProduct(data) {
  return request({
    url: '/guide-products',
    method: 'post',
    data
  })
}

// 更新讲解产品
export function updateGuideProduct(id, data) {
  return request({
    url: `/guide-products/${id}`,
    method: 'put',
    data
  })
}

// 删除讲解产品
export function deleteGuideProduct(id) {
  return request({
    url: `/guide-products/${id}`,
    method: 'delete'
  })
}
