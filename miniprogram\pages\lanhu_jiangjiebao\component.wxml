<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重新加载</button>
  </view>

  <!-- 讲解包长图 -->
  <view wx:else class="bundle-content">
    <image
      wx:if="{{bundleDetail.description}}"
      class="bundle-long-image"
      src="{{bundleDetail.description}}"
      mode="widthFix"
      bindtap="onPreviewImage"
    />
    <view wx:else class="no-image">
      <text>暂无图片</text>
    </view>
  </view>

  <!-- 底部固定悬浮购买按钮 -->
  <view wx:if="{{!loading && !error && bundleDetail}}" class="fixed-buy-bar">
    <view class="price-info">
      <text class="discount-label">限时特惠</text>
      <text class="price-text">¥{{bundleDetail.discount}}</text>
      <text class="price-unit">元</text>
    </view>
    <button
      class="buy-button {{bundleDetail.status === 1 ? 'buy-button-active' : 'buy-button-disabled'}}"
      bindtap="onBuyBundle"
      disabled="{{bundleDetail.status !== 1}}"
    >
      马上购买
    </button>
  </view>
</view>