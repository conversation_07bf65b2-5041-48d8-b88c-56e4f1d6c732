package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 讲解产品实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product_table")
@ApiModel(value = "GuideProduct对象", description = "讲解产品信息")
public class GuideProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品ID")
    @TableId(value = "product_id", type = IdType.AUTO)
    private Integer productId;

    @ApiModelProperty(value = "产品标题")
    @TableField("title")
    @NotBlank(message = "产品标题不能为空")
    private String title;

    @ApiModelProperty(value = "景区ID")
    @TableField("scenic_id")
    @NotBlank(message = "景区ID不能为空")
    private String scenicId;

    @ApiModelProperty(value = "讲解点数量")
    @TableField("point_count")
    private Integer pointCount;

    @ApiModelProperty(value = "讲解时长")
    @TableField("duration")
    private String duration;

    @ApiModelProperty(value = "产品价格")
    @TableField("price")
    @NotNull(message = "产品价格不能为空")
    private BigDecimal price;

    @ApiModelProperty(value = "背景图片URL")
    @TableField("background_image_url")
    @NotBlank(message = "背景图片URL不能为空")
    private String backgroundImageUrl;

    @ApiModelProperty(value = "示例视频URL")
    @TableField("example_video_url")
    @NotBlank(message = "示例视频URL不能为空")
    private String exampleVideoUrl;

    @ApiModelProperty(value = "讲解员ID")
    @TableField("lecturer_id")
    @NotNull(message = "讲解员ID不能为空")
    private Integer lecturerId;

    @ApiModelProperty(value = "地图URL")
    @TableField("map_url")
    @NotBlank(message = "地图URL不能为空")
    private String mapUrl;

    @ApiModelProperty(value = "开始收听图片URL")
    @TableField("start_listening_image_url")
    @NotBlank(message = "开始收听图片URL不能为空")
    private String startListeningImageUrl;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "产品描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private java.time.LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private java.time.LocalDateTime updatedAt;

    @ApiModelProperty(value = "背景颜色")
    @TableField("background_color")
    private String backgroundColor;
}
