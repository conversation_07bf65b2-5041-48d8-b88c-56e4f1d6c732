<template>
  <div class="app-wrapper" :class="classObj">
    <!-- 侧边栏 -->
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    
    <Sidebar class="sidebar-container" />
    
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="fixed-header">
        <Navbar />
      </div>
      
      <!-- 主要内容区域 -->
      <AppMain />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import Sidebar from './components/Sidebar/index.vue'
import Navbar from './components/Navbar.vue'
import AppMain from './components/AppMain.vue'

const appStore = useAppStore()

// 响应式设计
const WIDTH = 992
const sidebar = computed(() => appStore.sidebar)
const device = computed(() => appStore.device)

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

// 监听窗口大小变化
const isMobile = () => window.innerWidth - 1 < WIDTH

const resizeHandler = () => {
  if (!document.hidden) {
    const mobile = isMobile()
    appStore.setDevice(mobile ? 'mobile' : 'desktop')

    if (mobile) {
      appStore.closeSidebar(true)
    }
  }
}

// 点击遮罩层关闭侧边栏
const handleClickOutside = () => {
  appStore.closeSidebar(false)
}

// 生命周期
onMounted(() => {
  resizeHandler()
  window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler)
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.main-container {
  margin-left: 210px;
  transition: margin-left 0.28s;
  min-height: 100vh;
  width: calc(100% - 210px);
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - 210px);
  transition: width 0.28s;
}

.hideSidebar .main-container {
  margin-left: 54px;
  width: calc(100% - 54px);
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .main-container {
  margin-left: 0;
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
