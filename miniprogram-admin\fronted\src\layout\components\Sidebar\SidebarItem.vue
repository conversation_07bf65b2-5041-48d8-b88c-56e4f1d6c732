<template>
  <div v-if="!item.meta || !item.meta.hidden">
    <!-- 一级菜单项 - 直接显示为菜单项 -->
    <app-link :to="item.path">
      <el-menu-item :index="item.path" :class="{'submenu-title-noDropdown': !isNest}">
        <el-icon v-if="item.meta && item.meta.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <template #title>
          <span>{{ item.meta.title }}</span>
        </template>
      </el-menu-item>
    </app-link>
  </div>
</template>

<script setup>
import AppLink from './Link.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  }
})

// 调试信息
console.log('SidebarItem 渲染:', props.item)
</script>

<style lang="scss" scoped>
.nest-menu .el-sub-menu > .el-sub-menu__title,
.el-menu-item {
  &:hover {
    background-color: #263445 !important;
  }
}
</style>
