<view class="page">
  <!-- 顶部标签栏 -->
  <view class="tab-bar">
    <view class="tab-container">
      <view wx:for="{{tabs}}" wx:key="key" class="tab-item {{activeTab === item.key ? 'active' : ''}}" data-tab="{{item.key}}" bindtap="onTabChange">
        <text class="tab-text">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view wx:if="{{loading && orders.length === 0}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <view wx:elif="{{orders.length === 0}}" class="empty-container">
      <view class="block_2">
        <image src="../../images/lanhu_quanbudingdan/FigmaDDSSlicePNG7280c13388256058c12e3d6f6c343aed.png" class="image_3"></image>
      </view>
    </view>

    <view wx:else>
      <view wx:for="{{orders}}" wx:key="id" class="order-item">
        <!-- 订单头部 -->
        <view class="order-header">
          <text class="order-no">订单号: {{item.orderNo}}</text>
          <text class="order-status">{{item.statusText}}</text>
        </view>

        <!-- 订单内容 -->
        <view class="order-content">
          <image src="{{item.scenicInfo ? item.scenicInfo.image : '../../images/lanhu_quanbudingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png'}}" class="product-image"></image>
          <view class="order-info">
            <text class="product-name">{{item.scenicInfo ? item.scenicInfo.title : '旅游讲解服务'}}</text>
            <text class="order-time">下单时间: {{item.formattedDate}}</text>
            <view class="order-summary">
              <text class="item-count">共 1 件商品</text>
              <view class="price-info">
                <text class="price-label">实付款：</text>
                <text class="price-symbol">￥</text>
                <text class="price-amount">{{item.totalAmount}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单操作按钮 -->
        <view class="order-actions">
          <view wx:if="{{item.status === 'pending'}}" class="action-buttons">
            <view class="action-btn cancel-btn" data-order-id="{{item.id}}" bindtap="cancelOrder">
              <text class="btn-text">取消订单</text>
            </view>
            <view class="action-btn primary-btn" data-order-id="{{item.id}}" bindtap="payOrder">
              <text class="btn-text">立即支付</text>
            </view>
          </view>

          <view wx:elif="{{item.status === 'paid'}}" class="action-buttons">
            <view class="action-btn secondary-btn" data-order-id="{{item.id}}" bindtap="reviewOrder">
              <text class="btn-text">立即评价</text>
            </view>
            <view class="action-btn primary-btn" data-order-id="{{item.id}}" bindtap="buyAgain">
              <text class="btn-text">再次购买</text>
            </view>
          </view>

          <view wx:elif="{{item.status === 'canceled'}}" class="action-buttons">
            <view class="action-btn primary-btn" data-order-id="{{item.id}}" bindtap="buyAgain">
              <text class="btn-text">再次购买</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view wx:if="{{loading && orders.length > 0}}" class="load-more">
      <text class="load-more-text">加载中...</text>
    </view>
  </view>
</view>