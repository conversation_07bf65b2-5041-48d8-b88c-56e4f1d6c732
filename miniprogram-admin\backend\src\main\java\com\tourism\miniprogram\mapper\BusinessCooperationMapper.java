package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.BusinessCooperation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商务合作Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface BusinessCooperationMapper extends BaseMapper<BusinessCooperation> {

    /**
     * 根据用户ID获取合作申请列表
     *
     * @param userId 用户ID
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<BusinessCooperation> selectCooperationsByUserId(@Param("userId") Integer userId);



    /**
     * 根据关键词搜索合作申请
     *
     * @param keyword 关键词
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR phone LIKE CONCAT('%', #{keyword}, '%') " +
            "OR content LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY created_at DESC")
    List<BusinessCooperation> searchCooperations(@Param("keyword") String keyword);
}
