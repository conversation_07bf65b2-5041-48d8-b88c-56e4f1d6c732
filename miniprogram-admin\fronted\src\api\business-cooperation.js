import request from '@/utils/request'

export function fetchCooperationList(params) {
  return request({
    url: '/business-cooperation/page',
    method: 'get',
    params
  })
}

export function fetchCooperation(id) {
  return request({
    url: `/business-cooperation/${id}`,
    method: 'get'
  })
}

export function createCooperation(data) {
  return request({
    url: '/business-cooperation',
    method: 'post',
    data
  })
}

export function updateCooperation(id, data) {
  return request({
    url: `/business-cooperation/${id}`,
    method: 'put',
    data
  })
}

export function deleteCooperation(id) {
  return request({
    url: `/business-cooperation/${id}`,
    method: 'delete'
  })
}

export function searchCooperations(keyword) {
  return request({
    url: '/business-cooperation/search',
    method: 'get',
    params: { keyword }
  })
}
