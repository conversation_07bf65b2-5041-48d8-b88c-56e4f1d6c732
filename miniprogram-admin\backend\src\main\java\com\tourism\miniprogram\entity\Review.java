package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 评价实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("review_table")
@ApiModel(value = "Review对象", description = "评价信息")
public class Review implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "评价ID")
    @TableId(value = "review_id", type = IdType.AUTO)
    private Integer reviewId;

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    @NotBlank(message = "产品ID不能为空")
    private String productId;

    @ApiModelProperty(value = "评价内容")
    @TableField("content")
    @NotBlank(message = "评价内容不能为空")
    private String content;

    @ApiModelProperty(value = "评价时间")
    @TableField("review_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reviewTime;

    @ApiModelProperty(value = "状态：1-显示，0-隐藏")
    @TableField("status")
    private Integer status;
}
