# 旅游讲解小程序文件管理API系统

基于Java 1.8和Spring Boot的后端API系统，用于管理腾讯云COS中图像和视频文件的上传与下载。

## 功能特性

- ✅ 支持图片和视频文件上传
- ✅ 文件上传后立即返回下载URL
- ✅ 支持批量文件上传
- ✅ 集成腾讯云COS存储
- ✅ 支持CDN加速访问
- ✅ 完整的API文档（Swagger + Knife4j）
- ✅ 文件格式和大小验证
- ✅ 全局异常处理
- ✅ 跨域支持

## 技术栈

- **Java**: 1.8
- **Spring Boot**: 2.7.18
- **腾讯云COS SDK**: 5.6.89
- **Swagger**: 3.0.0
- **Knife4j**: 3.0.3
- **Hutool**: 5.8.16
- **Lombok**: 自动生成代码

## 项目结构

```
src/main/java/com/tourism/
├── TourismFileManagementApplication.java  # 启动类
├── common/
│   └── Result.java                        # 通用响应结果类
├── config/
│   ├── TencentCosConfig.java             # 腾讯云COS配置
│   ├── SwaggerConfig.java                # Swagger配置
│   ├── FileUploadConfig.java             # 文件上传配置
│   └── WebConfig.java                    # Web配置
├── controller/
│   ├── FileManagementController.java     # 文件管理控制器
│   └── HealthController.java             # 健康检查控制器
├── dto/
│   ├── FileInfoDTO.java                  # 文件信息DTO
│   └── FileUploadResponseDTO.java        # 文件上传响应DTO
├── exception/
│   └── GlobalExceptionHandler.java       # 全局异常处理器
├── service/
│   ├── CosService.java                   # COS服务类
│   └── FileManagementService.java        # 文件管理服务类
└── utils/
    └── FileUtils.java                    # 文件工具类
```

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Maven 3.6+
- 腾讯云COS账号

### 2. 配置文件

修改 `src/main/resources/application.yml` 中的腾讯云COS配置：

```yaml
tencent:
  cos:
    secret-id: YOUR_SECRET_ID          # 替换为您的SecretId
    secret-key: YOUR_SECRET_KEY        # 替换为您的SecretKey
    region: ap-beijing                 # 替换为您的地域
    bucket-name: your-bucket-name      # 替换为您的存储桶名称
    cdn-domain: https://your-cdn-domain.com  # 可选，CDN域名
    url-expire-time: 3600             # URL有效期（秒）
```

### 3. 编译运行

```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

### 4. 访问API文档

启动成功后，访问以下地址查看API文档：

- **Knife4j增强文档**: http://localhost:8080/api/doc.html
- **Swagger原生文档**: http://localhost:8080/api/swagger-ui/

## API接口说明

### 文件上传接口

#### 单文件上传

```http
POST /api/file/upload
Content-Type: multipart/form-data

参数:
- file: 要上传的文件（支持图片和视频）

响应示例:
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "uuid-123456",
    "originalFileName": "景区图片.jpg",
    "fileType": "image",
    "fileSize": 1024000,
    "downloadUrl": "https://example.cos.ap-beijing.myqcloud.com/tourism/files/...",
    "cdnUrl": "https://cdn.example.com/tourism/files/...",
    "uploadTime": "2023-12-01 12:34:56",
    "urlExpireTime": 3600
  },
  "timestamp": 1640995200000
}
```

#### 批量文件上传

```http
POST /api/file/upload/batch
Content-Type: multipart/form-data

参数:
- files: 要上传的文件数组（最多10个文件）
```

### 文件下载接口

#### 生成下载URL

```http
GET /api/file/download-url?filePath=tourism/files/image/2023/12/01/20231201_123456_abc123.jpg

响应示例:
{
  "code": 200,
  "message": "下载URL生成成功",
  "data": "https://example.cos.ap-beijing.myqcloud.com/tourism/files/...",
  "timestamp": 1640995200000
}
```

### 文件删除接口

```http
DELETE /api/file/delete?filePath=tourism/files/image/2023/12/01/20231201_123456_abc123.jpg

响应示例:
{
  "code": 200,
  "message": "文件删除成功",
  "data": true,
  "timestamp": 1640995200000
}
```

## 支持的文件格式

### 图片格式
- jpg, jpeg, png, gif, bmp, webp

### 视频格式
- mp4, avi, mov, wmv, flv, 3gp, mkv

## 配置说明

### 文件上传限制

```yaml
file:
  upload:
    image-types: jpg,jpeg,png,gif,bmp,webp    # 允许的图片格式
    video-types: mp4,avi,mov,wmv,flv,3gp,mkv  # 允许的视频格式
    max-size: 104857600                       # 最大文件大小（100MB）
    path-prefix: tourism/files/               # 文件存储路径前缀
```

### Spring Boot文件上传配置

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB      # 单个文件最大大小
      max-request-size: 100MB   # 请求最大大小
      enabled: true             # 启用文件上传
```

## 最佳实践

### 1. 安全性

- 使用腾讯云CAM进行权限管理
- 定期轮换访问密钥
- 启用COS防盗链功能
- 设置合理的URL有效期

### 2. 性能优化

- 配置CDN加速域名
- 使用合适的文件存储类型
- 启用COS传输加速
- 合理设置文件缓存策略

### 3. 监控和日志

- 配置COS访问日志
- 监控API调用频率
- 设置异常告警
- 定期检查存储使用量

## 常见问题

### Q: 文件上传失败怎么办？

A: 请检查以下几点：
1. 腾讯云COS配置是否正确
2. 文件格式是否支持
3. 文件大小是否超出限制
4. 网络连接是否正常

### Q: 如何配置CDN加速？

A: 在腾讯云控制台配置CDN域名，然后在配置文件中设置 `cdn-domain` 参数。

### Q: 如何自定义文件存储路径？

A: 修改配置文件中的 `path-prefix` 参数，或在 `FileUtils.generateFilePath()` 方法中自定义路径生成逻辑。

## 许可证

Apache License 2.0

## 联系方式

如有问题，请联系开发团队：<EMAIL>
