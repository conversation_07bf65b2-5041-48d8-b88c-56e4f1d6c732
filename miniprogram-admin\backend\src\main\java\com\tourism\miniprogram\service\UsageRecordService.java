package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.UsageRecord;

import java.util.List;

/**
 * 使用记录服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface UsageRecordService extends IService<UsageRecord> {

    /**
     * 根据用户ID获取使用记录
     *
     * @param userId 用户ID
     * @return 使用记录列表
     */
    List<UsageRecord> getUsageRecordsByUserId(Integer userId);

    /**
     * 根据景区ID获取使用记录
     *
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    List<UsageRecord> getUsageRecordsByScenicId(String scenicId);

    /**
     * 根据门票ID获取使用记录
     *
     * @param couponId 门票ID
     * @return 使用记录列表
     */
    List<UsageRecord> getUsageRecordsByCouponId(Integer couponId);

    /**
     * 创建使用记录
     *
     * @param couponId 门票ID
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 是否成功
     */
    boolean createUsageRecord(Integer couponId, Integer userId, String scenicId);
}
