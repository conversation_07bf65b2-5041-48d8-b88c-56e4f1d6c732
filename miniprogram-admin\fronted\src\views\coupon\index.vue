<template>
  <div class="coupon-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>门票管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="卡券编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入卡券编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input-number
            v-model="searchForm.userId"
            placeholder="请输入用户ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="未激活" value="unactivated" />
            <el-option label="已激活" value="active" />
            <el-option label="已使用" value="used" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="景区ID">
          <el-input-number
            v-model="searchForm.scenicId"
            placeholder="请输入景区ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column prop="code" label="卡券编码" min-width="150" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="scenicId" label="景区ID" width="100" />
        <el-table-column prop="productId" label="产品ID" width="100" />
        <el-table-column prop="bundleId" label="组合包ID" width="100" />
        <el-table-column prop="orderId" label="订单ID" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="validFrom" label="生效时间" width="180" />
        <el-table-column prop="validTo" label="过期时间" width="180" />
        <el-table-column prop="usedAt" label="使用时间" width="180" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCouponPage } from '@/api/coupon'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])

const searchForm = reactive({
  code: '',
  userId: null,
  status: '',
  scenicId: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'unactivated': 'info',
    'active': 'success',
    'used': 'warning',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'unactivated': '未激活',
    'active': '已激活',
    'used': '已使用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const { data } = await getCouponPage(params)
    tableData.value = data.records
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取门票列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    code: '',
    userId: null,
    status: '',
    scenicId: null
  })
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 操作
const handleDetail = (row) => {
  router.push(`/coupon/detail/${row.id}`)
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.coupon-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
