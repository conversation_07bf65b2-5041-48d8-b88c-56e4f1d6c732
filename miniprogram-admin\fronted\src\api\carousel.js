import request from '@/utils/request'

/**
 * 获取轮播图数据
 * @param {Object} params 查询参数
 * @param {number} params.provinceId 省份ID
 * @param {string} params.type 类型
 */
export function getCarouselList(params) {
  return request({
    url: '/carousels',
    method: 'get',
    params
  })
}

/**
 * 分页获取轮播图列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.title 标题
 * @param {number} params.provinceId 省份ID
 * @param {string} params.type 类型
 */
export function getCarouselPage(params) {
  return request({
    url: '/carousels/page',
    method: 'get',
    params
  })
}

/**
 * 获取轮播图详情
 * @param {number} id 轮播图ID
 */
export function getCarouselInfo(id) {
  return request({
    url: `/carousels/${id}`,
    method: 'get'
  })
}

/**
 * 创建轮播图
 * @param {Object} data 轮播图数据
 */
export function createCarousel(data) {
  return request({
    url: '/carousels',
    method: 'post',
    data
  })
}

/**
 * 更新轮播图
 * @param {number} id 轮播图ID
 * @param {Object} data 轮播图数据
 */
export function updateCarousel(id, data) {
  return request({
    url: `/carousels/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除轮播图
 * @param {number} id 轮播图ID
 */
export function deleteCarousel(id) {
  return request({
    url: `/carousels/${id}`,
    method: 'delete'
  })
}
