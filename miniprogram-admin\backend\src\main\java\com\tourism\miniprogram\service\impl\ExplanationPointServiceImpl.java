package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.ExplanationPoint;
import com.tourism.miniprogram.mapper.ExplanationPointMapper;
import com.tourism.miniprogram.service.ExplanationPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲解点服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class ExplanationPointServiceImpl extends ServiceImpl<ExplanationPointMapper, ExplanationPoint> implements ExplanationPointService {

    @Override
    public List<ExplanationPoint> getPointsByAreaId(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.selectPointsByAreaId(areaId);
        } catch (Exception e) {
            log.error("根据区域ID获取讲解点列表失败，areaId: {}", areaId, e);
            throw new RuntimeException("获取讲解点列表失败");
        }
    }
}
