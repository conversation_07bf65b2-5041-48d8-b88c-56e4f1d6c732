import request from '@/utils/request'

/**
 * 分页获取讲解点列表
 * @param {Object} params 查询参数
 */
export function getExplanationPointPage(params) {
  return request({
    url: '/explanation-points/page',
    method: 'get',
    params
  })
}

/**
 * 获取讲解点详情
 * @param {number} id 讲解点ID
 */
export function getExplanationPointById(id) {
  return request({
    url: `/explanation-points/${id}`,
    method: 'get'
  })
}

/**
 * 根据区域ID获取讲解点列表
 * @param {number} areaId 区域ID
 */
export function getPointsByAreaId(areaId) {
  return request({
    url: `/explanation-points/area/${areaId}`,
    method: 'get'
  })
}

/**
 * 创建讲解点
 * @param {Object} data 讲解点数据
 */
export function createExplanationPoint(data) {
  return request({
    url: '/explanation-points',
    method: 'post',
    data
  })
}

/**
 * 更新讲解点
 * @param {number} id 讲解点ID
 * @param {Object} data 讲解点数据
 */
export function updateExplanationPoint(id, data) {
  return request({
    url: `/explanation-points/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除讲解点
 * @param {number} id 讲解点ID
 */
export function deleteExplanationPoint(id) {
  return request({
    url: `/explanation-points/${id}`,
    method: 'delete'
  })
}
