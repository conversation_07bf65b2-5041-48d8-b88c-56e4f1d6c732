package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.City;

import java.util.List;

/**
 * 城市服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface CityService extends IService<City> {

    /**
     * 根据省份ID获取启用的城市列表
     *
     * @param provinceId 省份ID
     * @return 城市列表
     */
    List<City> getEnabledCitiesByProvinceId(Integer provinceId);
}
