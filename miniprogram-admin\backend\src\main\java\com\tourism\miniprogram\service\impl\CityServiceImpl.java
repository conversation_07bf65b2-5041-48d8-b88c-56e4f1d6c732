package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.City;
import com.tourism.miniprogram.mapper.CityMapper;
import com.tourism.miniprogram.service.CityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 城市服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class CityServiceImpl extends ServiceImpl<CityMapper, City> implements CityService {

    @Override
    public List<City> getEnabledCitiesByProvinceId(Integer provinceId) {
        try {
            if (provinceId == null) {
                throw new IllegalArgumentException("省份ID不能为空");
            }
            return baseMapper.selectEnabledCitiesByProvinceId(provinceId);
        } catch (Exception e) {
            log.error("根据省份ID获取城市列表失败，provinceId: {}", provinceId, e);
            throw new RuntimeException("获取城市列表失败");
        }
    }
}
