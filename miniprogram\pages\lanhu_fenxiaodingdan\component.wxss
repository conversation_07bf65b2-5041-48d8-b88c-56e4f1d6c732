.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1796rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.block_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 264rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.group_1 {
  width: 694rpx;
  height: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 0 30rpx;
}
.image-text_1 {
  width: 56rpx;
  height: 116rpx;
  margin-top: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-left: 8rpx;
}
.text-group_1 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 42rpx;
}
.text_1 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 58rpx;
}
.box_1 {
  width: 226rpx;
  height: 128rpx;
  margin-left: 58rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 26rpx;
}
.text-wrapper_1 {
  width: 226rpx;
  height: 40rpx;
  margin-top: 44rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_3 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_4 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_5 {
  width: 154rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 58rpx;
}
.group_2 {
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: row;
  margin: 12rpx 0 8rpx 34rpx;
}
.group_3 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 2rpx;
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
}
.block_2 {
  width: 750rpx;
  height: 1534rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.box_2 {
  width: 690rpx;
  height: 1380rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.box_3 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_2 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_6 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_7 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_2 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_3 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_2 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_8 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_9 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text_10 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text-wrapper_3 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_11 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_12 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_13 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_4 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_4 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_14 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_15 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_3 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_4 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_3 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_16 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_17 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text-wrapper_5 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text_18 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_19 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(238, 116, 53, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_6 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_20 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_21 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_22 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_5 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_7 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_23 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_24 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_4 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_5 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_4 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_25 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_26 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text-wrapper_8 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text_27 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_28 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(238, 116, 53, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_9 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_29 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_30 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_31 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_10 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_32 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_33 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(134,144,156,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_5 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_6 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_5 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_34 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_35 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text-wrapper_11 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text_36 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_37 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(238, 116, 53, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_12 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_38 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_39 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_40 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_7 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 78rpx 0 38rpx 232rpx;
}