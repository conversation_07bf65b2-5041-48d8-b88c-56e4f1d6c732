package com.tourism.miniprogram.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.tourism.miniprogram.config.FileUploadConfig;
import com.tourism.miniprogram.config.TencentCosConfig;
import com.tourism.miniprogram.dto.FileUploadResult;
import com.tourism.miniprogram.exception.BusinessException;
import com.tourism.miniprogram.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * 文件服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private COSClient cosClient;

    @Autowired
    private TencentCosConfig cosConfig;

    @Autowired
    private FileUploadConfig uploadConfig;

    @Override
    public FileUploadResult uploadFile(MultipartFile file) {
        try {
            // 验证文件
            validateFile(file);
            
            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());

            // 上传文件
            return uploadFile(file.getInputStream(), fileName, file.getContentType(), file.getSize());
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new BusinessException("文件上传失败");
        }
    }

    @Override
    public FileUploadResult uploadFile(InputStream inputStream, String fileName, String contentType, long fileSize) {
        try {
            String filePath = uploadConfig.getPathPrefix() + fileName;
            
            // 创建上传请求
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(fileSize);
            metadata.setContentType(contentType);
            
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                cosConfig.getBucketName(), filePath, inputStream, metadata);
            
            // 执行上传
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功: {}, ETag: {}", filePath, result.getETag());
            
            // 构建返回结果
            return buildUploadResult(fileName, filePath, contentType, fileSize);
            
        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String filePath) {
        try {
            cosClient.deleteObject(cosConfig.getBucketName(), filePath);
            log.info("文件删除成功: {}", filePath);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String generatePresignedUrl(String filePath, long expireTime) {
        try {
            Date expiration = new Date(System.currentTimeMillis() + expireTime * 1000);
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                cosConfig.getBucketName(), filePath, HttpMethodName.GET);
            request.setExpiration(expiration);
            
            URL url = cosClient.generatePresignedUrl(request);
            return url.toString();
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", e.getMessage(), e);
            throw new BusinessException("生成下载链接失败");
        }
    }

    @Override
    public String generatePresignedUrl(String filePath) {
        return generatePresignedUrl(filePath, cosConfig.getUrlExpireTime());
    }

    @Override
    public boolean fileExists(String filePath) {
        try {
            cosClient.getObjectMetadata(cosConfig.getBucketName(), filePath);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public FileUploadResult getFileInfo(String filePath) {
        try {
            ObjectMetadata metadata = cosClient.getObjectMetadata(cosConfig.getBucketName(), filePath);
            
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String fileExtension = getFileExtension(fileName);
            
            return FileUploadResult.builder()
                .fileName(fileName)
                .filePath(filePath)
                .fileSize(metadata.getContentLength())
                .contentType(metadata.getContentType())
                .fileExtension(fileExtension)
                .fileUrl(getFileUrl(filePath))
                .cdnUrl(getCdnUrl(filePath))
                .uploadTime(metadata.getLastModified().getTime())
                .build();
                
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", e.getMessage(), e);
            throw new BusinessException("获取文件信息失败");
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
        
        if (file.getSize() > uploadConfig.getMaxSize()) {
            throw new BusinessException("文件大小超过限制");
        }
        
        String fileExtension = getFileExtension(file.getOriginalFilename());
        if (!uploadConfig.isAllowedFileType(fileExtension)) {
            throw new BusinessException("不支持的文件格式");
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String originalFilename) {
        String fileExtension = getFileExtension(originalFilename);
        String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
        String uuid = IdUtil.simpleUUID().substring(0, 8);
        return String.format("%s_%s_%s.%s", getFileTypePrefix(fileExtension), timestamp, uuid, fileExtension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename) || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 根据文件类型获取前缀
     */
    private String getFileTypePrefix(String fileExtension) {
        if (uploadConfig.isAllowedImageType(fileExtension)) {
            return "image";
        } else if (uploadConfig.isAllowedVideoType(fileExtension)) {
            return "video";
        } else if (uploadConfig.isAllowedAudioType(fileExtension)) {
            return "audio";
        }
        return "file";
    }

    /**
     * 构建上传结果
     */
    private FileUploadResult buildUploadResult(String fileName, String filePath, String contentType, long fileSize) {
        String fileExtension = getFileExtension(fileName);
        
        return FileUploadResult.builder()
            .fileName(fileName)
            .filePath(filePath)
            .fileSize(fileSize)
            .contentType(contentType)
            .fileExtension(fileExtension)
            .fileUrl(getFileUrl(filePath))
            .cdnUrl(getCdnUrl(filePath))
            .uploadTime(System.currentTimeMillis())
            .build();
    }

    /**
     * 获取文件访问URL
     */
    private String getFileUrl(String filePath) {
        return String.format("https://%s.cos.%s.myqcloud.com/%s", 
            cosConfig.getBucketName(), cosConfig.getRegion(), filePath);
    }

    /**
     * 获取CDN访问URL
     */
    private String getCdnUrl(String filePath) {
        if (StrUtil.isNotBlank(cosConfig.getCdnDomain())) {
            return cosConfig.getCdnDomain() + "/" + filePath;
        }
        return getFileUrl(filePath);
    }
}
