package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Feedback;

import java.util.List;

/**
 * 意见反馈服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface FeedbackService extends IService<Feedback> {

    /**
     * 根据用户ID获取反馈列表
     *
     * @param userId 用户ID
     * @return 反馈列表
     */
    List<Feedback> getFeedbacksByUserId(Integer userId);

    /**
     * 根据状态获取反馈列表
     *
     * @param status 状态
     * @return 反馈列表
     */
    List<Feedback> getFeedbacksByStatus(String status);

    /**
     * 获取待处理的反馈数量
     *
     * @return 待处理数量
     */
    Integer getPendingFeedbackCount();

    /**
     * 根据关键词搜索反馈
     *
     * @param keyword 关键词
     * @return 反馈列表
     */
    List<Feedback> searchFeedbacks(String keyword);

    /**
     * 回复反馈
     *
     * @param feedbackId 反馈ID
     * @param reply 回复内容
     * @return 是否成功
     */
    boolean replyFeedback(Integer feedbackId, String reply);

    /**
     * 更新反馈状态
     *
     * @param feedbackId 反馈ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateFeedbackStatus(Integer feedbackId, String status);

    /**
     * 批量更新反馈状态
     *
     * @param feedbackIds 反馈ID列表
     * @param status 新状态
     * @return 是否成功
     */
    boolean batchUpdateFeedbackStatus(List<Integer> feedbackIds, String status);
}
