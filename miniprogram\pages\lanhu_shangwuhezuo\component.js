const businessCooperationService = require('../../utils/businessCooperationService');
const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    cooperationList: [],        // 合作申请列表
    showCreateModal: false,     // 显示创建弹窗
    isSubmitting: false,        // 提交状态
    formData: {                 // 表单数据
      name: '',
      phone: '',
      content: ''
    },
    pagination: {               // 分页信息
      current: 1,
      size: 10,
      total: 0,
      hasMore: true
    },
    loading: false,             // 加载状态
    userInfo: null              // 用户信息
  },

  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("商务合作页面加载");
      this.initPage();
    },
    detached: function () {
      console.info("商务合作页面卸载");
    },
  },

  methods: {
    // 初始化页面
    async initPage() {
      try {
        // 获取用户信息
        const userInfo = await userService.getUserInfo();
        this.setData({
          userInfo: userInfo,
          'formData.name': userInfo.nickname || '',
          'formData.phone': userInfo.phone || ''
        });

        // 加载合作申请列表
        await this.loadCooperationList();
      } catch (error) {
        console.error('初始化页面失败:', error);
      }
    },

    // 加载合作申请列表
    async loadCooperationList(refresh = false) {
      if (this.data.loading) return;

      try {
        this.setData({ loading: true });

        const current = refresh ? 1 : this.data.pagination.current;
        const response = await businessCooperationService.getCooperationList(this.data.userInfo.id, {
          current: current,
          size: this.data.pagination.size
        });

        if (response.code === 200) {
          const newList = response.data.records || [];
          const cooperationList = refresh ? newList : [...this.data.cooperationList, ...newList];

          this.setData({
            cooperationList: cooperationList,
            'pagination.current': current,
            'pagination.total': response.data.total || 0,
            'pagination.hasMore': cooperationList.length < (response.data.total || 0)
          });
        }
      } catch (error) {
        console.error('加载合作申请列表失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 创建合作申请
    onCreateCooperation() {
      this.setData({ showCreateModal: true });
    },

    // 关闭弹窗
    onCloseModal() {
      this.setData({
        showCreateModal: false,
        formData: {
          name: this.data.userInfo?.nickname || '',
          phone: this.data.userInfo?.phone || '',
          content: ''
        }
      });
    },

    // 阻止事件冒泡
    stopPropagation() {},

    // 表单输入事件
    onNameInput(e) {
      this.setData({
        'formData.name': e.detail.value
      });
    },

    onPhoneInput(e) {
      this.setData({
        'formData.phone': e.detail.value
      });
    },

    onContentInput(e) {
      this.setData({
        'formData.content': e.detail.value
      });
    },

    // 提交合作申请
    async onSubmitCooperation() {
      const { name, phone, content } = this.data.formData;

      // 表单验证
      if (!name.trim()) {
        wx.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return;
      }

      if (!phone.trim()) {
        wx.showToast({
          title: '请输入电话',
          icon: 'none'
        });
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(phone)) {
        wx.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }

      if (!content.trim()) {
        wx.showToast({
          title: '请输入合作内容',
          icon: 'none'
        });
        return;
      }

      try {
        this.setData({ isSubmitting: true });

        const submitData = {
          name: name.trim(),
          phone: phone.trim(),
          content: content.trim(),
          userId: this.data.userInfo.id
        };

        const response = await businessCooperationService.createCooperation(submitData);

        if (response.code === 200) {
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });

          this.onCloseModal();
          await this.loadCooperationList(true); // 刷新列表
        } else {
          throw new Error(response.message || '提交失败');
        }
      } catch (error) {
        console.error('提交合作申请失败:', error);
        wx.showToast({
          title: error.message || '提交失败',
          icon: 'none'
        });
      } finally {
        this.setData({ isSubmitting: false });
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const now = new Date();
      const diff = now - date;

      if (diff < 60000) { // 1分钟内
        return '刚刚';
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
      } else {
        return date.toLocaleDateString();
      }
    }
  }
});