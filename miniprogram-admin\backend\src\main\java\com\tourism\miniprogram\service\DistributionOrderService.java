package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.DistributionOrder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销订单服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface DistributionOrderService extends IService<DistributionOrder> {

    /**
     * 根据分销员ID获取分销订单列表
     *
     * @param distributorId 分销员ID
     * @return 分销订单列表
     */
    List<DistributionOrder> getByDistributorId(Integer distributorId);

    /**
     * 根据订单ID获取分销订单
     *
     * @param orderId 订单ID
     * @return 分销订单
     */
    DistributionOrder getByOrderId(Integer orderId);

    /**
     * 根据状态获取分销订单列表
     *
     * @param status 结算状态
     * @return 分销订单列表
     */
    List<DistributionOrder> getByStatus(String status);

    /**
     * 根据分销员ID和状态获取分销订单列表
     *
     * @param distributorId 分销员ID
     * @param status 结算状态
     * @return 分销订单列表
     */
    List<DistributionOrder> getByDistributorIdAndStatus(Integer distributorId, String status);

    /**
     * 创建分销订单记录
     *
     * @param distributorId 分销员ID
     * @param orderId 订单ID
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @param baseRate 基础佣金比例
     * @return 分销订单
     */
    DistributionOrder createDistributionOrder(Integer distributorId, Integer orderId, 
                                            BigDecimal orderAmount, BigDecimal commissionRate, BigDecimal baseRate);

    /**
     * 结算分销订单
     *
     * @param distributionOrderId 分销订单ID
     * @return 是否成功
     */
    boolean settleDistributionOrder(Integer distributionOrderId);

    /**
     * 批量结算分销订单
     *
     * @param distributionOrderIds 分销订单ID列表
     * @return 结算成功的数量
     */
    int batchSettleDistributionOrders(List<Integer> distributionOrderIds);

    /**
     * 取消分销订单
     *
     * @param distributionOrderId 分销订单ID
     * @return 是否成功
     */
    boolean cancelDistributionOrder(Integer distributionOrderId);

    /**
     * 获取分销员待结算佣金总额
     *
     * @param distributorId 分销员ID
     * @return 待结算佣金总额
     */
    BigDecimal getPendingCommissionAmount(Integer distributorId);

    /**
     * 获取分销员已结算佣金总额
     *
     * @param distributorId 分销员ID
     * @return 已结算佣金总额
     */
    BigDecimal getSettledCommissionAmount(Integer distributorId);

    /**
     * 获取分销员总佣金金额
     *
     * @param distributorId 分销员ID
     * @return 总佣金金额
     */
    BigDecimal getTotalCommissionAmount(Integer distributorId);

    /**
     * 获取分销员订单数量统计
     *
     * @param distributorId 分销员ID
     * @return 订单数量
     */
    Integer getOrderCountByDistributorId(Integer distributorId);

    /**
     * 获取系统总分销订单数量
     *
     * @return 总分销订单数量
     */
    Integer getTotalDistributionOrderCount();

    /**
     * 获取系统总分销佣金金额
     *
     * @return 总分销佣金金额
     */
    BigDecimal getTotalSystemCommissionAmount();

    /**
     * 检查订单是否已存在分销记录
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    boolean hasDistributionRecord(Integer orderId);

    /**
     * 计算订单佣金金额
     *
     * @param orderAmount 订单金额
     * @param commissionRate 佣金比例
     * @return 佣金金额
     */
    BigDecimal calculateCommissionAmount(BigDecimal orderAmount, BigDecimal commissionRate);

    /**
     * 处理订单分销逻辑
     * 当有新订单时，检查是否需要创建分销记录
     *
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param orderAmount 订单金额
     * @return 是否创建了分销记录
     */
    boolean processOrderDistribution(Integer orderId, Integer userId, BigDecimal orderAmount);
}
