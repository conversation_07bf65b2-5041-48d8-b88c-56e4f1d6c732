<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const key = computed(() => route.path)
</script>

<style lang="scss" scoped>
.app-main {
  min-height: calc(100vh - 50px);
  width: calc(100% - 210px);
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  margin-top: 20px;
  margin-left: 20px;
  transition: margin-left 0.28s, width 0.28s;
}

// 当侧边栏收起时的样式
.hideSidebar .app-main {
  margin-left: 54px;
  width: calc(100% - 54px);
}

// 移动端样式
.mobile .app-main {
  margin-left: 0;
  width: 100%;
}

.fixed-header + .app-main {
  padding-top: 70px;
}

.hasTagsView .app-main {
  min-height: calc(100vh - 84px);
}

.hasTagsView .fixed-header + .app-main {
  padding-top: 104px;
}
</style>
