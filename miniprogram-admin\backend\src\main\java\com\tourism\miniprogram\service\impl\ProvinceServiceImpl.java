package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Province;
import com.tourism.miniprogram.mapper.ProvinceMapper;
import com.tourism.miniprogram.service.ProvinceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 省份服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class ProvinceServiceImpl extends ServiceImpl<ProvinceMapper, Province> implements ProvinceService {

    @Override
    public List<Province> getEnabledProvinces() {
        try {
            return baseMapper.selectEnabledProvinces();
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            throw new RuntimeException("获取省份列表失败");
        }
    }
}
