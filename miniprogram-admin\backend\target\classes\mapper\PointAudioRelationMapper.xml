<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.PointAudioRelationMapper">

    <!-- 根据讲解点ID获取音频关系列表 -->
    <select id="selectByPointId" resultType="com.tourism.miniprogram.entity.PointAudioRelation">
        SELECT relation_id, point_id, audio_id, sort_order, created_at
        FROM point_audio_relation
        WHERE point_id = #{pointId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 根据讲解点ID获取音频关系详细列表 -->
    <select id="selectByPointIdWithDetails" resultType="com.tourism.miniprogram.dto.PointAudioRelationDTO">
        SELECT
            par.relation_id as relationId,
            par.point_id as pointId,
            par.audio_id as audioId,
            par.sort_order as sortOrder,
            par.created_at as createdAt,
            ep.point_name as pointName,
            ea.audio_name as audioName,
            ea.audio_url as audioUrl,
            ea.audio_image as audioImage,
            ea.duration as duration,
            ea.audio_address as audioAddress
        FROM point_audio_relation par
        LEFT JOIN explanation_point ep ON par.point_id = ep.point_id
        LEFT JOIN explanation_audio ea ON par.audio_id = ea.audio_id
        WHERE par.point_id = #{pointId}
        ORDER BY par.sort_order ASC, par.relation_id DESC
    </select>

    <!-- 根据音频ID获取讲解点关系列表 -->
    <select id="selectByAudioId" resultType="com.tourism.miniprogram.entity.PointAudioRelation">
        SELECT relation_id, point_id, audio_id, sort_order, created_at
        FROM point_audio_relation
        WHERE audio_id = #{audioId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 批量插入关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO point_audio_relation (point_id, audio_id, sort_order)
        VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.pointId}, #{item.audioId}, #{item.sortOrder})
        </foreach>
    </insert>

    <!-- 根据讲解点ID删除所有关系 -->
    <delete id="deleteByPointId">
        DELETE FROM point_audio_relation WHERE point_id = #{pointId}
    </delete>

    <!-- 根据音频ID删除所有关系 -->
    <delete id="deleteByAudioId">
        DELETE FROM point_audio_relation WHERE audio_id = #{audioId}
    </delete>

    <!-- 更新排序 -->
    <update id="updateSortOrder">
        UPDATE point_audio_relation 
        SET sort_order = #{sortOrder}
        WHERE relation_id = #{relationId}
    </update>

</mapper>
