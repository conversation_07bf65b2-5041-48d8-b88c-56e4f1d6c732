package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.BusinessCooperation;
import com.tourism.miniprogram.mapper.BusinessCooperationMapper;
import com.tourism.miniprogram.service.BusinessCooperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商务合作服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class BusinessCooperationServiceImpl extends ServiceImpl<BusinessCooperationMapper, BusinessCooperation> implements BusinessCooperationService {

    @Override
    public List<BusinessCooperation> getCooperationsByUserId(Integer userId) {
        try {
            return baseMapper.selectCooperationsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取合作申请列表失败，userId: {}", userId, e);
            throw new RuntimeException("获取合作申请列表失败");
        }
    }



    @Override
    public List<BusinessCooperation> searchCooperations(String keyword) {
        try {
            return baseMapper.searchCooperations(keyword);
        } catch (Exception e) {
            log.error("搜索合作申请失败，keyword: {}", keyword, e);
            throw new RuntimeException("搜索合作申请失败");
        }
    }


}
