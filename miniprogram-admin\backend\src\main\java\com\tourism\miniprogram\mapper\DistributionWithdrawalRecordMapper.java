package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.DistributionWithdrawalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销提现记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface DistributionWithdrawalRecordMapper extends BaseMapper<DistributionWithdrawalRecord> {

    /**
     * 根据分销员ID获取提现记录列表
     *
     * @param distributorId 分销员ID
     * @return 提现记录列表
     */
    @Select("SELECT * FROM distribution_withdrawal_records WHERE distributor_id = #{distributorId} ORDER BY created_at DESC")
    List<DistributionWithdrawalRecord> selectByDistributorId(Integer distributorId);

    /**
     * 根据状态获取提现记录列表
     *
     * @param status 提现状态
     * @return 提现记录列表
     */
    @Select("SELECT * FROM distribution_withdrawal_records WHERE status = #{status} ORDER BY created_at DESC")
    List<DistributionWithdrawalRecord> selectByStatus(String status);

    /**
     * 根据分销员ID和状态获取提现记录列表
     *
     * @param distributorId 分销员ID
     * @param status 提现状态
     * @return 提现记录列表
     */
    @Select("SELECT * FROM distribution_withdrawal_records WHERE distributor_id = #{distributorId} AND status = #{status} ORDER BY created_at DESC")
    List<DistributionWithdrawalRecord> selectByDistributorIdAndStatus(@Param("distributorId") Integer distributorId,
                                                                     @Param("status") String status);

    /**
     * 获取分销员待处理提现金额总计
     *
     * @param distributorId 分销员ID
     * @return 待处理提现金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM distribution_withdrawal_records WHERE distributor_id = #{distributorId} AND status = 'pending'")
    BigDecimal getPendingWithdrawalAmount(Integer distributorId);

    /**
     * 获取分销员已完成提现金额总计
     *
     * @param distributorId 分销员ID
     * @return 已完成提现金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM distribution_withdrawal_records WHERE distributor_id = #{distributorId} AND status = 'completed'")
    BigDecimal getCompletedWithdrawalAmount(Integer distributorId);

    /**
     * 根据交易流水号获取提现记录
     *
     * @param transactionNo 交易流水号
     * @return 提现记录
     */
    @Select("SELECT * FROM distribution_withdrawal_records WHERE transaction_no = #{transactionNo}")
    DistributionWithdrawalRecord selectByTransactionNo(String transactionNo);

    /**
     * 获取系统总提现金额统计
     *
     * @return 总提现金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM distribution_withdrawal_records WHERE status = 'completed'")
    BigDecimal getTotalWithdrawalAmount();

    /**
     * 获取待处理提现记录数量
     *
     * @return 待处理提现记录数量
     */
    @Select("SELECT COUNT(*) FROM distribution_withdrawal_records WHERE status = 'pending'")
    Integer countPendingWithdrawals();
}
