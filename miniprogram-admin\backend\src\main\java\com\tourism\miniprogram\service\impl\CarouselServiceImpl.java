package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Carousel;
import com.tourism.miniprogram.mapper.CarouselMapper;
import com.tourism.miniprogram.service.CarouselService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 轮播图服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class CarouselServiceImpl extends ServiceImpl<CarouselMapper, Carousel> implements CarouselService {

    @Override
    public List<Carousel> getCarouselsByCondition(Integer provinceId) {
        try {
            return baseMapper.selectCarouselsByCondition(provinceId);
        } catch (Exception e) {
            log.error("获取轮播图列表失败，provinceId: {}", provinceId, e);
            throw new RuntimeException("获取轮播图列表失败");
        }
    }
}
