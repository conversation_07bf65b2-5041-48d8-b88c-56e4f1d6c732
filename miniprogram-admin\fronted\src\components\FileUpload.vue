<template>
  <div class="file-upload">
    <el-upload
      :class="uploadClass"
      action="#"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :http-request="handleUpload"
      :disabled="loading"
      :accept="accept"
    >
      <!-- 图片预览 -->
      <div v-if="fileType === 'image' && modelValue" class="file-preview">
        <img :src="modelValue" :alt="fileName" />
        <div class="file-actions">
          <el-button type="primary" size="small" @click.stop="previewFile">预览</el-button>
          <el-button type="danger" size="small" @click.stop="removeFile">删除</el-button>
        </div>
      </div>
      
      <!-- 其他文件预览 -->
      <div v-else-if="modelValue" class="file-info">
        <el-icon class="file-icon"><Document /></el-icon>
        <div class="file-details">
          <div class="file-name">{{ fileName }}</div>
          <div class="file-size">{{ formatFileSize(fileSize) }}</div>
        </div>
        <div class="file-actions">
          <el-button type="primary" size="small" @click.stop="downloadFile">下载</el-button>
          <el-button type="danger" size="small" @click.stop="removeFile">删除</el-button>
        </div>
      </div>
      
      <!-- 上传中状态 -->
      <div v-else-if="loading" class="upload-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <div>{{ loadingText }}</div>
        <div v-if="progress > 0" class="progress-text">{{ progress }}%</div>
      </div>
      
      <!-- 上传按钮 -->
      <div v-else class="upload-trigger">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">{{ uploadText }}</div>
      </div>
    </el-upload>
    
    <div v-if="tip" class="upload-tip">{{ tip }}</div>
    
    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="80%" center>
      <img :src="modelValue" style="width: 100%; height: auto;" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading, Document } from '@element-plus/icons-vue'
import { uploadFile, uploadImage, uploadVideo, uploadAudio } from '@/api/upload'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: 'image', // image, video, audio, file
    validator: (value) => ['image', 'video', 'audio', 'file'].includes(value)
  },
  maxSize: {
    type: Number,
    default: 2 // MB
  },
  accept: {
    type: String,
    default: ''
  },
  uploadText: {
    type: String,
    default: '点击上传'
  },
  tip: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: 300
  },
  height: {
    type: [String, Number],
    default: 100
  }
})

const emit = defineEmits(['update:modelValue', 'success', 'error'])

// 响应式数据
const loading = ref(false)
const progress = ref(0)
const loadingText = ref('上传中...')
const previewVisible = ref(false)
const fileName = ref('')
const fileSize = ref(0)

// 计算属性
const uploadClass = computed(() => {
  return [
    'file-uploader',
    `file-uploader--${props.fileType}`,
    { 'is-loading': loading.value }
  ]
})

// 上传前验证
const beforeUpload = (file) => {
  // 文件大小验证
  const maxSizeBytes = props.maxSize * 1024 * 1024
  if (file.size > maxSizeBytes) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 文件类型验证
  if (props.fileType === 'image') {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持上传 JPG、PNG、GIF、WebP 格式的图片')
      return false
    }
  } else if (props.fileType === 'video') {
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持上传 MP4、AVI、MOV、WMV、FLV 格式的视频')
      return false
    }
  } else if (props.fileType === 'audio') {
    const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac']
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持上传 MP3、WAV、OGG、AAC、FLAC 格式的音频')
      return false
    }
  }

  return true
}

// 文件上传
const handleUpload = async (options) => {
  const { file } = options
  
  try {
    loading.value = true
    progress.value = 0
    fileName.value = file.name
    fileSize.value = file.size
    
    // 调用上传API
    let fileUrl
    if (props.fileType === 'image') {
      fileUrl = await uploadImage(file, (progressValue) => {
        progress.value = progressValue
        loadingText.value = `上传中... ${progressValue}%`
      })
    } else if (props.fileType === 'video') {
      fileUrl = await uploadVideo(file, (progressValue) => {
        progress.value = progressValue
        loadingText.value = `上传中... ${progressValue}%`
      })
    } else if (props.fileType === 'audio') {
      fileUrl = await uploadAudio(file, (progressValue) => {
        progress.value = progressValue
        loadingText.value = `上传中... ${progressValue}%`
      })
    } else {
      // 对于通用文件上传，仍然使用uploadFile函数
      const response = await uploadFile(file, (progressValue) => {
        progress.value = progressValue
        loadingText.value = `上传中... ${progressValue}%`
      })

      if (response.code === 200 && response.data) {
        fileUrl = response.data.cdnUrl || response.data.fileUrl || response.data.fileName
      } else {
        throw new Error(response.message || '上传失败')
      }
    }

    // 设置文件URL
    emit('update:modelValue', fileUrl)
    emit('success', { fileUrl })
    ElMessage.success('文件上传成功')
  } catch (error) {
    console.error('文件上传失败:', error)
    emit('error', error)
    ElMessage.error(error.message || '文件上传失败，请重试')
  } finally {
    loading.value = false
    progress.value = 0
    loadingText.value = '上传中...'
  }
}

// 删除文件
const removeFile = () => {
  emit('update:modelValue', '')
  fileName.value = ''
  fileSize.value = 0
}

// 预览文件
const previewFile = () => {
  if (props.fileType === 'image') {
    previewVisible.value = true
  } else {
    window.open(props.modelValue, '_blank')
  }
}

// 下载文件
const downloadFile = () => {
  const link = document.createElement('a')
  link.href = props.modelValue
  link.download = fileName.value || 'download'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-upload {
  display: inline-block;
}

.file-uploader {
  display: inline-block;
}

.file-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: v-bind(width + 'px');
  min-height: v-bind(height + 'px');
}

.file-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.file-uploader.is-loading .el-upload {
  cursor: not-allowed;
}

.file-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.file-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-preview .file-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.file-preview:hover .file-actions {
  opacity: 1;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 12px;
  width: 100%;
}

.file-icon {
  font-size: 32px;
  color: #409eff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
  width: 100%;
  height: 100%;
}

.upload-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.progress-text {
  margin-top: 4px;
  font-size: 12px;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #8c939d;
  font-size: 14px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
  text-align: center;
}
</style>
