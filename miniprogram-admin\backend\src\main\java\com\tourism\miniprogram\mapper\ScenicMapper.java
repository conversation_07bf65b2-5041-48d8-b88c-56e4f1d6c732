package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.entity.Scenic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 景区Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface ScenicMapper extends BaseMapper<Scenic> {

    /**
     * 分页查询推荐景区列表
     *
     * @param page       分页对象
     * @param provinceId 省份ID
     * @param cityId     城市ID
     * @return 景区分页列表
     */
    IPage<Scenic> selectRecommendScenics(Page<Scenic> page, 
                                        @Param("provinceId") Integer provinceId, 
                                        @Param("cityId") Integer cityId);

    /**
     * 根据景区ID获取景区详情
     *
     * @param scenicId 景区ID
     * @return 景区详情
     */
    Scenic selectByScenicId(@Param("scenicId") String scenicId);
}
