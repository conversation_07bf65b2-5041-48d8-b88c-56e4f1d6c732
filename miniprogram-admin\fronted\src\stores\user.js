import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('token') || '')

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除token
  const clearToken = () => {
    token.value = ''
    localStorage.removeItem('token')
    userInfo.value = null
  }

  // 获取用户信息
  const fetchUserInfo = async (userId) => {
    try {
      const response = await getUserInfo(userId)
      if (response.code === 200) {
        userInfo.value = response.data
        return response.data
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 登出
  const logout = () => {
    clearToken()
    // 可以在这里添加其他登出逻辑
  }

  return {
    userInfo,
    token,
    setToken,
    clearToken,
    fetchUserInfo,
    logout
  }
})
