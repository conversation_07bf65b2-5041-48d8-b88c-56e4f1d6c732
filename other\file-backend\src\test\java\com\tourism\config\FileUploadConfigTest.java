package com.tourism.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件上传配置测试类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootTest
@SpringJUnitConfig
public class FileUploadConfigTest {

    @Test
    public void testFileUploadConfig() {
        FileUploadConfig config = new FileUploadConfig();
        
        // 测试默认配置
        assertNotNull(config.getImageTypeList());
        assertNotNull(config.getVideoTypeList());
        assertNotNull(config.getAudioTypeList());
        
        // 测试图片格式检查
        assertTrue(config.isAllowedImageType("jpg"));
        assertTrue(config.isAllowedImageType("png"));
        assertFalse(config.isAllowedImageType("mp3"));
        
        // 测试视频格式检查
        assertTrue(config.isAllowedVideoType("mp4"));
        assertTrue(config.isAllowedVideoType("avi"));
        assertFalse(config.isAllowedVideoType("jpg"));
        
        // 测试音频格式检查
        assertTrue(config.isAllowedAudioType("mp3"));
        assertTrue(config.isAllowedAudioType("wav"));
        assertTrue(config.isAllowedAudioType("flac"));
        assertTrue(config.isAllowedAudioType("aac"));
        assertTrue(config.isAllowedAudioType("ogg"));
        assertTrue(config.isAllowedAudioType("wma"));
        assertTrue(config.isAllowedAudioType("m4a"));
        assertFalse(config.isAllowedAudioType("jpg"));
        
        // 测试综合文件格式检查
        assertTrue(config.isAllowedFileType("jpg"));  // 图片
        assertTrue(config.isAllowedFileType("mp4"));  // 视频
        assertTrue(config.isAllowedFileType("mp3"));  // 音频
        assertFalse(config.isAllowedFileType("txt")); // 不支持的格式
    }

    @Test
    public void testCaseInsensitiveCheck() {
        FileUploadConfig config = new FileUploadConfig();
        
        // 测试大小写不敏感
        assertTrue(config.isAllowedAudioType("MP3"));
        assertTrue(config.isAllowedAudioType("WAV"));
        assertTrue(config.isAllowedImageType("JPG"));
        assertTrue(config.isAllowedVideoType("MP4"));
    }
}
