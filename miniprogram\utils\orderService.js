// 订单服务模块
const httpService = require('./httpService');

class OrderService {
  constructor() {
    this.cacheKeyPrefix = 'orders_cache_';
    this.cacheTime = 1 * 60 * 1000; // 1分钟缓存
  }

  // 创建订单
  async createOrder(orderData) {
    try {
      if (!orderData.userId) {
        throw new Error('用户ID不能为空');
      }
      if (!orderData.totalAmount) {
        throw new Error('订单金额不能为空');
      }

      const order = await httpService.post('/api/orders', {
        userId: orderData.userId,
        totalAmount: orderData.totalAmount,
        status: 'pending',
        orderNo: this.generateOrderNo(),
        scenicId: orderData.scenicId,
        productId: orderData.productId,
        quantity: orderData.quantity || 1,
        productName: orderData.productName,
        productPrice: orderData.productPrice,
        remark: orderData.remark || ''
      }, {
        loadingText: '创建订单中...'
      });

      return order;
    } catch (error) {
      console.error('创建订单失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 支付订单
  async updateOrderStatus(orderId, status) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }
      if (!status) {
        throw new Error('订单状态不能为空');
      }

      console.log('更新订单状态:', orderId, status);

      const result = await httpService.post(`/api/orders/${orderId}/pay`, {
      }, {
        showLoading: false
      });

      console.log('订单状态更新成功:', result);
      return result;
    } catch (error) {
      console.error('更新订单状态失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取订单详情
  async getOrderDetail(orderId) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      const order = await httpService.get(`/api/orders/${orderId}`, {}, {
        loadingText: '加载订单详情中...'
      });

      return order;
    } catch (error) {
      console.error('获取订单详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取用户订单列表
  async getUserOrders(userId, params = {}) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }

      const {
        current = 1,
        size = 20,
        status = ''
      } = params;

      const queryParams = {
        userId: userId.toString(),
        current: current.toString(),
        size: size.toString()
      };

      if (status) {
        queryParams.status = status;
      }

      const orders = await httpService.get('/api/orders/page', queryParams, {
        loadingText: '加载订单列表中...'
      });

      return orders;
    } catch (error) {
      console.error('获取用户订单列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 生成订单号
  generateOrderNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${year}${month}${day}${hour}${minute}${second}${random}`;
  }

  // 模拟支付处理
  async processPayment(orderId, paymentData) {
    try {
      console.log('处理支付:', orderId, paymentData);

      // 模拟支付延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟支付成功（实际项目中这里会调用真实的支付接口）
      const paymentResult = {
        success: true,
        transactionId: this.generateTransactionId(),
        paymentTime: new Date().toISOString(),
        amount: paymentData.amount
      };

      console.log('支付处理完成:', paymentResult);
      return paymentResult;
    } catch (error) {
      console.error('支付处理失败:', error);
      throw error;
    }
  }

  // 生成交易号
  generateTransactionId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `TXN${timestamp}${random}`;
  }

  // 清除缓存
  clearCache() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          wx.removeStorageSync(key);
        }
      });
      console.log('订单缓存已清除');
    } catch (error) {
      console.error('清除订单缓存失败:', error);
    }
  }
}

// 创建单例实例
const orderService = new OrderService();

module.exports = orderService;
