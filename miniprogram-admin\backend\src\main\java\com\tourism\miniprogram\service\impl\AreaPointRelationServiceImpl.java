package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.dto.AreaPointRelationDTO;
import com.tourism.miniprogram.entity.AreaPointRelation;
import com.tourism.miniprogram.mapper.AreaPointRelationMapper;
import com.tourism.miniprogram.service.AreaPointRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 区域讲解点关系服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Slf4j
@Service
public class AreaPointRelationServiceImpl extends ServiceImpl<AreaPointRelationMapper, AreaPointRelation> implements AreaPointRelationService {

    @Override
    public List<AreaPointRelation> getByAreaId(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.selectByAreaId(areaId);
        } catch (Exception e) {
            log.error("根据区域ID获取讲解点关系列表失败，areaId: {}", areaId, e);
            throw new RuntimeException("获取讲解点关系列表失败");
        }
    }

    @Override
    public List<AreaPointRelationDTO> getByAreaIdWithDetails(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.selectByAreaIdWithDetails(areaId);
        } catch (Exception e) {
            log.error("根据区域ID获取讲解点关系详细列表失败，areaId: {}", areaId, e);
            throw new RuntimeException("获取讲解点关系详细列表失败");
        }
    }

    @Override
    public List<AreaPointRelation> getByPointId(Integer pointId) {
        try {
            if (pointId == null) {
                throw new IllegalArgumentException("讲解点ID不能为空");
            }
            return baseMapper.selectByPointId(pointId);
        } catch (Exception e) {
            log.error("根据讲解点ID获取区域关系列表失败，pointId: {}", pointId, e);
            throw new RuntimeException("获取区域关系列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRelation(Integer areaId, Integer pointId, Integer sortOrder) {
        try {
            if (areaId == null || pointId == null) {
                throw new IllegalArgumentException("区域ID和讲解点ID不能为空");
            }

            // 检查关系是否已存在
            QueryWrapper<AreaPointRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("area_id", areaId).eq("point_id", pointId);
            if (baseMapper.selectCount(queryWrapper) > 0) {
                throw new RuntimeException("该关系已存在");
            }

            AreaPointRelation relation = new AreaPointRelation();
            relation.setAreaId(areaId);
            relation.setPointId(pointId);
            relation.setSortOrder(sortOrder != null ? sortOrder : 0);

            return baseMapper.insert(relation) > 0;
        } catch (Exception e) {
            log.error("添加区域讲解点关系失败，areaId: {}, pointId: {}", areaId, pointId, e);
            throw new RuntimeException("添加关系失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRelation(Integer areaId, Integer pointId) {
        try {
            if (areaId == null || pointId == null) {
                throw new IllegalArgumentException("区域ID和讲解点ID不能为空");
            }

            QueryWrapper<AreaPointRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("area_id", areaId).eq("point_id", pointId);
            return baseMapper.delete(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("删除区域讲解点关系失败，areaId: {}, pointId: {}", areaId, pointId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddRelations(List<AreaPointRelation> relations) {
        try {
            if (relations == null || relations.isEmpty()) {
                throw new IllegalArgumentException("关系列表不能为空");
            }
            return baseMapper.batchInsert(relations) > 0;
        } catch (Exception e) {
            log.error("批量添加区域讲解点关系失败", e);
            throw new RuntimeException("批量添加关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByAreaId(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.deleteByAreaId(areaId) >= 0;
        } catch (Exception e) {
            log.error("根据区域ID删除关系失败，areaId: {}", areaId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByPointId(Integer pointId) {
        try {
            if (pointId == null) {
                throw new IllegalArgumentException("讲解点ID不能为空");
            }
            return baseMapper.deleteByPointId(pointId) >= 0;
        } catch (Exception e) {
            log.error("根据讲解点ID删除关系失败，pointId: {}", pointId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Integer relationId, Integer sortOrder) {
        try {
            if (relationId == null || sortOrder == null) {
                throw new IllegalArgumentException("关系ID和排序值不能为空");
            }
            return baseMapper.updateSortOrder(relationId, sortOrder) > 0;
        } catch (Exception e) {
            log.error("更新排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            throw new RuntimeException("更新排序失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders) {
        try {
            if (relationIds == null || sortOrders == null || relationIds.size() != sortOrders.size()) {
                throw new IllegalArgumentException("关系ID列表和排序值列表不能为空且长度必须一致");
            }

            for (int i = 0; i < relationIds.size(); i++) {
                baseMapper.updateSortOrder(relationIds.get(i), sortOrders.get(i));
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新排序失败", e);
            throw new RuntimeException("批量更新排序失败");
        }
    }
}
