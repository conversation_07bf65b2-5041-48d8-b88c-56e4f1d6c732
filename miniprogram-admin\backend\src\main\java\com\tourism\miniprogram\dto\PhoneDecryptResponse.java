package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信小程序手机号解密响应
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@ApiModel(description = "微信小程序手机号解密响应")
public class PhoneDecryptResponse {

    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phoneNumber;

    @ApiModelProperty(value = "纯手机号", example = "13800138000")
    private String purePhoneNumber;

    @ApiModelProperty(value = "国家代码", example = "86")
    private String countryCode;

    @ApiModelProperty(value = "用户ID", example = "123")
    private Integer userId;

    @ApiModelProperty(value = "是否绑定成功", example = "true")
    private Boolean bindSuccess;
}
