package com.tourism;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 旅游讲解小程序文件管理API系统启动类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
public class TourismFileManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(TourismFileManagementApplication.class, args);
        System.out.println("=================================");
        System.out.println("旅游讲解小程序文件管理API系统启动成功！");
        System.out.println("API文档地址: http://localhost:8080/api/doc.html");
        System.out.println("=================================");
    }
}
