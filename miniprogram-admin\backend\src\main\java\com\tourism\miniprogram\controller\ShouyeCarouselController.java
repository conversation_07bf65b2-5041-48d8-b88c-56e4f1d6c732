package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ShouyeCarousel;
import com.tourism.miniprogram.service.ShouyeCarouselService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 首页轮播图控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/shouye-carousels")
@Api(tags = "首页轮播图管理")
public class ShouyeCarouselController {

    @Autowired
    private ShouyeCarouselService shouyeCarouselService;

    /**
     * 获取首页轮播图列表
     *
     * @return 首页轮播图列表
     */
    @GetMapping
    @ApiOperation(value = "获取首页轮播图列表", notes = "获取所有首页轮播图")
    public Result<List<ShouyeCarousel>> list() {
        try {
            List<ShouyeCarousel> carousels = shouyeCarouselService.list();
            return Result.success(carousels);
        } catch (Exception e) {
            log.error("获取首页轮播图列表失败", e);
            return Result.error("获取首页轮播图列表失败");
        }
    }

    /**
     * 分页获取首页轮播图列表
     *
     * @param current 当前页码
     * @param size    每页大小
     * @return 分页结果
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取首页轮播图列表", notes = "分页获取首页轮播图列表")
    public Result<IPage<ShouyeCarousel>> page(
            @ApiParam(value = "当前页码", required = true) @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", required = true) @RequestParam(defaultValue = "10") Integer size) {
        try {
            Page<ShouyeCarousel> page = new Page<>(current, size);
            IPage<ShouyeCarousel> result = shouyeCarouselService.page(page);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页获取首页轮播图列表失败，current: {}, size: {}", current, size, e);
            return Result.error("分页获取首页轮播图列表失败");
        }
    }

    /**
     * 根据ID获取首页轮播图详情
     *
     * @param id 首页轮播图ID
     * @return 首页轮播图详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取首页轮播图详情", notes = "根据ID获取首页轮播图详情")
    public Result<ShouyeCarousel> getById(
            @ApiParam(value = "首页轮播图ID", required = true) @PathVariable Integer id) {
        try {
            ShouyeCarousel carousel = shouyeCarouselService.getById(id);
            if (carousel == null) {
                return Result.error("首页轮播图不存在");
            }
            return Result.success(carousel);
        } catch (Exception e) {
            log.error("获取首页轮播图详情失败，id: {}", id, e);
            return Result.error("获取首页轮播图详情失败");
        }
    }

    /**
     * 创建首页轮播图
     *
     * @param shouyeCarousel 首页轮播图信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建首页轮播图", notes = "创建新的首页轮播图")
    public Result<ShouyeCarousel> create(@Valid @RequestBody ShouyeCarousel shouyeCarousel) {
        try {
            boolean success = shouyeCarouselService.save(shouyeCarousel);
            if (success) {
                // 重新查询获取完整的首页轮播图信息（包含自动生成的ID和时间戳）
                ShouyeCarousel createdCarousel = shouyeCarouselService.getById(shouyeCarousel.getCarouselId());
                return Result.success(createdCarousel);
            } else {
                return Result.error("首页轮播图创建失败");
            }
        } catch (Exception e) {
            log.error("创建首页轮播图失败，shouyeCarousel: {}", shouyeCarousel, e);
            return Result.error("创建首页轮播图失败");
        }
    }

    /**
     * 更新首页轮播图
     *
     * @param id             首页轮播图ID
     * @param shouyeCarousel 首页轮播图信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新首页轮播图", notes = "根据ID更新首页轮播图信息")
    public Result<String> update(
            @ApiParam(value = "首页轮播图ID", required = true) @PathVariable Integer id,
            @Valid @RequestBody ShouyeCarousel shouyeCarousel) {
        try {
            shouyeCarousel.setCarouselId(id);
            boolean success = shouyeCarouselService.updateById(shouyeCarousel);
            if (success) {
                return Result.success("首页轮播图更新成功");
            } else {
                return Result.error("首页轮播图更新失败");
            }
        } catch (Exception e) {
            log.error("更新首页轮播图失败，id: {}, shouyeCarousel: {}", id, shouyeCarousel, e);
            return Result.error("更新首页轮播图失败");
        }
    }

    /**
     * 删除首页轮播图
     *
     * @param id 首页轮播图ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除首页轮播图", notes = "根据ID删除首页轮播图")
    public Result<String> delete(
            @ApiParam(value = "首页轮播图ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = shouyeCarouselService.removeById(id);
            if (success) {
                return Result.success("首页轮播图删除成功");
            } else {
                return Result.error("首页轮播图删除失败");
            }
        } catch (Exception e) {
            log.error("删除首页轮播图失败，id: {}", id, e);
            return Result.error("删除首页轮播图失败");
        }
    }
}
