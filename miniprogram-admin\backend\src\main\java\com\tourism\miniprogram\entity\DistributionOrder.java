package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分销订单实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("distribution_orders")
@ApiModel(value = "DistributionOrder对象", description = "分销订单信息")
public class DistributionOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分销订单ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分销员ID")
    @TableField("distributor_id")
    @NotNull(message = "分销员ID不能为空")
    private Integer distributorId;

    @ApiModelProperty(value = "关联订单ID")
    @TableField("order_id")
    @NotNull(message = "订单ID不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "佣金比例")
    @TableField("commission_rate")
    @NotNull(message = "佣金比例不能为空")
    @DecimalMin(value = "0.00", message = "佣金比例不能小于0")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "佣金金额")
    @TableField("commission_amount")
    @NotNull(message = "佣金金额不能为空")
    @DecimalMin(value = "0.00", message = "佣金金额不能小于0")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "订单金额")
    @TableField("order_amount")
    @NotNull(message = "订单金额不能为空")
    @DecimalMin(value = "0.00", message = "订单金额不能小于0")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "结算状态：pending-待结算，settled-已结算，canceled-已取消")
    @TableField("status")
    @NotBlank(message = "结算状态不能为空")
    private String status;

    @ApiModelProperty(value = "结算时间")
    @TableField("settled_at")
    private LocalDateTime settledAt;

    @ApiModelProperty(value = "基础佣金比例")
    @TableField("base_rate")
    @NotNull(message = "基础佣金比例不能为空")
    @DecimalMin(value = "0.00", message = "基础佣金比例不能小于0")
    private BigDecimal baseRate;

    @ApiModelProperty(value = "实际应用比例")
    @TableField("applied_rate")
    @NotNull(message = "实际应用比例不能为空")
    @DecimalMin(value = "0.00", message = "实际应用比例不能小于0")
    private BigDecimal appliedRate;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
