package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.DistributionProductCommissionRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分销产品佣金比例Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface DistributionProductCommissionRateMapper extends BaseMapper<DistributionProductCommissionRate> {

    /**
     * 根据分销员ID获取佣金比例列表
     *
     * @param distributorId 分销员ID
     * @return 佣金比例列表
     */
    @Select("SELECT * FROM distribution_product_commission_rates WHERE distributor_id = #{distributorId} ORDER BY created_at DESC")
    List<DistributionProductCommissionRate> selectByDistributorId(Integer distributorId);

    /**
     * 根据分销员ID和产品ID获取佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 佣金比例记录
     */
    @Select("SELECT * FROM distribution_product_commission_rates WHERE distributor_id = #{distributorId} AND product_id = #{productId}")
    DistributionProductCommissionRate selectByDistributorIdAndProductId(@Param("distributorId") Integer distributorId,
                                                                       @Param("productId") Integer productId);

    /**
     * 根据分销员ID和组合包ID获取佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 佣金比例记录
     */
    @Select("SELECT * FROM distribution_product_commission_rates WHERE distributor_id = #{distributorId} AND bundle_id = #{bundleId}")
    DistributionProductCommissionRate selectByDistributorIdAndBundleId(@Param("distributorId") Integer distributorId,
                                                                      @Param("bundleId") Integer bundleId);

    /**
     * 根据产品ID获取所有分销员的佣金比例
     *
     * @param productId 产品ID
     * @return 佣金比例列表
     */
    @Select("SELECT * FROM distribution_product_commission_rates WHERE product_id = #{productId} ORDER BY created_at DESC")
    List<DistributionProductCommissionRate> selectByProductId(Integer productId);

    /**
     * 根据组合包ID获取所有分销员的佣金比例
     *
     * @param bundleId 组合包ID
     * @return 佣金比例列表
     */
    @Select("SELECT * FROM distribution_product_commission_rates WHERE bundle_id = #{bundleId} ORDER BY created_at DESC")
    List<DistributionProductCommissionRate> selectByBundleId(Integer bundleId);

    /**
     * 检查分销员是否有产品特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM distribution_product_commission_rates WHERE distributor_id = #{distributorId} AND product_id = #{productId}")
    Boolean existsByDistributorIdAndProductId(@Param("distributorId") Integer distributorId,
                                            @Param("productId") Integer productId);

    /**
     * 检查分销员是否有组合包特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM distribution_product_commission_rates WHERE distributor_id = #{distributorId} AND bundle_id = #{bundleId}")
    Boolean existsByDistributorIdAndBundleId(@Param("distributorId") Integer distributorId,
                                           @Param("bundleId") Integer bundleId);
}
