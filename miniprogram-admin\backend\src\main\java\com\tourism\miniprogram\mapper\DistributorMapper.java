package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Distributor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销员Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface DistributorMapper extends BaseMapper<Distributor> {

    /**
     * 根据用户ID获取分销员信息
     *
     * @param userId 用户ID
     * @return 分销员信息
     */
    @Select("SELECT * FROM distributors WHERE user_id = #{userId}")
    Distributor selectByUserId(Integer userId);

    /**
     * 根据分销员编号获取分销员信息
     *
     * @param distributorCode 分销员编号
     * @return 分销员信息
     */
    @Select("SELECT * FROM distributors WHERE distributor_code = #{distributorCode}")
    Distributor selectByDistributorCode(String distributorCode);

    /**
     * 根据状态获取分销员列表
     *
     * @param status 状态
     * @return 分销员列表
     */
    @Select("SELECT * FROM distributors WHERE status = #{status} ORDER BY created_at DESC")
    List<Distributor> selectByStatus(String status);

    /**
     * 更新分销员佣金信息
     *
     * @param distributorId 分销员ID
     * @param totalCommission 累积佣金总额
     * @param availableCommission 可提现金额
     * @param unsettledCommission 未结算金额
     * @return 更新的记录数
     */
    @Update("UPDATE distributors SET total_commission = #{totalCommission}, " +
            "available_commission = #{availableCommission}, " +
            "unsettled_commission = #{unsettledCommission}, " +
            "updated_at = NOW() " +
            "WHERE id = #{distributorId}")
    int updateCommissionInfo(@Param("distributorId") Integer distributorId,
                           @Param("totalCommission") BigDecimal totalCommission,
                           @Param("availableCommission") BigDecimal availableCommission,
                           @Param("unsettledCommission") BigDecimal unsettledCommission);

    /**
     * 更新分销员今日激活人数
     *
     * @param distributorId 分销员ID
     * @param todayActivated 今日激活人数
     * @return 更新的记录数
     */
    @Update("UPDATE distributors SET today_activated = #{todayActivated}, updated_at = NOW() " +
            "WHERE id = #{distributorId}")
    int updateTodayActivated(@Param("distributorId") Integer distributorId,
                           @Param("todayActivated") Integer todayActivated);

    /**
     * 重置所有分销员今日激活人数（定时任务使用）
     *
     * @return 更新的记录数
     */
    @Update("UPDATE distributors SET today_activated = 0, updated_at = NOW()")
    int resetTodayActivated();

    /**
     * 获取活跃分销员统计信息
     *
     * @return 活跃分销员数量
     */
    @Select("SELECT COUNT(*) FROM distributors WHERE status = 'active'")
    Integer countActiveDistributors();

    /**
     * 获取分销员总佣金统计
     *
     * @return 总佣金金额
     */
    @Select("SELECT COALESCE(SUM(total_commission), 0) FROM distributors")
    BigDecimal getTotalCommissionSum();
}
