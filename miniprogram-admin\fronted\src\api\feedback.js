import request from '@/utils/request'

export function getFeedbackList(params) {
  return request({
    url: '/feedback/page',
    method: 'get',
    params
  })
}

export function fetchFeedback(id) {
  return request({
    url: `/feedback/${id}`,
    method: 'get'
  })
}

export function createFeedback(data) {
  return request({
    url: '/feedback',
    method: 'post',
    data
  })
}

export function updateFeedback(id, data) {
  return request({
    url: `/feedback/${id}`,
    method: 'put',
    data
  })
}

export function deleteFeedback(id) {
  return request({
    url: `/feedback/${id}`,
    method: 'delete'
  })
}

export function replyFeedback(id, data) {
  return request({
    url: `/feedback/${id}/reply`,
    method: 'post',
    data
  })
}
