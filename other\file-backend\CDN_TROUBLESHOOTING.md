# CDN访问问题排查指南

## 🚨 **问题现象**
配置了CDN域名 `https://www.gobsweb.com`，但生成的URL无法访问，加载不出内容。

## 🔍 **排查步骤**

### 1. **验证CDN域名解析**
```bash
# 检查DNS解析
nslookup www.gobsweb.com

# 测试HTTP连接
curl -I https://www.gobsweb.com

# 检查是否返回腾讯云CDN的响应头
curl -v https://www.gobsweb.com 2>&1 | grep -i "server\|x-"
```

### 2. **检查腾讯云CDN控制台配置**

#### A. **回源配置**
- 登录腾讯云CDN控制台
- 找到域名 `www.gobsweb.com`
- 检查回源配置：
  ```
  源站类型: 对象存储（COS）
  源站地址: travel-1315014578.cos.ap-guangzhou.myqcloud.com
  回源协议: HTTPS（推荐）
  ```

#### B. **域名状态**
- 确认域名状态为"已启动"
- 检查CNAME是否正确配置

### 3. **验证COS存储桶权限**

#### A. **存储桶权限设置**
1. 登录腾讯云COS控制台
2. 选择存储桶 `travel-1315014578`
3. 权限管理 → 存储桶访问权限
4. 设置为：**公有读私有写** 或 **公有读写**

#### B. **CORS配置**
在COS控制台添加CORS规则：
```json
[
  {
    "allowedOrigins": ["*"],
    "allowedMethods": ["GET", "POST", "PUT", "DELETE", "HEAD"],
    "allowedHeaders": ["*"],
    "exposeHeaders": ["ETag", "x-cos-request-id"],
    "maxAgeSeconds": 3600
  }
]
```

### 4. **测试文件访问**

#### A. **直接测试COS访问**
```bash
# 测试COS源站是否可访问
curl -I https://travel-1315014578.cos.ap-guangzhou.myqcloud.com/

# 如果有具体文件，测试文件访问
curl -I https://travel-1315014578.cos.ap-guangzhou.myqcloud.com/tourism/files/test.jpg
```

#### B. **测试CDN访问**
```bash
# 测试CDN域名
curl -I https://www.gobsweb.com/

# 测试具体文件（如果存在）
curl -I https://www.gobsweb.com/tourism/files/test.jpg
```

### 5. **上传测试文件验证**

#### A. **上传测试文件**
```bash
curl -X POST http://localhost:8080/api/test/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test-image.jpg'
```

#### B. **检查返回的URL**
```json
{
  "previewUrl": "https://www.gobsweb.com/tourism/files/image/20231201_123456_abc123.jpg",
  "downloadUrl": "https://travel-1315014578.cos.ap-guangzhou.myqcloud.com/...",
  "cdnUrl": "https://www.gobsweb.com/tourism/files/image/20231201_123456_abc123.jpg"
}
```

#### C. **分别测试URL**
1. 先测试 `downloadUrl`（COS直链）是否可访问
2. 再测试 `previewUrl`（CDN链接）是否可访问

## 🛠️ **常见问题及解决方案**

### 问题1: CDN域名无法解析
**解决方案:**
1. 检查域名DNS设置，确保CNAME指向腾讯云CDN
2. 等待DNS传播（可能需要几分钟到几小时）

### 问题2: CDN返回404
**解决方案:**
1. 检查CDN回源配置是否正确
2. 确认COS存储桶权限为公有读
3. 检查文件路径是否正确

### 问题3: CDN返回403
**解决方案:**
1. 检查COS存储桶权限设置
2. 确认CDN防盗链配置
3. 检查访问策略设置

### 问题4: 缓存问题
**解决方案:**
1. 在CDN控制台刷新缓存
2. 添加版本参数强制刷新：`?v=timestamp`
3. 等待缓存过期时间

## 📋 **检查清单**

- [ ] CDN域名DNS解析正确
- [ ] CDN回源配置指向正确的COS存储桶
- [ ] COS存储桶权限设置为公有读
- [ ] CDN域名状态为已启动
- [ ] CORS配置正确
- [ ] 文件路径格式正确
- [ ] 缓存已刷新

## 🔧 **调试命令**

```bash
# 1. 检查CDN配置
curl http://localhost:8080/api/test/cdn-test

# 2. 上传测试文件
curl -X POST http://localhost:8080/api/test/upload \
  -F 'file=@test.jpg'

# 3. 测试返回的URL
curl -I [返回的previewUrl]

# 4. 对比COS直链
curl -I [返回的downloadUrl]
```

## 📞 **需要提供的信息**

如果问题仍然存在，请提供：
1. CDN域名配置截图
2. COS存储桶权限截图
3. 上传文件后返回的完整响应
4. 访问URL时的错误信息
5. 浏览器开发者工具中的网络请求详情
