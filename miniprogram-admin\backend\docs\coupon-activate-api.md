# 卡券激活接口文档

## 接口概述

本文档描述了数字卡券管理系统中的卡券激活接口，该接口允许直接通过卡券ID激活指定的卡券。

## 接口详情

### 激活卡券

**接口路径：** `POST /api/coupons/{couponId}/activate`

**请求方式：** POST

**接口描述：** 根据卡券ID激活指定的卡券

**说明：** 由于应用程序配置了全局上下文路径 `/api`，实际的Controller映射为 `/coupons`，最终完整路径为 `/api/coupons/{couponId}/activate`

#### 请求参数

| 参数名 | 参数类型 | 是否必填 | 参数位置 | 参数描述 |
|--------|----------|----------|----------|----------|
| couponId | Integer | 是 | Path | 卡券ID |

#### 请求示例

```http
POST /api/coupons/123/activate
Content-Type: application/json
```

#### 响应参数

| 参数名 | 参数类型 | 参数描述 |
|--------|----------|----------|
| code | Integer | 响应状态码 |
| message | String | 响应消息 |
| data | Coupon | 激活后的卡券信息 |
| timestamp | Long | 响应时间戳 |

#### 响应示例

**成功响应 (200):**
```json
{
  "code": 200,
  "message": "卡券激活成功",
  "data": {
    "id": 123,
    "code": "TKT123456789",
    "productId": 1,
    "bundleId": null,
    "orderId": 456,
    "userId": 789,
    "status": "active",
    "activationCodeId": null,
    "validFrom": "2025-06-15T14:30:00",
    "validTo": "2025-06-16T02:30:00",
    "usedAt": "2025-06-15T14:30:00",
    "createdAt": "2025-06-15T10:00:00",
    "updatedAt": "2025-06-15T14:30:00"
  },
  "timestamp": 1718456400000
}
```

**错误响应 (400 - 卡券状态不允许激活):**
```json
{
  "code": 400,
  "message": "卡券状态不允许激活，当前状态：active",
  "data": null,
  "timestamp": 1718456400000
}
```

**错误响应 (404 - 卡券不存在):**
```json
{
  "code": 404,
  "message": "卡券不存在",
  "data": null,
  "timestamp": 1718456400000
}
```

**错误响应 (500 - 服务器内部错误):**
```json
{
  "code": 500,
  "message": "系统异常，请稍后重试",
  "data": null,
  "timestamp": 1718456400000
}
```

## 业务逻辑

### 激活流程

1. **参数验证**：检查卡券ID是否为空
2. **卡券存在性检查**：根据ID查询卡券是否存在
3. **状态验证**：检查卡券当前状态是否为 `unactivated`
4. **执行激活**：更新卡券状态和相关字段
5. **返回结果**：返回激活后的完整卡券信息

### 字段更新规则

激活时会更新以下字段：

- `status`: 设置为 `'active'`
- `used_at`: 设置为当前时间戳
- `valid_to`: 设置为当前时间加12小时
- `updated_at`: 自动更新为当前时间

### 状态转换

```
unactivated → active (允许)
active → active (不允许，返回400错误)
used → active (不允许，返回400错误)
expired → active (不允许，返回400错误)
```

## 错误码说明

| 错误码 | 错误描述 | 解决方案 |
|--------|----------|----------|
| 400 | 卡券ID不能为空 | 检查请求路径中的couponId参数 |
| 400 | 卡券状态不允许激活 | 确认卡券当前状态为unactivated |
| 404 | 卡券不存在 | 检查卡券ID是否正确 |
| 500 | 卡券激活失败 | 检查数据库连接和数据完整性 |
| 500 | 系统异常 | 联系系统管理员 |

## 技术实现

### 事务管理

该接口使用 `@Transactional` 注解确保数据一致性，如果激活过程中发生异常，所有数据库操作将回滚。

### 日志记录

接口包含详细的日志记录：
- 激活开始和结束日志
- 错误情况的详细日志
- 业务逻辑关键步骤的日志

### 安全考虑

- 参数验证防止空值攻击
- 状态检查防止重复激活
- 异常处理防止信息泄露

## 使用示例

### cURL 示例

```bash
# 激活ID为123的卡券
curl -X POST "http://localhost:8080/api/coupons/123/activate" \
     -H "Content-Type: application/json"
```

### JavaScript 示例

```javascript
// 使用 fetch API
async function activateCoupon(couponId) {
  try {
    const response = await fetch(`/api/coupons/${couponId}/activate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('卡券激活成功:', result.data);
      return result.data;
    } else {
      console.error('卡券激活失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('激活请求失败:', error);
    throw error;
  }
}

// 使用示例
activateCoupon(123)
  .then(coupon => {
    console.log('激活的卡券:', coupon);
  })
  .catch(error => {
    console.error('激活失败:', error.message);
  });
```

## 注意事项

1. **幂等性**：该接口不是幂等的，重复调用会返回400错误
2. **时效性**：激活后的卡券有效期为12小时
3. **状态管理**：只有状态为 `unactivated` 的卡券才能被激活
4. **数据一致性**：使用事务确保数据的一致性
5. **错误处理**：客户端应根据不同的错误码进行相应的处理

## 相关接口

- `GET /api/coupons/{id}` - 获取卡券详情
- `GET /api/coupons/unactivated` - 查询未激活卡券
- `GET /api/coupons/has-unactivated` - 检查是否有未激活卡券

## 配置说明

应用程序在 `application.yml` 中配置了全局上下文路径：

```yaml
server:
  servlet:
    context-path: /api
```

因此所有Controller的 `@RequestMapping` 路径都会自动添加 `/api` 前缀。
