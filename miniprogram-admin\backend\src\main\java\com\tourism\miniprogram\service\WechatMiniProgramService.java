package com.tourism.miniprogram.service;

/**
 * 微信小程序服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface WechatMiniProgramService {

    /**
     * 获取微信小程序访问令牌
     *
     * @return access_token
     */
    String getAccessToken();

    /**
     * 生成小程序码
     *
     * @param scene 场景值，最大32个可见字符
     * @param page 页面路径，不能带参数
     * @param width 二维码的宽度，单位px，最小280px，最大1280px
     * @return 小程序码图片的字节数组
     */
    byte[] generateMiniProgramCode(String scene, String page, Integer width);

    /**
     * 上传小程序码到云存储并返回URL
     *
     * @param imageBytes 图片字节数组
     * @param fileName 文件名
     * @return 图片URL
     */
    String uploadMiniProgramCodeToCloud(byte[] imageBytes, String fileName);
}
