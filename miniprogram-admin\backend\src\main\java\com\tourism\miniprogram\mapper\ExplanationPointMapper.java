package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ExplanationPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲解点Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface ExplanationPointMapper extends BaseMapper<ExplanationPoint> {

    /**
     * 根据区域ID获取讲解点列表
     *
     * @param areaId 区域ID
     * @return 讲解点列表
     */
    List<ExplanationPoint> selectPointsByAreaId(@Param("areaId") Integer areaId);
}
