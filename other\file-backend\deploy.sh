#!/bin/bash

# 旅游讲解小程序文件管理API系统部署脚本
# Author: Tourism Team
# Version: 1.0.0

echo "==================================="
echo "旅游讲解小程序文件管理API系统部署脚本"
echo "==================================="

# 设置变量
APP_NAME="tourism-file-management-api"
JAR_NAME="file-management-api-1.0.0.jar"
PID_FILE="$APP_NAME.pid"
LOG_FILE="logs/$APP_NAME.log"

# 创建日志目录
mkdir -p logs

# 检查Java环境
check_java() {
    if [ -z "$JAVA_HOME" ]; then
        echo "错误: JAVA_HOME 环境变量未设置"
        exit 1
    fi
    
    if [ ! -x "$JAVA_HOME/bin/java" ]; then
        echo "错误: Java 可执行文件不存在: $JAVA_HOME/bin/java"
        exit 1
    fi
    
    JAVA_VERSION=$($JAVA_HOME/bin/java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo "Java版本: $JAVA_VERSION"
}

# 停止应用
stop_app() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "正在停止应用 (PID: $PID)..."
            kill $PID
            
            # 等待进程结束
            for i in {1..30}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    echo "应用已停止"
                    rm -f $PID_FILE
                    return 0
                fi
                sleep 1
            done
            
            # 强制杀死进程
            echo "强制停止应用..."
            kill -9 $PID
            rm -f $PID_FILE
        else
            echo "应用进程不存在，删除PID文件"
            rm -f $PID_FILE
        fi
    else
        echo "应用未运行"
    fi
}

# 启动应用
start_app() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "应用已在运行 (PID: $PID)"
            return 0
        fi
    fi
    
    echo "正在启动应用..."
    
    # 检查JAR文件是否存在
    if [ ! -f "target/$JAR_NAME" ]; then
        echo "错误: JAR文件不存在，请先执行 mvn clean package"
        exit 1
    fi
    
    # 启动应用
    nohup $JAVA_HOME/bin/java \
        -Xms512m \
        -Xmx1024m \
        -Dspring.profiles.active=prod \
        -Dfile.encoding=UTF-8 \
        -Duser.timezone=GMT+08 \
        -jar target/$JAR_NAME \
        > $LOG_FILE 2>&1 &
    
    echo $! > $PID_FILE
    
    # 等待应用启动
    echo "等待应用启动..."
    for i in {1..60}; do
        if curl -s http://localhost:8080/api/health/check > /dev/null 2>&1; then
            echo "应用启动成功！"
            echo "API文档地址: http://localhost:8080/api/doc.html"
            echo "健康检查地址: http://localhost:8080/api/health/check"
            return 0
        fi
        sleep 2
    done
    
    echo "应用启动超时，请检查日志: $LOG_FILE"
    exit 1
}

# 重启应用
restart_app() {
    echo "正在重启应用..."
    stop_app
    sleep 2
    start_app
}

# 查看应用状态
status_app() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "应用正在运行 (PID: $PID)"
            
            # 检查健康状态
            if curl -s http://localhost:8080/api/health/check > /dev/null 2>&1; then
                echo "应用健康状态: 正常"
            else
                echo "应用健康状态: 异常"
            fi
        else
            echo "应用未运行（PID文件存在但进程不存在）"
            rm -f $PID_FILE
        fi
    else
        echo "应用未运行"
    fi
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f $LOG_FILE
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 编译打包
build_app() {
    echo "正在编译打包..."
    mvn clean package -DskipTests
    
    if [ $? -eq 0 ]; then
        echo "编译打包成功"
    else
        echo "编译打包失败"
        exit 1
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            check_java
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            check_java
            restart_app
            ;;
        status)
            status_app
            ;;
        logs)
            show_logs
            ;;
        build)
            build_app
            ;;
        deploy)
            check_java
            build_app
            restart_app
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs|build|deploy}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动应用"
            echo "  stop    - 停止应用"
            echo "  restart - 重启应用"
            echo "  status  - 查看应用状态"
            echo "  logs    - 查看应用日志"
            echo "  build   - 编译打包"
            echo "  deploy  - 编译打包并重启应用"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
