package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.DistributionWithdrawalRecord;
import com.tourism.miniprogram.service.DistributionWithdrawalRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销提现记录控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/distribution/withdrawal")
@Api(tags = "分销提现记录管理")
public class DistributionWithdrawalRecordController {

    @Autowired
    private DistributionWithdrawalRecordService withdrawalRecordService;

    /**
     * 获取提现记录列表（分页查询）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param distributorId 分销员ID
     * @param status 提现状态
     * @return 提现记录列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取提现记录列表", notes = "分页查询提现记录，支持按分销员、状态筛选")
    public Result<IPage<DistributionWithdrawalRecord>> getWithdrawalRecordList(
            @ApiParam(value = "当前页码", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "分销员ID") @RequestParam(required = false) Integer distributorId,
            @ApiParam(value = "提现状态") @RequestParam(required = false) String status) {
        try {
            Page<DistributionWithdrawalRecord> page = new Page<>(current, size);
            QueryWrapper<DistributionWithdrawalRecord> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (distributorId != null) {
                queryWrapper.eq("distributor_id", distributorId);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }

            // 按申请时间倒序排列
            queryWrapper.orderByDesc("applied_at");

            IPage<DistributionWithdrawalRecord> recordPage = withdrawalRecordService.page(page, queryWrapper);
            return Result.success(recordPage);
        } catch (Exception e) {
            log.error("获取提现记录列表失败", e);
            return Result.error("获取提现记录列表失败");
        }
    }

    /**
     * 获取提现记录详情
     *
     * @param recordId 提现记录ID
     * @return 提现记录信息
     */
    @GetMapping("/{recordId}")
    @ApiOperation(value = "获取提现记录详情", notes = "根据记录ID获取提现记录详细信息")
    public Result<DistributionWithdrawalRecord> getWithdrawalRecordInfo(
            @ApiParam(value = "提现记录ID", required = true) @PathVariable Integer recordId) {
        try {
            DistributionWithdrawalRecord record = withdrawalRecordService.getById(recordId);
            if (record == null) {
                return Result.error(404, "提现记录不存在");
            }
            return Result.success(record);
        } catch (Exception e) {
            log.error("获取提现记录详情失败，recordId: {}", recordId, e);
            return Result.error("获取提现记录详情失败");
        }
    }

    /**
     * 根据分销员ID获取提现记录
     *
     * @param distributorId 分销员ID
     * @return 提现记录列表
     */
    @GetMapping("/distributor/{distributorId}")
    @ApiOperation(value = "获取分销员提现记录", notes = "获取指定分销员的所有提现记录")
    public Result<List<DistributionWithdrawalRecord>> getWithdrawalRecordsByDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            List<DistributionWithdrawalRecord> records = withdrawalRecordService.getByDistributorId(distributorId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取分销员提现记录失败，distributorId: {}", distributorId, e);
            return Result.error("获取提现记录失败");
        }
    }

    /**
     * 根据状态获取提现记录
     *
     * @param status 提现状态
     * @return 提现记录列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态获取提现记录", notes = "获取指定状态的提现记录列表")
    public Result<List<DistributionWithdrawalRecord>> getWithdrawalRecordsByStatus(
            @ApiParam(value = "提现状态", required = true) @PathVariable String status) {
        try {
            List<DistributionWithdrawalRecord> records = withdrawalRecordService.getByStatus(status);
            return Result.success(records);
        } catch (Exception e) {
            log.error("根据状态获取提现记录失败，status: {}", status, e);
            return Result.error("获取提现记录失败");
        }
    }

    /**
     * 创建提现申请
     *
     * @param distributorId 分销员ID
     * @param amount 提现金额
     * @return 创建结果
     */
    @PostMapping("/apply")
    @ApiOperation(value = "创建提现申请", notes = "分销员申请提现")
    public Result<DistributionWithdrawalRecord> createWithdrawalRequest(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "提现金额", required = true) @RequestParam BigDecimal amount) {
        try {
            // 检查提现条件
            if (!withdrawalRecordService.canWithdraw(distributorId, amount)) {
                return Result.error(400, "提现条件不满足，请检查可提现金额或分销员状态");
            }

            DistributionWithdrawalRecord record = withdrawalRecordService.createWithdrawalRequest(distributorId, amount);
            return Result.success(record);
        } catch (Exception e) {
            log.error("创建提现申请失败，distributorId: {}, amount: {}", distributorId, amount, e);
            return Result.error("创建提现申请失败: " + e.getMessage());
        }
    }

    /**
     * 审核提现申请
     *
     * @param recordId 提现记录ID
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（如果不通过）
     * @return 审核结果
     */
    @PutMapping("/{recordId}/review")
    @ApiOperation(value = "审核提现申请", notes = "管理员审核提现申请")
    public Result<String> reviewWithdrawalRequest(
            @ApiParam(value = "提现记录ID", required = true) @PathVariable Integer recordId,
            @ApiParam(value = "是否通过", required = true) @RequestParam Boolean approved,
            @ApiParam(value = "拒绝原因") @RequestParam(required = false) String rejectReason) {
        try {
            boolean success = withdrawalRecordService.reviewWithdrawalRequest(recordId, approved, rejectReason);
            if (success) {
                return Result.success(approved ? "提现申请审核通过" : "提现申请已拒绝");
            } else {
                return Result.error("审核失败");
            }
        } catch (Exception e) {
            log.error("审核提现申请失败，recordId: {}, approved: {}", recordId, approved, e);
            return Result.error("审核提现申请失败");
        }
    }

    /**
     * 完成提现处理
     *
     * @param recordId 提现记录ID
     * @param transactionNo 交易流水号
     * @return 处理结果
     */
    @PutMapping("/{recordId}/complete")
    @ApiOperation(value = "完成提现处理", notes = "标记提现为已完成状态")
    public Result<String> completeWithdrawal(
            @ApiParam(value = "提现记录ID", required = true) @PathVariable Integer recordId,
            @ApiParam(value = "交易流水号") @RequestParam(required = false) String transactionNo) {
        try {
            boolean success = withdrawalRecordService.completeWithdrawal(recordId, transactionNo);
            if (success) {
                return Result.success("提现处理完成");
            } else {
                return Result.error("提现处理失败");
            }
        } catch (Exception e) {
            log.error("完成提现处理失败，recordId: {}", recordId, e);
            return Result.error("完成提现处理失败");
        }
    }

    /**
     * 获取分销员提现统计信息
     *
     * @param distributorId 分销员ID
     * @return 统计信息
     */
    @GetMapping("/statistics/{distributorId}")
    @ApiOperation(value = "获取分销员提现统计", notes = "获取分销员的提现相关统计数据")
    public Result<Object> getWithdrawalStatistics(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            BigDecimal pendingAmount = withdrawalRecordService.getPendingWithdrawalAmount(distributorId);
            BigDecimal completedAmount = withdrawalRecordService.getCompletedWithdrawalAmount(distributorId);
            
            return Result.success(new Object() {
                public final BigDecimal pendingWithdrawalAmount = pendingAmount;
                public final BigDecimal completedWithdrawalAmount = completedAmount;
            });
        } catch (Exception e) {
            log.error("获取分销员提现统计失败，distributorId: {}", distributorId, e);
            return Result.error("获取提现统计失败");
        }
    }

    /**
     * 获取系统提现统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取系统提现统计", notes = "获取系统整体提现统计数据")
    public Result<Object> getSystemWithdrawalStatistics() {
        try {
            BigDecimal totalAmount = withdrawalRecordService.getTotalWithdrawalAmount();
            Integer pendingCount = withdrawalRecordService.getPendingWithdrawalCount();
            
            return Result.success(new Object() {
                public final BigDecimal totalWithdrawalAmount = totalAmount;
                public final Integer pendingWithdrawalCount = pendingCount;
            });
        } catch (Exception e) {
            log.error("获取系统提现统计失败", e);
            return Result.error("获取提现统计失败");
        }
    }

    /**
     * 根据交易流水号获取提现记录
     *
     * @param transactionNo 交易流水号
     * @return 提现记录
     */
    @GetMapping("/transaction/{transactionNo}")
    @ApiOperation(value = "根据交易流水号获取提现记录", notes = "通过交易流水号查询提现记录")
    public Result<DistributionWithdrawalRecord> getWithdrawalRecordByTransactionNo(
            @ApiParam(value = "交易流水号", required = true) @PathVariable String transactionNo) {
        try {
            DistributionWithdrawalRecord record = withdrawalRecordService.getByTransactionNo(transactionNo);
            if (record == null) {
                return Result.error(404, "提现记录不存在");
            }
            return Result.success(record);
        } catch (Exception e) {
            log.error("根据交易流水号获取提现记录失败，transactionNo: {}", transactionNo, e);
            return Result.error("获取提现记录失败");
        }
    }

    /**
     * 检查分销员是否可以提现
     *
     * @param distributorId 分销员ID
     * @param amount 提现金额
     * @return 检查结果
     */
    @GetMapping("/check")
    @ApiOperation(value = "检查提现条件", notes = "检查分销员是否满足提现条件")
    public Result<Boolean> checkWithdrawalCondition(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "提现金额", required = true) @RequestParam BigDecimal amount) {
        try {
            boolean canWithdraw = withdrawalRecordService.canWithdraw(distributorId, amount);
            return Result.success(canWithdraw);
        } catch (Exception e) {
            log.error("检查提现条件失败，distributorId: {}, amount: {}", distributorId, amount, e);
            return Result.error("检查提现条件失败");
        }
    }
}
