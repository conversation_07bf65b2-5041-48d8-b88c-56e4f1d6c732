<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ProvinceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.Province">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, sort, status, created_at, updated_at
    </sql>

    <!-- 获取启用的省份列表 -->
    <select id="selectEnabledProvinces" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM provinces
        WHERE status = 1
        ORDER BY sort ASC, id ASC
    </select>

</mapper>
