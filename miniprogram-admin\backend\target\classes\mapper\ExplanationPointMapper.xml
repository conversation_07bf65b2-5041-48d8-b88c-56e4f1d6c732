<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationPointMapper">

    <!-- 根据区域ID获取讲解点列表 -->
    <select id="selectPointsByAreaId" resultType="com.tourism.miniprogram.entity.ExplanationPoint">
        SELECT ep.point_id, ep.point_name, ep.point_image, ep.sort_order, ep.status, ep.created_at, ep.updated_at
        FROM explanation_point ep
        INNER JOIN area_point_relation apr ON ep.point_id = apr.point_id
        WHERE apr.area_id = #{areaId}
        ORDER BY apr.sort_order ASC, ep.point_id DESC
    </select>

</mapper>
