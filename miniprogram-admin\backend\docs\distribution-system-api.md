# 分销员系统API接口文档

## 概述

分销员系统提供完整的分销管理功能，包括分销员管理、提现管理、关系管理、佣金管理和订单分销等功能。

## 基础信息

- **基础URL**: `/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 1. 分销员管理 (`/distributor`)

#### 1.1 获取分销员列表
- **接口**: `GET /distributor/list`
- **描述**: 分页查询分销员列表
- **参数**:
  - `current`: 当前页码 (默认: 1)
  - `size`: 每页大小 (默认: 10)
  - `status`: 分销员状态 (可选)
  - `distributorCode`: 分销员编号 (可选)

#### 1.2 获取分销员详情
- **接口**: `GET /distributor/{distributorId}`
- **描述**: 根据分销员ID获取详细信息

#### 1.3 根据用户ID获取分销员信息
- **接口**: `GET /distributor/user/{userId}`
- **描述**: 检查用户是否为分销员

#### 1.4 创建分销员
- **接口**: `POST /distributor/create`
- **描述**: 为用户创建分销员身份
- **参数**:
  - `userId`: 用户ID (必填)
  - `commissionRate`: 佣金比例 (可选)

#### 1.5 更新分销员状态
- **接口**: `PUT /distributor/{distributorId}/status`
- **描述**: 更新分销员状态
- **参数**:
  - `status`: 新状态 (active/inactive/frozen)

#### 1.6 冻结/解冻分销员
- **接口**: `PUT /distributor/{distributorId}/freeze`
- **接口**: `PUT /distributor/{distributorId}/unfreeze`

#### 1.7 生成专属二维码
- **接口**: `POST /distributor/{distributorId}/qrcode`

#### 1.8 获取统计信息
- **接口**: `GET /distributor/statistics`

### 2. 分销提现记录管理 (`/distribution/withdrawal`)

#### 2.1 获取提现记录列表
- **接口**: `GET /distribution/withdrawal/list`
- **参数**:
  - `current`: 当前页码
  - `size`: 每页大小
  - `distributorId`: 分销员ID (可选)
  - `status`: 提现状态 (可选)

#### 2.2 创建提现申请
- **接口**: `POST /distribution/withdrawal/apply`
- **参数**:
  - `distributorId`: 分销员ID
  - `amount`: 提现金额

#### 2.3 审核提现申请
- **接口**: `PUT /distribution/withdrawal/{recordId}/review`
- **参数**:
  - `approved`: 是否通过
  - `rejectReason`: 拒绝原因 (可选)

#### 2.4 完成提现处理
- **接口**: `PUT /distribution/withdrawal/{recordId}/complete`
- **参数**:
  - `transactionNo`: 交易流水号 (可选)

#### 2.5 获取提现统计
- **接口**: `GET /distribution/withdrawal/statistics/{distributorId}`
- **接口**: `GET /distribution/withdrawal/statistics`

### 3. 分销产品佣金比例管理 (`/distribution/commission-rate`)

#### 4.1 获取佣金比例列表
- **接口**: `GET /distribution/commission-rate/list`
- **参数**:
  - `current`: 当前页码
  - `size`: 每页大小
  - `distributorId`: 分销员ID (可选)
  - `productId`: 产品ID (可选)
  - `bundleId`: 组合包ID (可选)

#### 4.2 设置产品佣金比例
- **接口**: `POST /distribution/commission-rate/product`
- **参数**:
  - `distributorId`: 分销员ID
  - `productId`: 产品ID
  - `commissionRate`: 佣金比例

#### 4.3 设置组合包佣金比例
- **接口**: `POST /distribution/commission-rate/bundle`
- **参数**:
  - `distributorId`: 分销员ID
  - `bundleId`: 组合包ID
  - `commissionRate`: 佣金比例

#### 4.4 获取有效佣金比例
- **接口**: `GET /distribution/commission-rate/effective/product`
- **接口**: `GET /distribution/commission-rate/effective/bundle`
- **参数**:
  - `distributorId`: 分销员ID
  - `productId`: 产品ID 或 `bundleId`: 组合包ID

#### 4.5 删除佣金比例
- **接口**: `DELETE /distribution/commission-rate/product`
- **接口**: `DELETE /distribution/commission-rate/bundle`

#### 3.6 检查特定佣金比例
- **接口**: `GET /distribution/commission-rate/check/product`
- **接口**: `GET /distribution/commission-rate/check/bundle`

### 4. 分销订单管理 (`/distribution/order`)

#### 4.1 获取分销订单列表
- **接口**: `GET /distribution/order/list`
- **参数**:
  - `current`: 当前页码
  - `size`: 每页大小
  - `distributorId`: 分销员ID (可选)
  - `orderId`: 订单ID (可选)
  - `status`: 结算状态 (可选)

#### 4.2 创建分销订单记录
- **接口**: `POST /distribution/order/create`
- **参数**:
  - `distributorId`: 分销员ID
  - `orderId`: 订单ID
  - `orderAmount`: 订单金额
  - `commissionRate`: 佣金比例
  - `baseRate`: 基础佣金比例 (可选)

#### 4.3 结算分销订单
- **接口**: `PUT /distribution/order/{distributionOrderId}/settle`
- **接口**: `PUT /distribution/order/batch-settle` (批量结算)

#### 4.4 取消分销订单
- **接口**: `PUT /distribution/order/{distributionOrderId}/cancel`

#### 4.5 获取佣金统计
- **接口**: `GET /distribution/order/statistics/{distributorId}`
- **接口**: `GET /distribution/order/statistics`

#### 4.6 处理订单分销逻辑
- **接口**: `POST /distribution/order/process`
- **参数**:
  - `orderId`: 订单ID
  - `userId`: 用户ID
  - `orderAmount`: 订单金额

#### 4.7 计算佣金金额
- **接口**: `GET /distribution/order/calculate-commission`
- **参数**:
  - `orderAmount`: 订单金额
  - `commissionRate`: 佣金比例

#### 4.8 检查分销记录
- **接口**: `GET /distribution/order/check/{orderId}`

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

## 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 业务流程

### 分销员注册流程
1. 用户申请成为分销员
2. 系统创建分销员记录
3. 生成专属分销员编号和二维码
4. 分销员状态设为活跃

### 订单分销流程
1. 用户下单时通过业务逻辑确定分销员关系
2. 如存在关系，创建分销订单记录
3. 计算佣金金额
4. 订单完成后进行佣金结算

### 提现流程
1. 分销员申请提现
2. 系统验证可提现金额
3. 管理员审核提现申请
4. 审核通过后处理提现
5. 更新分销员佣金信息

## 注意事项

1. 所有金额字段使用 `decimal(10,2)` 格式
2. 佣金比例使用百分比形式 (如: 10.00 表示 10%)
3. 时间字段使用 ISO 8601 格式
4. 分页查询默认按创建时间倒序排列
5. 删除操作会级联删除相关数据
6. 重要操作会记录操作日志
