package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.City;
import com.tourism.miniprogram.service.CityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 城市控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/cities")
@Api(tags = "城市管理")
public class CityController {

    @Autowired
    private CityService cityService;

    /**
     * 根据省份获取城市列表
     *
     * @param provinceId 省份ID
     * @return 城市列表
     */
    @GetMapping
    @ApiOperation(value = "获取城市列表", notes = "根据省份ID获取城市列表")
    public Result<List<City>> getCities(
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId) {
        try {
            List<City> cities = cityService.getEnabledCitiesByProvinceId(provinceId);
            return Result.success(cities);
        } catch (IllegalArgumentException e) {
            log.warn("参数错误: {}", e.getMessage());
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("获取城市列表失败，provinceId: {}", provinceId, e);
            return Result.error("获取城市列表失败");
        }
    }

    /**
     * 分页获取城市列表
     *
     * @param current    当前页
     * @param size       每页大小
     * @param name       城市名称（模糊查询）
     * @param provinceId 省份ID
     * @return 城市分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取城市列表", notes = "分页获取城市列表，支持按名称和省份筛选")
    public Result<IPage<City>> getCityPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "城市名称") @RequestParam(required = false) String name,
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId) {
        try {
            Page<City> page = new Page<>(current, size);
            QueryWrapper<City> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (provinceId != null) {
                queryWrapper.eq("province_id", provinceId);
            }
            queryWrapper.orderByAsc("sort").orderByAsc("id");

            IPage<City> cityPage = cityService.page(page, queryWrapper);
            return Result.success(cityPage);
        } catch (Exception e) {
            log.error("分页获取城市列表失败", e);
            return Result.error("获取城市列表失败");
        }
    }

    /**
     * 获取城市详情
     *
     * @param id 城市ID
     * @return 城市详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取城市详情", notes = "根据ID获取城市详细信息")
    public Result<City> getCityById(@ApiParam(value = "城市ID", required = true) @PathVariable Integer id) {
        try {
            City city = cityService.getById(id);
            if (city == null) {
                return Result.error(404, "城市不存在");
            }
            return Result.success(city);
        } catch (Exception e) {
            log.error("获取城市详情失败，id: {}", id, e);
            return Result.error("获取城市详情失败");
        }
    }

    /**
     * 创建城市
     *
     * @param city 城市信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建城市", notes = "创建新的城市")
    public Result<City> createCity(@RequestBody @Valid City city) {
        try {
            // 检查城市代码是否已存在
            QueryWrapper<City> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", city.getCode());
            City existCity = cityService.getOne(queryWrapper);
            if (existCity != null) {
                return Result.error("城市代码已存在");
            }

            boolean success = cityService.save(city);
            if (success) {
                // 重新查询获取完整的城市信息（包含自动生成的ID和时间戳）
                City createdCity = cityService.getById(city.getId());
                return Result.success(createdCity);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建城市失败", e);
            return Result.error("创建城市失败");
        }
    }

    /**
     * 更新城市
     *
     * @param id   城市ID
     * @param city 城市信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新城市", notes = "更新城市信息")
    public Result<String> updateCity(
            @ApiParam(value = "城市ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid City city) {
        try {
            City existCity = cityService.getById(id);
            if (existCity == null) {
                return Result.error(404, "城市不存在");
            }

            // 检查城市代码是否已被其他城市使用
            QueryWrapper<City> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", city.getCode()).ne("id", id);
            City duplicateCity = cityService.getOne(queryWrapper);
            if (duplicateCity != null) {
                return Result.error("城市代码已被其他城市使用");
            }

            city.setId(id);
            boolean success = cityService.updateById(city);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新城市失败，id: {}", id, e);
            return Result.error("更新城市失败");
        }
    }

    /**
     * 删除城市
     *
     * @param id 城市ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除城市", notes = "删除城市")
    public Result<String> deleteCity(@ApiParam(value = "城市ID", required = true) @PathVariable Integer id) {
        try {
            City city = cityService.getById(id);
            if (city == null) {
                return Result.error(404, "城市不存在");
            }

            boolean success = cityService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除城市失败，id: {}", id, e);
            return Result.error("删除城市失败");
        }
    }
}
