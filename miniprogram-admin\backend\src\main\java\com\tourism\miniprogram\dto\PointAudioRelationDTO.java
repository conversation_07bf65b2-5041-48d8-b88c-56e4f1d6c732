package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 讲解点音频关系DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "PointAudioRelationDTO对象", description = "讲解点音频关系详细信息")
public class PointAudioRelationDTO {

    @ApiModelProperty(value = "关系ID")
    private Integer relationId;

    @ApiModelProperty(value = "讲解点ID")
    private Integer pointId;

    @ApiModelProperty(value = "讲解点名称")
    private String pointName;

    @ApiModelProperty(value = "音频ID")
    private Integer audioId;

    @ApiModelProperty(value = "音频名称")
    private String audioName;

    @ApiModelProperty(value = "音频URL")
    private String audioUrl;

    @ApiModelProperty(value = "音频图片")
    private String audioImage;

    @ApiModelProperty(value = "音频时长")
    private Integer duration;

    @ApiModelProperty(value = "音频位置地址")
    private String audioAddress;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
}
