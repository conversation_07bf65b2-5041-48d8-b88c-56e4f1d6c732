package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲解音频实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("explanation_audio")
@ApiModel(value = "ExplanationAudio对象", description = "讲解音频信息")
public class ExplanationAudio implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "音频ID")
    @TableId(value = "audio_id", type = IdType.AUTO)
    private Integer audioId;

    @ApiModelProperty(value = "音频名称")
    @TableField("audio_name")
    @NotBlank(message = "音频名称不能为空")
    private String audioName;

    @ApiModelProperty(value = "音频文件URL")
    @TableField("audio_url")
    @NotBlank(message = "音频文件URL不能为空")
    private String audioUrl;

    @ApiModelProperty(value = "音频关联图片URL")
    @TableField("audio_image")
    private String audioImage;

    @ApiModelProperty(value = "音频时长(秒)")
    @TableField("duration")
    private Integer duration;

    @ApiModelProperty(value = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "音频位置地址")
    @TableField("audio_address")
    private String audioAddress;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
