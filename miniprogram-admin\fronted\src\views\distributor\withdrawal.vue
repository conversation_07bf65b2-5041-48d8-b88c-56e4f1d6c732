<template>
  <div class="withdrawal-records">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回详情
      </el-button>
      <h2>提现记录管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="提现状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待处理" value="pending" />
            <el-option label="已完成" value="completed" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 提现记录表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>提现记录列表</span>
          <div>
            <el-button type="primary" @click="handleCreateWithdrawal">
              <el-icon><Plus /></el-icon>
              创建提现申请
            </el-button>
            <el-button type="info" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="recordList"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="id" label="记录ID" width="100" align="center" />
        
        <el-table-column prop="amount" label="提现金额" min-width="120" align="right">
          <template #default="scope">
            <span class="money-text">¥{{ formatMoney(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="transactionNo" label="交易流水号" min-width="180">
          <template #default="scope">
            <span>{{ scope.row.transactionNo || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="提现状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="appliedAt" label="申请时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.appliedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="completedAt" label="完成时间" min-width="160">
          <template #default="scope">
            {{ scope.row.completedAt ? formatDate(scope.row.completedAt) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="rejectReason" label="拒绝原因" min-width="200">
          <template #default="scope">
            <span>{{ scope.row.rejectReason || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-dropdown 
              v-if="scope.row.status === 'pending'"
              @command="(command) => handleAction(command, scope.row)"
            >
              <el-button type="warning" size="small">
                审核<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="approve">通过</el-dropdown-item>
                  <el-dropdown-item command="reject">拒绝</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="success"
              size="small"
              @click="handleComplete(scope.row)"
            >
              <el-icon><Check /></el-icon>
              完成
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建提现申请对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建提现申请"
      width="500px"
      :before-close="handleCreateDialogClose"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="提现金额" prop="amount">
          <el-input
            v-model.number="createForm.amount"
            placeholder="请输入提现金额"
            type="number"
            :step="0.01"
          >
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">可提现金额：¥{{ formatMoney(availableAmount) }}</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateSubmit" :loading="createLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      :title="reviewType === 'approve' ? '通过提现申请' : '拒绝提现申请'"
      width="500px"
    >
      <el-form
        ref="reviewFormRef"
        :model="reviewForm"
        :rules="reviewRules"
        label-width="120px"
      >
        <el-form-item label="提现金额">
          <span class="money-text">¥{{ formatMoney(currentRecord?.amount) }}</span>
        </el-form-item>
        <el-form-item 
          v-if="reviewType === 'reject'"
          label="拒绝原因" 
          prop="rejectReason"
        >
          <el-input
            v-model="reviewForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button 
            :type="reviewType === 'approve' ? 'success' : 'danger'" 
            @click="handleReviewSubmit" 
            :loading="reviewLoading"
          >
            {{ reviewType === 'approve' ? '通过' : '拒绝' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getWithdrawalRecordList,
  createWithdrawalRequest,
  reviewWithdrawalRequest,
  completeWithdrawal,
  getDistributorDetail
} from '@/api/distributor'
import { formatDate, formatMoney } from '@/utils'

const router = useRouter()
const route = useRoute()

const distributorId = route.params.id

// 搜索表单
const searchForm = reactive({
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const recordList = ref([])
const loading = ref(false)
const availableAmount = ref(0)

// 创建提现申请对话框
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const createForm = reactive({
  amount: ''
})

const createRules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '提现金额必须大于0', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value > availableAmount.value) {
          callback(new Error('提现金额不能超过可提现金额'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 审核对话框
const reviewDialogVisible = ref(false)
const reviewLoading = ref(false)
const reviewType = ref('approve') // approve | reject
const currentRecord = ref(null)
const reviewFormRef = ref()
const reviewForm = reactive({
  rejectReason: ''
})

const reviewRules = {
  rejectReason: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' }
  ]
}

// 获取提现记录列表
const fetchRecordList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      distributorId: distributorId,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getWithdrawalRecordList(params)
    if (response.code === 200) {
      recordList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取提现记录列表失败:', error)
    ElMessage.error('获取提现记录列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可提现金额
const fetchAvailableAmount = async () => {
  try {
    const response = await getDistributorDetail(distributorId)
    if (response.code === 200) {
      availableAmount.value = response.data.availableCommission || 0
    }
  } catch (error) {
    console.error('获取可提现金额失败:', error)
  }
}

// 状态相关方法
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    completed: 'success',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

// 返回详情页
const goBack = () => {
  router.push(`/distributor/detail/${distributorId}`)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchRecordList()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchRecordList()
}

// 刷新
const handleRefresh = () => {
  fetchRecordList()
  fetchAvailableAmount()
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchRecordList()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchRecordList()
}

// 查看详情
const handleView = (row) => {
  // 这里可以打开详情对话框
  console.log('查看提现记录详情:', row)
}

// 创建提现申请
const handleCreateWithdrawal = () => {
  createDialogVisible.value = true
}

const handleCreateDialogClose = () => {
  createFormRef.value?.resetFields()
  createForm.amount = ''
}

const handleCreateSubmit = async () => {
  try {
    await createFormRef.value?.validate()
    createLoading.value = true
    
    const data = {
      distributorId: distributorId,
      amount: createForm.amount
    }
    
    const response = await createWithdrawalRequest(data)
    if (response.code === 200) {
      ElMessage.success('提现申请创建成功')
      createDialogVisible.value = false
      handleRefresh()
    }
  } catch (error) {
    console.error('创建提现申请失败:', error)
    ElMessage.error(error.response?.data?.message || '创建提现申请失败')
  } finally {
    createLoading.value = false
  }
}

// 审核操作
const handleAction = (command, row) => {
  currentRecord.value = row
  reviewType.value = command
  reviewForm.rejectReason = ''
  reviewDialogVisible.value = true
}

const handleReviewSubmit = async () => {
  try {
    if (reviewType.value === 'reject') {
      await reviewFormRef.value?.validate()
    }
    
    reviewLoading.value = true
    
    const data = {
      approved: reviewType.value === 'approve'
    }
    
    if (reviewType.value === 'reject') {
      data.rejectReason = reviewForm.rejectReason
    }
    
    const response = await reviewWithdrawalRequest(currentRecord.value.id, data)
    if (response.code === 200) {
      ElMessage.success(`提现申请${reviewType.value === 'approve' ? '通过' : '拒绝'}成功`)
      reviewDialogVisible.value = false
      handleRefresh()
    }
  } catch (error) {
    console.error('审核提现申请失败:', error)
    ElMessage.error('审核提现申请失败')
  } finally {
    reviewLoading.value = false
  }
}

// 完成提现
const handleComplete = async (row) => {
  try {
    const { value: transactionNo } = await ElMessageBox.prompt(
      '请输入交易流水号（可选）',
      '完成提现',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入交易流水号'
      }
    )
    
    const response = await completeWithdrawal(row.id, transactionNo)
    if (response.code === 200) {
      ElMessage.success('提现完成成功')
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成提现失败:', error)
      ElMessage.error('完成提现失败')
    }
  }
}

onMounted(() => {
  fetchRecordList()
  fetchAvailableAmount()
})
</script>

<style lang="scss" scoped>
.withdrawal-records {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .money-text {
      font-weight: 500;
      color: #67C23A;
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
}

@media (max-width: 768px) {
  .withdrawal-records {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 10px;
        
        .el-select {
          width: 100% !important;
        }
      }
    }
    
    .pagination-container {
      text-align: center;
      
      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>
