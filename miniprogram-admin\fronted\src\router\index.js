import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'House' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue'),
        meta: { title: '用户列表', icon: 'UserFilled' }
      },
      {
        path: 'detail/:id',
        name: 'UserDetail',
        component: () => import('@/views/user/detail.vue'),
        meta: { title: '用户详情', hidden: true },
        props: true
      },
      {
        path: 'edit/:id',
        name: 'UserEdit',
        component: () => import('@/views/user/edit.vue'),
        meta: { title: '编辑用户', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/province',
    component: Layout,
    redirect: '/province/list',
    meta: { title: '省份管理', icon: 'Location' },
    children: [
      {
        path: 'list',
        name: 'ProvinceList',
        component: () => import('@/views/province/list.vue'),
        meta: { title: '省份列表', icon: 'LocationFilled' }
      },
      {
        path: 'create',
        name: 'ProvinceCreate',
        component: () => import('@/views/province/form.vue'),
        meta: { title: '新增省份', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProvinceEdit',
        component: () => import('@/views/province/form.vue'),
        meta: { title: '编辑省份', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ProvinceDetail',
        component: () => import('@/views/province/detail.vue'),
        meta: { title: '省份详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/city',
    component: Layout,
    redirect: '/city/list',
    meta: { title: '城市管理', icon: 'OfficeBuilding' },
    children: [
      {
        path: 'list',
        name: 'CityList',
        component: () => import('@/views/city/list.vue'),
        meta: { title: '城市列表', icon: 'Building' }
      },
      {
        path: 'create',
        name: 'CityCreate',
        component: () => import('@/views/city/form.vue'),
        meta: { title: '新增城市', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'CityEdit',
        component: () => import('@/views/city/form.vue'),
        meta: { title: '编辑城市', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'CityDetail',
        component: () => import('@/views/city/detail.vue'),
        meta: { title: '城市详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/scenic',
    component: Layout,
    redirect: '/scenic/list',
    meta: { title: '景区管理', icon: 'MapLocation' },
    children: [
      {
        path: 'list',
        name: 'ScenicList',
        component: () => import('@/views/scenic/list.vue'),
        meta: { title: '景区列表', icon: 'Place' }
      },
      {
        path: 'create',
        name: 'ScenicCreate',
        component: () => import('@/views/scenic/form.vue'),
        meta: { title: '新增景区', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ScenicEdit',
        component: () => import('@/views/scenic/form.vue'),
        meta: { title: '编辑景区', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ScenicDetail',
        component: () => import('@/views/scenic/detail.vue'),
        meta: { title: '景区详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/carousel',
    component: Layout,
    redirect: '/carousel/list',
    meta: { title: '轮播图管理', icon: 'Picture' },
    children: [
      {
        path: 'list',
        name: 'CarouselList',
        component: () => import('@/views/carousel/list.vue'),
        meta: { title: '轮播图列表', icon: 'PictureFilled' }
      },
      {
        path: 'create',
        name: 'CarouselCreate',
        component: () => import('@/views/carousel/form.vue'),
        meta: { title: '新增轮播图', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'CarouselEdit',
        component: () => import('@/views/carousel/form.vue'),
        meta: { title: '编辑轮播图', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'CarouselDetail',
        component: () => import('@/views/carousel/detail.vue'),
        meta: { title: '轮播图详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/shouye-carousel',
    component: Layout,
    meta: { title: '首页轮播图管理', icon: 'Monitor' },
    children: [
      {
        path: '',
        name: 'ShouyeCarousel',
        component: () => import('@/views/shouye-carousel/index.vue'),
        meta: { title: '首页轮播图管理', icon: 'Monitor' }
      }
    ]
  },
  {
    path: '/review',
    component: Layout,
    redirect: '/review/list',
    meta: { title: '评价管理', icon: 'ChatDotRound' },
    children: [
      {
        path: 'list',
        name: 'ReviewList',
        component: () => import('@/views/review/list.vue'),
        meta: { title: '评价列表', icon: 'Comment' }
      },
      {
        path: 'create',
        name: 'ReviewCreate',
        component: () => import('@/views/review/form.vue'),
        meta: { title: '新增评价', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ReviewEdit',
        component: () => import('@/views/review/form.vue'),
        meta: { title: '编辑评价', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ReviewDetail',
        component: () => import('@/views/review/detail.vue'),
        meta: { title: '评价详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/guide-product',
    component: Layout,
    redirect: '/guide-product/list',
    meta: { title: '讲解产品管理', icon: 'Headset' },
    children: [
      {
        path: 'list',
        name: 'GuideProductList',
        component: () => import('@/views/guide-product/list.vue'),
        meta: { title: '讲解产品列表', icon: 'Service' }
      },
      {
        path: 'create',
        name: 'GuideProductCreate',
        component: () => import('@/views/guide-product/form.vue'),
        meta: { title: '新增讲解产品', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'GuideProductEdit',
        component: () => import('@/views/guide-product/form.vue'),
        meta: { title: '编辑讲解产品', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'GuideProductDetail',
        component: () => import('@/views/guide-product/detail.vue'),
        meta: { title: '讲解产品详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/explanation-area',
    component: Layout,
    redirect: '/explanation-area/list',
    meta: { title: '讲解区域管理', icon: 'Grid' },
    children: [
      {
        path: 'list',
        name: 'ExplanationAreaList',
        component: () => import('@/views/explanation-area/list.vue'),
        meta: { title: '讲解区域列表', icon: 'Grid' }
      },
      {
        path: 'create',
        name: 'ExplanationAreaCreate',
        component: () => import('@/views/explanation-area/form.vue'),
        meta: { title: '新增讲解区域', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ExplanationAreaEdit',
        component: () => import('@/views/explanation-area/form.vue'),
        meta: { title: '编辑讲解区域', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ExplanationAreaDetail',
        component: () => import('@/views/explanation-area/detail.vue'),
        meta: { title: '讲解区域详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/explanation-point',
    component: Layout,
    redirect: '/explanation-point/list',
    meta: { title: '讲解点管理', icon: 'LocationInformation' },
    children: [
      {
        path: 'list',
        name: 'ExplanationPointList',
        component: () => import('@/views/explanation-point/list.vue'),
        meta: { title: '讲解点列表', icon: 'LocationInformation' }
      },
      {
        path: 'create',
        name: 'ExplanationPointCreate',
        component: () => import('@/views/explanation-point/form.vue'),
        meta: { title: '新增讲解点', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ExplanationPointEdit',
        component: () => import('@/views/explanation-point/form.vue'),
        meta: { title: '编辑讲解点', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ExplanationPointDetail',
        component: () => import('@/views/explanation-point/detail.vue'),
        meta: { title: '讲解点详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/explanation-audio',
    component: Layout,
    redirect: '/explanation-audio/list',
    meta: { title: '讲解音频管理', icon: 'Microphone' },
    children: [
      {
        path: 'list',
        name: 'ExplanationAudioList',
        component: () => import('@/views/explanation-audio/list.vue'),
        meta: { title: '讲解音频列表', icon: 'Microphone' }
      },
      {
        path: 'create',
        name: 'ExplanationAudioCreate',
        component: () => import('@/views/explanation-audio/form.vue'),
        meta: { title: '新增讲解音频', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ExplanationAudioEdit',
        component: () => import('@/views/explanation-audio/form.vue'),
        meta: { title: '编辑讲解音频', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ExplanationAudioDetail',
        component: () => import('@/views/explanation-audio/detail.vue'),
        meta: { title: '讲解音频详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/guide-point',
    component: Layout,
    redirect: '/guide-point/list',
    meta: { title: '原讲解点管理', icon: 'Position' },
    children: [
      {
        path: 'list',
        name: 'GuidePointList',
        component: () => import('@/views/guide-point/list.vue'),
        meta: { title: '原讲解点列表', icon: 'Position' }
      },
      {
        path: 'create',
        name: 'GuidePointCreate',
        component: () => import('@/views/guide-point/form.vue'),
        meta: { title: '新增讲解点', hidden: true }
      },
      {
        path: 'batch-create',
        name: 'GuidePointBatchCreate',
        component: () => import('@/views/guide-point/batch-create.vue'),
        meta: { title: '批量创建讲解点', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'GuidePointEdit',
        component: () => import('@/views/guide-point/form.vue'),
        meta: { title: '编辑讲解点', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'GuidePointDetail',
        component: () => import('@/views/guide-point/detail.vue'),
        meta: { title: '讲解点详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/lecturer',
    component: Layout,
    meta: { title: '讲师管理', icon: 'Avatar' },
    children: [
      {
        path: '',
        name: 'LecturerList',
        component: () => import('@/views/lecturer/index.vue'),
        meta: { title: '讲师管理', icon: 'Avatar' }
      },
      {
        path: 'create',
        name: 'LecturerCreate',
        component: () => import('@/views/lecturer/form.vue'),
        meta: { title: '新增讲师', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'LecturerEdit',
        component: () => import('@/views/lecturer/form.vue'),
        meta: { title: '编辑讲师', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/product',
    component: Layout,
    meta: { title: '产品管理', icon: 'Box' },
    children: [
      {
        path: '',
        name: 'ProductList',
        component: () => import('@/views/product/index.vue'),
        meta: { title: '产品管理', icon: 'Box' }
      },
      {
        path: 'create',
        name: 'ProductCreate',
        component: () => import('@/views/product/form.vue'),
        meta: { title: '新增产品', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProductEdit',
        component: () => import('@/views/product/form.vue'),
        meta: { title: '编辑产品', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/product-bundle',
    component: Layout,
    meta: { title: '组合包管理', icon: 'Collection' },
    children: [
      {
        path: '',
        name: 'ProductBundleList',
        component: () => import('@/views/product-bundle/index.vue'),
        meta: { title: '组合包管理', icon: 'Collection' }
      },
      {
        path: 'create',
        name: 'ProductBundleCreate',
        component: () => import('@/views/product-bundle/form.vue'),
        meta: { title: '新增组合包', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProductBundleEdit',
        component: () => import('@/views/product-bundle/form.vue'),
        meta: { title: '编辑组合包', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    meta: { title: '订单管理', icon: 'Document' },
    children: [
      {
        path: '',
        name: 'OrderList',
        component: () => import('@/views/order/index.vue'),
        meta: { title: '订单管理', icon: 'Document' }
      },
      {
        path: 'detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/detail.vue'),
        meta: { title: '订单详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/distributor',
    component: Layout,
    redirect: '/distributor/list',
    meta: { title: '分销员管理', icon: 'UserFilled' },
    children: [
      {
        path: 'list',
        name: 'DistributorList',
        component: () => import('@/views/distributor/list.vue'),
        meta: { title: '分销员列表', icon: 'User' }
      },
      {
        path: 'detail/:id',
        name: 'DistributorDetail',
        component: () => import('@/views/distributor/detail.vue'),
        meta: { title: '分销员详情', hidden: true },
        props: true
      },
      {
        path: 'edit/:id',
        name: 'DistributorEdit',
        component: () => import('@/views/distributor/edit.vue'),
        meta: { title: '编辑分销员', hidden: true },
        props: true
      },
      {
        path: 'orders/:id',
        name: 'DistributorOrders',
        component: () => import('@/views/distributor/orders.vue'),
        meta: { title: '分销订单', hidden: true },
        props: true
      },
      {
        path: 'withdrawal/:id',
        name: 'DistributorWithdrawal',
        component: () => import('@/views/distributor/withdrawal.vue'),
        meta: { title: '提现记录', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/coupon',
    component: Layout,
    meta: { title: '门票管理', icon: 'Ticket' },
    children: [
      {
        path: '',
        name: 'CouponList',
        component: () => import('@/views/coupon/index.vue'),
        meta: { title: '门票管理', icon: 'Ticket' }
      },
      {
        path: 'detail/:id',
        name: 'CouponDetail',
        component: () => import('@/views/coupon/detail.vue'),
        meta: { title: '门票详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/activation-code',
    component: Layout,
    meta: { title: '激活码管理', icon: 'Key' },
    children: [
      {
        path: '',
        name: 'ActivationCodeList',
        component: () => import('@/views/activation-code/index.vue'),
        meta: { title: '激活码管理', icon: 'Key' }
      },
      {
        path: 'create',
        name: 'ActivationCodeCreate',
        component: () => import('@/views/activation-code/form.vue'),
        meta: { title: '生成激活码', hidden: true }
      }
    ]
  },
  {
    path: '/usage-record',
    component: Layout,
    meta: { title: '使用记录', icon: 'List' },
    children: [
      {
        path: '',
        name: 'UsageRecordList',
        component: () => import('@/views/usage-record/index.vue'),
        meta: { title: '使用记录', icon: 'List' }
      }
    ]
  },
  {
    path: '/relation-management',
    component: Layout,
    meta: { title: '关系管理', icon: 'Connection' },
    children: [
      {
        path: '',
        name: 'RelationManagement',
        component: () => import('@/views/relation-management/index.vue'),
        meta: { title: '关系管理', icon: 'Connection' }
      }
    ]
  },
  {
    path: '/feedback',
    component: Layout,
    meta: { title: '意见反馈', icon: 'ChatLineRound' },
    children: [
      {
        path: '',
        name: 'FeedbackList',
        component: () => import('@/views/feedback/index.vue'),
        meta: { title: '意见反馈', icon: 'ChatLineRound' }
      }
    ]
  },
  {
    path: '/business-cooperation',
    component: Layout,
    meta: { title: '商务合作', icon: 'Briefcase' },
    children: [
      {
        path: '',
        name: 'BusinessCooperationList',
        component: () => import('@/views/business-cooperation/index.vue'),
        meta: { title: '商务合作', icon: 'Briefcase' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '404', hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 旅游讲解小程序管理后台` : '旅游讲解小程序管理后台'
  next()
})

export default router
