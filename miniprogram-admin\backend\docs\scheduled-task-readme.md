# 数字卡券定时任务功能说明

## 概述

本系统实现了数字卡券的定时任务功能，主要用于自动处理过期卡券，确保数据的一致性和准确性。

## 功能特性

### 1. 过期卡券自动处理
- **执行频率**: 每分钟执行一次
- **处理逻辑**: 查询满足以下条件的卡券并将其状态更新为 'used'
  - `used_at` 字段不为null（已被使用）
  - `valid_to` 字段的时间小于当前系统时间（已过期）
  - `status` 字段不等于 'used'（避免重复更新）

### 2. 系统启动时处理
- **执行时机**: 系统启动后30秒执行一次
- **目的**: 确保系统启动后立即处理积累的过期卡券

### 3. 卡券状态统计
- **执行频率**: 每小时执行一次
- **功能**: 统计各种状态的卡券数量，便于监控系统运行状况

## 技术实现

### 核心组件

1. **CouponScheduledTask** - 定时任务执行类
   - 位置: `com.tourism.miniprogram.task.CouponScheduledTask`
   - 功能: 定义和执行各种定时任务

2. **ScheduleConfig** - 定时任务配置类
   - 位置: `com.tourism.miniprogram.config.ScheduleConfig`
   - 功能: 配置定时任务线程池参数

3. **CouponMapper** - 数据访问层扩展
   - 新增方法: `selectExpiredCoupons()` - 查询过期卡券
   - 新增方法: `batchUpdateExpiredCouponsStatus()` - 批量更新状态

4. **CouponService** - 业务逻辑层扩展
   - 新增方法: `processExpiredCoupons()` - 处理过期卡券

### 启用方式

在主应用类 `MiniprogramBackendApplication` 中添加了 `@EnableScheduling` 注解来启用定时任务功能。

```java
@SpringBootApplication
@MapperScan("com.tourism.miniprogram.mapper")
@EnableScheduling  // 启用定时任务
public class MiniprogramBackendApplication {
    // ...
}
```

## 定时任务配置

### Cron表达式说明

- `0 * * * * ?` - 每分钟的第0秒执行（过期卡券处理）
- `0 0 * * * ?` - 每小时的第0分0秒执行（状态统计）

### 线程池配置

- **线程池大小**: 5个线程
- **线程名前缀**: `tourism-scheduled-task-`
- **关闭策略**: 等待任务完成后关闭，最多等待60秒

## 日志记录

### 日志级别
- **INFO**: 正常执行信息，包括处理结果统计
- **DEBUG**: 详细的执行过程信息
- **WARN**: 警告信息，如线程池满载
- **ERROR**: 错误信息，包括异常堆栈

### 日志示例

```
2025-01-06 10:00:00.123 INFO  [tourism-scheduled-task-1] c.t.m.task.CouponScheduledTask : === 开始执行过期卡券处理定时任务 ===
2025-01-06 10:00:00.456 INFO  [tourism-scheduled-task-1] c.t.m.service.impl.CouponServiceImpl : 找到3张过期卡券需要处理
2025-01-06 10:00:00.789 INFO  [tourism-scheduled-task-1] c.t.m.service.impl.CouponServiceImpl : 成功处理3张过期卡券，状态已更新为'used'
2025-01-06 10:00:00.890 INFO  [tourism-scheduled-task-1] c.t.m.task.CouponScheduledTask : === 过期卡券处理定时任务完成 === 处理数量: 3, 耗时: 767ms
```

## 监控和告警

### 性能监控
- 记录每次任务执行的耗时
- 统计处理的卡券数量
- 监控线程池使用情况

### 异常处理
- 所有异常都会被捕获并记录日志
- 异常不会影响后续定时任务的执行
- 预留了告警通知接口，可扩展邮件或短信通知

## 数据库影响

### 查询操作
```sql
-- 查询过期卡券
SELECT * FROM coupons 
WHERE used_at IS NOT NULL 
  AND valid_to < NOW() 
  AND status != 'used'
```

### 更新操作
```sql
-- 批量更新过期卡券状态
UPDATE coupons 
SET status = 'used', updated_at = NOW() 
WHERE id IN (1, 2, 3, ...)
```

## 测试

### 单元测试
- 位置: `src/test/java/com/tourism/miniprogram/task/CouponScheduledTaskTest.java`
- 覆盖场景: 正常处理、无过期卡券、异常处理

### 集成测试建议
1. 创建测试数据（已使用且已过期的卡券）
2. 手动触发定时任务或等待自动执行
3. 验证卡券状态是否正确更新
4. 检查日志输出是否符合预期

## 配置参数

### 可调整参数
- 定时任务执行频率（修改Cron表达式）
- 线程池大小（修改ScheduleConfig中的poolSize）
- 启动延迟时间（修改initialDelay参数）

### 建议配置
- **生产环境**: 每分钟执行一次，确保及时处理
- **测试环境**: 可以调整为更长间隔，减少日志输出
- **开发环境**: 可以设置更短间隔进行调试

## 注意事项

1. **事务处理**: 过期卡券处理使用了 `@Transactional` 注解，确保数据一致性
2. **性能考虑**: 批量更新操作，避免逐条更新造成的性能问题
3. **并发安全**: 使用数据库级别的条件查询，避免并发问题
4. **日志管理**: 建议定期清理日志文件，避免磁盘空间不足
5. **监控告警**: 建议配置监控系统，及时发现定时任务异常

## 扩展功能

### 可扩展的功能点
1. **告警通知**: 添加邮件、短信或钉钉通知
2. **数据统计**: 生成定期的卡券使用报表
3. **自动清理**: 定期清理过期的历史数据
4. **动态配置**: 支持运行时修改定时任务参数
5. **分布式支持**: 在集群环境中避免重复执行
