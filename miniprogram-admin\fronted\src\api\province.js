import request from '@/utils/request'

/**
 * 获取省份列表
 */
export function getProvinceList() {
  return request({
    url: '/provinces',
    method: 'get'
  })
}

/**
 * 分页获取省份列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.name 省份名称
 */
export function getProvincePage(params) {
  return request({
    url: '/provinces/page',
    method: 'get',
    params
  })
}

/**
 * 获取省份详情
 * @param {number} id 省份ID
 */
export function getProvinceInfo(id) {
  return request({
    url: `/provinces/${id}`,
    method: 'get'
  })
}

/**
 * 创建省份
 * @param {Object} data 省份数据
 */
export function createProvince(data) {
  return request({
    url: '/provinces',
    method: 'post',
    data
  })
}

/**
 * 更新省份
 * @param {number} id 省份ID
 * @param {Object} data 省份数据
 */
export function updateProvince(id, data) {
  return request({
    url: `/provinces/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除省份
 * @param {number} id 省份ID
 */
export function deleteProvince(id) {
  return request({
    url: `/provinces/${id}`,
    method: 'delete'
  })
}
