const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    isLoggedIn: false,
    userInfo: null,
    isLoading: false,
    showPhoneAuthModal: false  // 控制手机号授权弹窗显示
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("我的页面加载");
      this.checkUserLoginStatus();
    },
    detached: function () {
      console.info("我的页面卸载");
    },
  },
  methods: {
    // 检查用户登录状态
    checkUserLoginStatus: function() {
      const loginStatus = userService.checkLoginStatus();
      this.setData({
        isLoggedIn: loginStatus.isLoggedIn,
        userInfo: loginStatus.userInfo
      });

      console.log('用户登录状态:', loginStatus);
    },

    // 页面点击事件 - 检测登录状态
    onPageTap: function() {
      if (!this.data.isLoggedIn) {
        this.handleUserLogin();
      } else {
        // 已登录，可以进行其他操作
        console.log('用户已登录，可以访问个人中心功能');
      }
    },

    // 处理用户登录流程
    handleUserLogin: async function() {
      try {
        // 显示登录确认弹窗
        const shouldLogin = await userService.showLoginConfirm();

        if (!shouldLogin) {
          return;
        }

        // 显示加载状态
        this.setData({ isLoading: true });
        wx.showLoading({ title: '登录中...' });

        // 执行微信登录
        const loginResult = await userService.wechatLogin();

        if (loginResult.success) {
          // 登录成功，重新检查登录状态以确保数据一致性
          const loginStatus = userService.checkLoginStatus();

          console.log('登录成功后的状态检查:', loginStatus);

          // 更新页面状态
          this.setData({
            isLoggedIn: loginStatus.isLoggedIn,
            userInfo: loginStatus.userInfo,
            isLoading: false
          });

          wx.hideLoading();
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

          console.log('登录成功，当前用户信息:', loginStatus.userInfo);

          // 显示授权提示（如果需要手机号授权）
          setTimeout(() => {
            this.handleUserAuth();
          }, 1500);

        } else {
          throw new Error(loginResult.error);
        }

      } catch (error) {
        console.error('登录失败:', error);
        this.setData({ isLoading: false });
        wx.hideLoading();
        wx.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        });
      }
    },

    // 处理用户授权流程 - 简化为仅手机号授权
    handleUserAuth: async function() {
      try {
        // 检查是否已有手机号
        if (this.data.userInfo && this.data.userInfo.phone) {
          console.log('用户已有手机号，无需重复授权');
          return;
        }

        // 显示手机号授权弹窗
        this.setData({
          showPhoneAuthModal: true
        });

      } catch (error) {
        console.error('授权流程失败:', error);
        wx.showToast({
          title: error.message || '授权失败',
          icon: 'none'
        });
      }
    },

    // 关闭手机号授权弹窗
    closePhoneAuthModal: function() {
      this.setData({
        showPhoneAuthModal: false
      });
    },

    // 手机号授权按钮点击事件
    onGetPhoneNumber: async function(e) {
      try {
        console.log('手机号授权事件:', e.detail);

        if (e.detail.errMsg !== 'getPhoneNumber:ok') {
          wx.showToast({
            title: '用户取消授权',
            icon: 'none'
          });
          return;
        }

        wx.showLoading({ title: '获取手机号中...' });

        // 调用新的手机号解密API
        let phoneResult;
        try {
          phoneResult = await this.decryptPhoneNumber({
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv
          });
        } catch (error) {
          console.error('主要解密方法失败，尝试备用方法:', error);
          // 尝试备用方法：直接使用userService
          phoneResult = await userService.decryptPhoneByServer({
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv
          });
        }

        // 更新用户信息到后端
        await userService.updateUserInfo({
          phone: phoneResult.phoneNumber
        });

        // 更新本地显示
        const updatedUserInfo = {
          ...this.data.userInfo,
          phone: phoneResult.phoneNumber
        };

        this.setData({
          userInfo: updatedUserInfo,
          showPhoneAuthModal: false  // 关闭授权弹窗
        });

        wx.hideLoading();
        wx.showToast({
          title: '手机号授权成功',
          icon: 'success'
        });

      } catch (error) {
        console.error('手机号授权失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: error.message || '手机号授权失败',
          icon: 'none'
        });
      }
    },

    // 调用手机号解密API（带重试机制）
    decryptPhoneNumber: async function(params, retryCount = 0) {
      const { token } = userService.checkLoginStatus();
      const maxRetries = 2;

      return new Promise((resolve, reject) => {
        // 先获取新的登录code，因为手机号解密需要新的code
        wx.login({
          success: (loginRes) => {
            if (loginRes.code) {
              console.log(`尝试手机号解密，重试次数: ${retryCount}, 登录code: ${loginRes.code}`);

              // 使用新的登录code进行手机号解密
              wx.request({
                url: `${userService.baseUrl}/api/auth/wechat/decrypt-phone`,
                method: 'POST',
                data: {
                  code: loginRes.code,  // 使用新获取的登录code
                  encryptedData: params.encryptedData,
                  iv: params.iv
                },
                header: {
                  'Content-Type': 'application/json',
                  'Authorization': token ? `Bearer ${token}` : undefined
                },
                timeout: 10000, // 10秒超时
                success: (res) => {
                  console.log('手机号解密API响应:', res);
                  if (res.statusCode === 200 && res.data.code === 200) {
                    resolve(res.data.data);
                  } else {
                    console.error('API返回错误:', res.data);

                    // 如果是code无效且还有重试次数，则重试
                    if ((res.data.message && res.data.message.includes('code')) && retryCount < maxRetries) {
                      console.log(`code无效，准备重试 (${retryCount + 1}/${maxRetries})`);
                      setTimeout(() => {
                        this.decryptPhoneNumber(params, retryCount + 1)
                          .then(resolve)
                          .catch(reject);
                      }, 1000); // 延迟1秒重试
                    } else {
                      reject(new Error(res.data.message || '手机号解密失败'));
                    }
                  }
                },
                fail: (err) => {
                  console.error('手机号解密API请求失败:', err);

                  // 网络错误也可以重试
                  if (retryCount < maxRetries) {
                    console.log(`网络请求失败，准备重试 (${retryCount + 1}/${maxRetries})`);
                    setTimeout(() => {
                      this.decryptPhoneNumber(params, retryCount + 1)
                        .then(resolve)
                        .catch(reject);
                    }, 1000);
                  } else {
                    reject(new Error('网络请求失败: ' + err.errMsg));
                  }
                }
              });
            } else {
              reject(new Error('获取登录code失败'));
            }
          },
          fail: (err) => {
            console.error('wx.login失败:', err);
            reject(new Error('获取登录code失败: ' + err.errMsg));
          }
        });
      });
    },

    // 退出登录
    onLogout: function() {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            const success = userService.logout();
            if (success) {
              this.setData({
                isLoggedIn: false,
                userInfo: null
              });
              wx.showToast({
                title: '已退出登录',
                icon: 'success'
              });
            }
          }
        }
      });
    },

    // 我的卡券点击事件
    onMyCardsClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_youhuiquanyemiankeshiyong/component'
      });
    },

    // 我的激活码点击事件
    onMyActivationCodeClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_jihuoma/component'
      });
    },

    // 去激活点击事件
    onActivateClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_shiyongjihuoma/component'
      });
    },

    // 我的订单点击事件
    onMyOrdersClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_quanbudingdan/component'
      });
    },

    // 订单状态点击事件 - 跳转到对应的订单tab
    onOrderStatusClick: function(e) {
      const status = e.currentTarget.dataset.status;
      wx.navigateTo({
        url: `/pages/lanhu_quanbudingdan/component?status=${status}`
      });
    },

    // 意见反馈点击事件
    onFeedbackClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_yijianfankui/component'
      });
    },

    // 商务合作点击事件
    onBusinessCooperationClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_shangwuhezuo/component'
      });
    },

    // 我的分销点击事件
    onMyDistributionClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_fenxiaozhongxin/component'
      });
    },

    // 设置点击事件
    onSettingsClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_shezhi/component'
      });
    }
  },
});
