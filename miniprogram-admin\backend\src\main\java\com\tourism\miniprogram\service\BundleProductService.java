package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.BundleProduct;

import java.util.List;

/**
 * 组合包产品关联服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface BundleProductService extends IService<BundleProduct> {

    /**
     * 根据组合包ID获取产品关联列表
     *
     * @param bundleId 组合包ID
     * @return 产品关联列表
     */
    List<BundleProduct> getByBundleId(Integer bundleId);

    /**
     * 根据产品ID获取组合包关联列表
     *
     * @param productId 产品ID
     * @return 组合包关联列表
     */
    List<BundleProduct> getByProductId(Integer productId);

    /**
     * 批量添加组合包产品关联
     *
     * @param bundleId 组合包ID
     * @param productIds 产品ID列表
     * @param quantities 数量列表
     * @param scenicIds 景区ID列表
     * @return 是否成功
     */
    boolean batchAddBundleProducts(Integer bundleId, List<Integer> productIds, List<Integer> quantities, List<Integer> scenicIds);

    /**
     * 删除组合包的所有产品关联
     *
     * @param bundleId 组合包ID
     * @return 是否成功
     */
    boolean removeByBundleId(Integer bundleId);
}
