package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.DistributionProductCommissionRate;
import com.tourism.miniprogram.entity.Distributor;
import com.tourism.miniprogram.mapper.DistributionProductCommissionRateMapper;
import com.tourism.miniprogram.service.DistributionProductCommissionRateService;
import com.tourism.miniprogram.service.DistributorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销产品佣金比例服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class DistributionProductCommissionRateServiceImpl extends ServiceImpl<DistributionProductCommissionRateMapper, DistributionProductCommissionRate> 
        implements DistributionProductCommissionRateService {

    @Autowired
    private DistributorService distributorService;

    @Override
    public List<DistributionProductCommissionRate> getByDistributorId(Integer distributorId) {
        try {
            return baseMapper.selectByDistributorId(distributorId);
        } catch (Exception e) {
            log.error("根据分销员ID获取佣金比例列表失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取佣金比例列表失败");
        }
    }

    @Override
    public DistributionProductCommissionRate getByDistributorIdAndProductId(Integer distributorId, Integer productId) {
        try {
            return baseMapper.selectByDistributorIdAndProductId(distributorId, productId);
        } catch (Exception e) {
            log.error("根据分销员ID和产品ID获取佣金比例失败，distributorId: {}, productId: {}", 
                    distributorId, productId, e);
            throw new RuntimeException("获取佣金比例失败");
        }
    }

    @Override
    public DistributionProductCommissionRate getByDistributorIdAndBundleId(Integer distributorId, Integer bundleId) {
        try {
            return baseMapper.selectByDistributorIdAndBundleId(distributorId, bundleId);
        } catch (Exception e) {
            log.error("根据分销员ID和组合包ID获取佣金比例失败，distributorId: {}, bundleId: {}", 
                    distributorId, bundleId, e);
            throw new RuntimeException("获取佣金比例失败");
        }
    }

    @Override
    public List<DistributionProductCommissionRate> getByProductId(Integer productId) {
        try {
            return baseMapper.selectByProductId(productId);
        } catch (Exception e) {
            log.error("根据产品ID获取佣金比例列表失败，productId: {}", productId, e);
            throw new RuntimeException("获取佣金比例列表失败");
        }
    }

    @Override
    public List<DistributionProductCommissionRate> getByBundleId(Integer bundleId) {
        try {
            return baseMapper.selectByBundleId(bundleId);
        } catch (Exception e) {
            log.error("根据组合包ID获取佣金比例列表失败，bundleId: {}", bundleId, e);
            throw new RuntimeException("获取佣金比例列表失败");
        }
    }

    @Override
    @Transactional
    public DistributionProductCommissionRate setProductCommissionRate(Integer distributorId, Integer productId, BigDecimal commissionRate) {
        try {
            log.info("设置分销员产品佣金比例，distributorId: {}, productId: {}, commissionRate: {}", 
                    distributorId, productId, commissionRate);

            // 检查是否已存在
            DistributionProductCommissionRate existingRate = getByDistributorIdAndProductId(distributorId, productId);
            
            if (existingRate != null) {
                // 更新现有记录
                existingRate.setCommissionRate(commissionRate);
                boolean success = updateById(existingRate);
                if (success) {
                    log.info("产品佣金比例更新成功，rateId: {}", existingRate.getId());
                    return existingRate;
                } else {
                    log.error("产品佣金比例更新失败，rateId: {}", existingRate.getId());
                    throw new RuntimeException("产品佣金比例更新失败");
                }
            } else {
                // 创建新记录
                DistributionProductCommissionRate newRate = new DistributionProductCommissionRate();
                newRate.setDistributorId(distributorId);
                newRate.setProductId(productId);
                newRate.setCommissionRate(commissionRate);

                boolean success = save(newRate);
                if (success) {
                    log.info("产品佣金比例创建成功，rateId: {}", newRate.getId());
                    return newRate;
                } else {
                    log.error("产品佣金比例创建失败");
                    throw new RuntimeException("产品佣金比例创建失败");
                }
            }
        } catch (Exception e) {
            log.error("设置分销员产品佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            throw new RuntimeException("设置产品佣金比例失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DistributionProductCommissionRate setBundleCommissionRate(Integer distributorId, Integer bundleId, BigDecimal commissionRate) {
        try {
            log.info("设置分销员组合包佣金比例，distributorId: {}, bundleId: {}, commissionRate: {}", 
                    distributorId, bundleId, commissionRate);

            // 检查是否已存在
            DistributionProductCommissionRate existingRate = getByDistributorIdAndBundleId(distributorId, bundleId);
            
            if (existingRate != null) {
                // 更新现有记录
                existingRate.setCommissionRate(commissionRate);
                boolean success = updateById(existingRate);
                if (success) {
                    log.info("组合包佣金比例更新成功，rateId: {}", existingRate.getId());
                    return existingRate;
                } else {
                    log.error("组合包佣金比例更新失败，rateId: {}", existingRate.getId());
                    throw new RuntimeException("组合包佣金比例更新失败");
                }
            } else {
                // 创建新记录
                DistributionProductCommissionRate newRate = new DistributionProductCommissionRate();
                newRate.setDistributorId(distributorId);
                newRate.setBundleId(bundleId);
                newRate.setCommissionRate(commissionRate);

                boolean success = save(newRate);
                if (success) {
                    log.info("组合包佣金比例创建成功，rateId: {}", newRate.getId());
                    return newRate;
                } else {
                    log.error("组合包佣金比例创建失败");
                    throw new RuntimeException("组合包佣金比例创建失败");
                }
            }
        } catch (Exception e) {
            log.error("设置分销员组合包佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            throw new RuntimeException("设置组合包佣金比例失败: " + e.getMessage());
        }
    }

    @Override
    public BigDecimal getEffectiveProductCommissionRate(Integer distributorId, Integer productId) {
        try {
            // 优先使用特定产品佣金比例
            DistributionProductCommissionRate specificRate = getByDistributorIdAndProductId(distributorId, productId);
            if (specificRate != null) {
                log.debug("使用特定产品佣金比例，distributorId: {}, productId: {}, rate: {}", 
                        distributorId, productId, specificRate.getCommissionRate());
                return specificRate.getCommissionRate();
            }

            // 使用分销员默认比例
            Distributor distributor = distributorService.getById(distributorId);
            if (distributor != null) {
                log.debug("使用分销员默认佣金比例，distributorId: {}, productId: {}, rate: {}", 
                        distributorId, productId, distributor.getCommissionRate());
                return distributor.getCommissionRate();
            }

            // 返回系统默认比例
            BigDecimal defaultRate = new BigDecimal("10.00");
            log.debug("使用系统默认佣金比例，distributorId: {}, productId: {}, rate: {}", 
                    distributorId, productId, defaultRate);
            return defaultRate;
        } catch (Exception e) {
            log.error("获取有效产品佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            return new BigDecimal("10.00"); // 返回默认比例
        }
    }

    @Override
    public BigDecimal getEffectiveBundleCommissionRate(Integer distributorId, Integer bundleId) {
        try {
            // 优先使用特定组合包佣金比例
            DistributionProductCommissionRate specificRate = getByDistributorIdAndBundleId(distributorId, bundleId);
            if (specificRate != null) {
                log.debug("使用特定组合包佣金比例，distributorId: {}, bundleId: {}, rate: {}", 
                        distributorId, bundleId, specificRate.getCommissionRate());
                return specificRate.getCommissionRate();
            }

            // 使用分销员默认比例
            Distributor distributor = distributorService.getById(distributorId);
            if (distributor != null) {
                log.debug("使用分销员默认佣金比例，distributorId: {}, bundleId: {}, rate: {}", 
                        distributorId, bundleId, distributor.getCommissionRate());
                return distributor.getCommissionRate();
            }

            // 返回系统默认比例
            BigDecimal defaultRate = new BigDecimal("10.00");
            log.debug("使用系统默认佣金比例，distributorId: {}, bundleId: {}, rate: {}", 
                    distributorId, bundleId, defaultRate);
            return defaultRate;
        } catch (Exception e) {
            log.error("获取有效组合包佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            return new BigDecimal("10.00"); // 返回默认比例
        }
    }

    @Override
    public boolean hasProductSpecificRate(Integer distributorId, Integer productId) {
        try {
            return baseMapper.existsByDistributorIdAndProductId(distributorId, productId);
        } catch (Exception e) {
            log.error("检查分销员是否有产品特定佣金比例失败，distributorId: {}, productId: {}", 
                    distributorId, productId, e);
            return false;
        }
    }

    @Override
    public boolean hasBundleSpecificRate(Integer distributorId, Integer bundleId) {
        try {
            return baseMapper.existsByDistributorIdAndBundleId(distributorId, bundleId);
        } catch (Exception e) {
            log.error("检查分销员是否有组合包特定佣金比例失败，distributorId: {}, bundleId: {}", 
                    distributorId, bundleId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean removeProductCommissionRate(Integer distributorId, Integer productId) {
        try {
            log.info("删除分销员产品佣金比例，distributorId: {}, productId: {}", distributorId, productId);

            QueryWrapper<DistributionProductCommissionRate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("distributor_id", distributorId)
                       .eq("product_id", productId);

            boolean success = remove(queryWrapper);
            if (success) {
                log.info("产品佣金比例删除成功，distributorId: {}, productId: {}", distributorId, productId);
            } else {
                log.warn("产品佣金比例删除失败或不存在，distributorId: {}, productId: {}", distributorId, productId);
            }

            return success;
        } catch (Exception e) {
            log.error("删除分销员产品佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            throw new RuntimeException("删除产品佣金比例失败");
        }
    }

    @Override
    @Transactional
    public boolean removeBundleCommissionRate(Integer distributorId, Integer bundleId) {
        try {
            log.info("删除分销员组合包佣金比例，distributorId: {}, bundleId: {}", distributorId, bundleId);

            QueryWrapper<DistributionProductCommissionRate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("distributor_id", distributorId)
                       .eq("bundle_id", bundleId);

            boolean success = remove(queryWrapper);
            if (success) {
                log.info("组合包佣金比例删除成功，distributorId: {}, bundleId: {}", distributorId, bundleId);
            } else {
                log.warn("组合包佣金比例删除失败或不存在，distributorId: {}, bundleId: {}", distributorId, bundleId);
            }

            return success;
        } catch (Exception e) {
            log.error("删除分销员组合包佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            throw new RuntimeException("删除组合包佣金比例失败");
        }
    }
}
