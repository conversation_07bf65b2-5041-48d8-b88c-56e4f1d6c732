package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.DistributionWithdrawalRecord;
import com.tourism.miniprogram.entity.Distributor;
import com.tourism.miniprogram.mapper.DistributionWithdrawalRecordMapper;
import com.tourism.miniprogram.service.DistributionWithdrawalRecordService;
import com.tourism.miniprogram.service.DistributorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分销提现记录服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class DistributionWithdrawalRecordServiceImpl extends ServiceImpl<DistributionWithdrawalRecordMapper, DistributionWithdrawalRecord> 
        implements DistributionWithdrawalRecordService {

    @Autowired
    private DistributorService distributorService;

    @Override
    public List<DistributionWithdrawalRecord> getByDistributorId(Integer distributorId) {
        try {
            return baseMapper.selectByDistributorId(distributorId);
        } catch (Exception e) {
            log.error("根据分销员ID获取提现记录列表失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取提现记录列表失败");
        }
    }

    @Override
    public List<DistributionWithdrawalRecord> getByStatus(String status) {
        try {
            return baseMapper.selectByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取提现记录列表失败，status: {}", status, e);
            throw new RuntimeException("获取提现记录列表失败");
        }
    }

    @Override
    public List<DistributionWithdrawalRecord> getByDistributorIdAndStatus(Integer distributorId, String status) {
        try {
            return baseMapper.selectByDistributorIdAndStatus(distributorId, status);
        } catch (Exception e) {
            log.error("根据分销员ID和状态获取提现记录列表失败，distributorId: {}, status: {}", distributorId, status, e);
            throw new RuntimeException("获取提现记录列表失败");
        }
    }

    @Override
    @Transactional
    public DistributionWithdrawalRecord createWithdrawalRequest(Integer distributorId, BigDecimal amount) {
        try {
            log.info("创建提现申请，distributorId: {}, amount: {}", distributorId, amount);

            // 检查分销员是否存在
            Distributor distributor = distributorService.getById(distributorId);
            if (distributor == null) {
                log.error("分销员不存在，distributorId: {}", distributorId);
                throw new RuntimeException("分销员不存在");
            }

            // 检查分销员状态
            if (!"active".equals(distributor.getStatus())) {
                log.error("分销员状态不允许提现，distributorId: {}, status: {}", distributorId, distributor.getStatus());
                throw new RuntimeException("分销员状态不允许提现");
            }

            // 检查可提现金额
            if (!canWithdraw(distributorId, amount)) {
                log.error("可提现金额不足，distributorId: {}, requestAmount: {}, availableAmount: {}", 
                        distributorId, amount, distributor.getAvailableCommission());
                throw new RuntimeException("可提现金额不足");
            }

            // 创建提现记录
            DistributionWithdrawalRecord record = new DistributionWithdrawalRecord();
            record.setDistributorId(distributorId);
            record.setAmount(amount);
            record.setStatus("pending");
            record.setAppliedAt(LocalDateTime.now());

            boolean success = save(record);
            if (success) {
                // 更新分销员可提现金额（减少）
                BigDecimal newAvailableCommission = distributor.getAvailableCommission().subtract(amount);
                distributor.setAvailableCommission(newAvailableCommission);
                distributorService.updateById(distributor);

                log.info("提现申请创建成功，recordId: {}, distributorId: {}, amount: {}", 
                        record.getId(), distributorId, amount);
                return record;
            } else {
                log.error("提现申请创建失败，数据库保存失败");
                throw new RuntimeException("提现申请创建失败");
            }
        } catch (Exception e) {
            log.error("创建提现申请失败，distributorId: {}, amount: {}", distributorId, amount, e);
            throw new RuntimeException("创建提现申请失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean reviewWithdrawalRequest(Integer withdrawalId, boolean approved, String rejectReason) {
        try {
            log.info("审核提现申请，withdrawalId: {}, approved: {}, rejectReason: {}", 
                    withdrawalId, approved, rejectReason);

            DistributionWithdrawalRecord record = getById(withdrawalId);
            if (record == null) {
                log.error("提现记录不存在，withdrawalId: {}", withdrawalId);
                return false;
            }

            if (!"pending".equals(record.getStatus())) {
                log.error("提现记录状态不允许审核，withdrawalId: {}, status: {}", withdrawalId, record.getStatus());
                return false;
            }

            if (approved) {
                record.setStatus("completed");
                record.setCompletedAt(LocalDateTime.now());
                record.setTransactionNo(generateTransactionNo());
                
                // 更新分销员已提现金额
                Distributor distributor = distributorService.getById(record.getDistributorId());
                if (distributor != null) {
                    BigDecimal newWithdrawnCommission = distributor.getWithdrawnCommission().add(record.getAmount());
                    distributor.setWithdrawnCommission(newWithdrawnCommission);
                    distributorService.updateById(distributor);
                }
            } else {
                record.setStatus("rejected");
                record.setRejectReason(rejectReason);
                
                // 恢复分销员可提现金额
                Distributor distributor = distributorService.getById(record.getDistributorId());
                if (distributor != null) {
                    BigDecimal newAvailableCommission = distributor.getAvailableCommission().add(record.getAmount());
                    distributor.setAvailableCommission(newAvailableCommission);
                    distributorService.updateById(distributor);
                }
            }

            boolean success = updateById(record);
            if (success) {
                log.info("提现申请审核成功，withdrawalId: {}, approved: {}", withdrawalId, approved);
            } else {
                log.error("提现申请审核失败，withdrawalId: {}", withdrawalId);
            }

            return success;
        } catch (Exception e) {
            log.error("审核提现申请失败，withdrawalId: {}", withdrawalId, e);
            throw new RuntimeException("审核提现申请失败");
        }
    }

    @Override
    @Transactional
    public boolean completeWithdrawal(Integer withdrawalId, String transactionNo) {
        try {
            log.info("完成提现处理，withdrawalId: {}, transactionNo: {}", withdrawalId, transactionNo);

            DistributionWithdrawalRecord record = getById(withdrawalId);
            if (record == null) {
                log.error("提现记录不存在，withdrawalId: {}", withdrawalId);
                return false;
            }

            if (!"pending".equals(record.getStatus())) {
                log.error("提现记录状态不允许完成，withdrawalId: {}, status: {}", withdrawalId, record.getStatus());
                return false;
            }

            record.setStatus("completed");
            record.setTransactionNo(transactionNo);
            record.setCompletedAt(LocalDateTime.now());

            boolean success = updateById(record);
            if (success) {
                // 更新分销员已提现金额
                Distributor distributor = distributorService.getById(record.getDistributorId());
                if (distributor != null) {
                    BigDecimal newWithdrawnCommission = distributor.getWithdrawnCommission().add(record.getAmount());
                    distributor.setWithdrawnCommission(newWithdrawnCommission);
                    distributorService.updateById(distributor);
                }

                log.info("提现处理完成，withdrawalId: {}, transactionNo: {}", withdrawalId, transactionNo);
            } else {
                log.error("提现处理失败，withdrawalId: {}", withdrawalId);
            }

            return success;
        } catch (Exception e) {
            log.error("完成提现处理失败，withdrawalId: {}", withdrawalId, e);
            throw new RuntimeException("完成提现处理失败");
        }
    }

    @Override
    public BigDecimal getPendingWithdrawalAmount(Integer distributorId) {
        try {
            return baseMapper.getPendingWithdrawalAmount(distributorId);
        } catch (Exception e) {
            log.error("获取分销员待处理提现金额失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取待处理提现金额失败");
        }
    }

    @Override
    public BigDecimal getCompletedWithdrawalAmount(Integer distributorId) {
        try {
            return baseMapper.getCompletedWithdrawalAmount(distributorId);
        } catch (Exception e) {
            log.error("获取分销员已完成提现金额失败，distributorId: {}", distributorId, e);
            throw new RuntimeException("获取已完成提现金额失败");
        }
    }

    @Override
    public DistributionWithdrawalRecord getByTransactionNo(String transactionNo) {
        try {
            return baseMapper.selectByTransactionNo(transactionNo);
        } catch (Exception e) {
            log.error("根据交易流水号获取提现记录失败，transactionNo: {}", transactionNo, e);
            throw new RuntimeException("获取提现记录失败");
        }
    }

    @Override
    public BigDecimal getTotalWithdrawalAmount() {
        try {
            return baseMapper.getTotalWithdrawalAmount();
        } catch (Exception e) {
            log.error("获取系统总提现金额失败", e);
            throw new RuntimeException("获取系统总提现金额失败");
        }
    }

    @Override
    public Integer getPendingWithdrawalCount() {
        try {
            return baseMapper.countPendingWithdrawals();
        } catch (Exception e) {
            log.error("获取待处理提现记录数量失败", e);
            throw new RuntimeException("获取待处理提现记录数量失败");
        }
    }

    @Override
    public String generateTransactionNo() {
        // 生成格式：WD + 时间戳 + 随机4位数字
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomNum = String.format("%04d", (int)(Math.random() * 10000));
        return "WD" + timestamp + randomNum;
    }

    @Override
    public boolean canWithdraw(Integer distributorId, BigDecimal amount) {
        try {
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }

            Distributor distributor = distributorService.getById(distributorId);
            if (distributor == null) {
                return false;
            }

            // 检查分销员状态
            if (!"active".equals(distributor.getStatus())) {
                return false;
            }

            // 检查可提现金额
            return distributor.getAvailableCommission().compareTo(amount) >= 0;
        } catch (Exception e) {
            log.error("检查分销员是否可以提现失败，distributorId: {}, amount: {}", distributorId, amount, e);
            return false;
        }
    }
}
