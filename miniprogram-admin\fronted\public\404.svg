<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="400" height="300" fill="#f5f5f5"/>
  
  <!-- 404文字 -->
  <text x="200" y="150" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="url(#grad1)">404</text>
  
  <!-- 装饰元素 -->
  <circle cx="100" cy="80" r="20" fill="#409EFF" opacity="0.3"/>
  <circle cx="320" cy="220" r="15" fill="#67C23A" opacity="0.3"/>
  <rect x="50" y="200" width="30" height="30" fill="#E6A23C" opacity="0.3" transform="rotate(45 65 215)"/>
  
  <!-- 说明文字 -->
  <text x="200" y="200" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="#606266">页面不存在</text>
</svg>
