<template>
  <div class="relation-container">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="selectedProductId"
            placeholder="选择产品"
            filterable
            clearable
            style="width: 100%"
            @change="handleProductChange"
          >
            <el-option
              v-for="product in productList"
              :key="product.productId"
              :label="product.title"
              :value="product.productId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="selectedAreaId"
            placeholder="选择区域"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="area in areaList"
              :key="area.areaId"
              :label="area.areaName"
              :value="area.areaId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleAddRelation" :disabled="!selectedProductId || !selectedAreaId">
            添加关系
          </el-button>
          <el-button @click="handleRefresh">刷新</el-button>
          <el-button type="success" @click="batchDialogVisible = true">批量添加</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 关系列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>产品-区域关系列表</span>
          <span class="count-badge">共 {{ relationList.length }} 条关系</span>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="relationList"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="relationId" label="关系ID" width="100" />
        <el-table-column prop="productId" label="产品ID" width="100" />
        <el-table-column prop="productName" label="产品名称" min-width="200" />
        <el-table-column prop="areaId" label="区域ID" width="100" />
        <el-table-column prop="areaName" label="区域名称" min-width="150" />
        <el-table-column prop="sortOrder" label="排序" width="120" sortable="custom">
          <template #default="{ row }">
            <el-input-number
              v-model="row.sortOrder"
              :min="0"
              :max="999"
              size="small"
              @change="handleSortOrderChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewAreaPoints(row.areaId)"
            >
              查看讲解点
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteRelation(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量添加产品-区域关系"
      width="600px"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="选择产品">
          <el-select
            v-model="batchForm.productId"
            placeholder="请选择产品"
            style="width: 100%"
          >
            <el-option
              v-for="product in productList"
              :key="product.productId"
              :label="product.title"
              :value="product.productId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择区域">
          <el-select
            v-model="batchForm.areaIds"
            placeholder="请选择区域"
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="area in areaList"
              :key="area.areaId"
              :label="area.areaName"
              :value="area.areaId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchAdd" :loading="batchLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 区域讲解点查看对话框 -->
    <el-dialog
      v-model="viewPointsDialogVisible"
      title="区域包含的讲解点"
      width="800px"
    >
      <div v-if="selectedAreaName" class="dialog-header">
        <h4>区域：{{ selectedAreaName }}</h4>
      </div>

      <el-table
        v-loading="pointsLoading"
        :data="areaPointsList"
        style="width: 100%"
      >
        <el-table-column prop="pointId" label="讲解点ID" width="100" />
        <el-table-column prop="pointName" label="讲解点名称" min-width="200" />
        <el-table-column prop="pointImage" label="图片" width="120">
          <template #default="{ row }">
            <el-image
              v-if="row.pointImage"
              :src="row.pointImage"
              style="width: 80px; height: 50px"
              fit="cover"
              :preview-src-list="[row.pointImage]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="100" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewPointAudios(row.pointId)"
            >
              查看音频
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="viewPointsDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 讲解点音频查看对话框 -->
    <el-dialog
      v-model="viewAudiosDialogVisible"
      title="讲解点包含的音频"
      width="800px"
    >
      <div v-if="selectedPointName" class="dialog-header">
        <h4>讲解点：{{ selectedPointName }}</h4>
      </div>

      <el-table
        v-loading="audiosLoading"
        :data="pointAudiosList"
        style="width: 100%"
      >
        <el-table-column prop="audioId" label="音频ID" width="100" />
        <el-table-column prop="audioName" label="音频名称" min-width="200" />
        <el-table-column prop="duration" label="时长(秒)" width="100" />
        <el-table-column prop="audioImage" label="音频图片" width="120">
          <template #default="{ row }">
            <el-image
              v-if="row.audioImage"
              :src="row.audioImage"
              style="width: 80px; height: 50px"
              fit="cover"
              :preview-src-list="[row.audioImage]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="100" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.audioUrl"
              type="primary"
              size="small"
              @click="playAudio(row.audioUrl)"
            >
              播放
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="viewAudiosDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" controls style="display: none;"></audio>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAreasByProductId,
  addProductAreaRelation,
  removeProductAreaRelation,
  updateProductAreaSortOrder,
  batchAddProductAreaRelations,
  getAllAreas
} from '@/api/relations'
import { getGuideProductPage } from '@/api/guideProduct'
import request from '@/utils/request'

// 响应式数据
const loading = ref(false)
const relationList = ref([])
const productList = ref([])
const areaList = ref([])
const selectedProductId = ref('')
const selectedAreaId = ref('')

// 批量操作
const batchDialogVisible = ref(false)
const batchLoading = ref(false)
const batchForm = reactive({
  productId: '',
  areaIds: []
})

// 查看功能
const viewPointsDialogVisible = ref(false)
const viewAudiosDialogVisible = ref(false)
const pointsLoading = ref(false)
const audiosLoading = ref(false)
const areaPointsList = ref([])
const pointAudiosList = ref([])
const selectedAreaName = ref('')
const selectedPointName = ref('')
const audioPlayer = ref()

// 获取产品列表
const fetchProductList = async () => {
  try {
    const response = await getGuideProductPage({ current: 1, size: 1000 })
    if (response.code === 200) {
      productList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  }
}

// 获取区域列表
const fetchAreaList = async () => {
  try {
    const response = await getAllAreas()
    if (response.code === 200) {
      areaList.value = response.data || []
    }
  } catch (error) {
    console.error('获取区域列表失败:', error)
    ElMessage.error('获取区域列表失败')
  }
}

// 获取关系列表
const fetchRelationList = async () => {
  if (!selectedProductId.value) {
    relationList.value = []
    return
  }

  try {
    loading.value = true
    const response = await getAreasByProductId(selectedProductId.value)
    if (response.code === 200) {
      relationList.value = response.data || []
    }
  } catch (error) {
    console.error('获取关系列表失败:', error)
    ElMessage.error('获取关系列表失败')
  } finally {
    loading.value = false
  }
}

// 产品选择变化
const handleProductChange = () => {
  fetchRelationList()
}

// 添加关系
const handleAddRelation = async () => {
  try {
    const data = {
      productId: selectedProductId.value,
      areaId: selectedAreaId.value,
      sortOrder: 0
    }
    
    const response = await addProductAreaRelation(data)
    if (response.code === 200) {
      ElMessage.success('添加关系成功')
      selectedAreaId.value = ''
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '添加关系失败')
    }
  } catch (error) {
    console.error('添加关系失败:', error)
    ElMessage.error('添加关系失败')
  }
}

// 删除关系
const handleDeleteRelation = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个关系吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await removeProductAreaRelation(row.productId, row.areaId)
    if (response.code === 200) {
      ElMessage.success('删除关系成功')
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '删除关系失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除关系失败:', error)
      ElMessage.error('删除关系失败')
    }
  }
}

// 排序变化
const handleSortOrderChange = async (row) => {
  try {
    const response = await updateProductAreaSortOrder(row.relationId, row.sortOrder)
    if (response.code === 200) {
      ElMessage.success('更新排序成功')
    } else {
      ElMessage.error(response.message || '更新排序失败')
    }
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
  }
}

// 表格排序
const handleSortChange = ({ prop, order }) => {
  if (prop === 'sortOrder') {
    if (order === 'ascending') {
      relationList.value.sort((a, b) => a.sortOrder - b.sortOrder)
    } else if (order === 'descending') {
      relationList.value.sort((a, b) => b.sortOrder - a.sortOrder)
    }
  }
}

// 批量添加
const handleBatchAdd = async () => {
  if (!batchForm.productId || !batchForm.areaIds.length) {
    ElMessage.warning('请选择产品和区域')
    return
  }

  try {
    batchLoading.value = true
    const relations = batchForm.areaIds.map((areaId, index) => ({
      productId: batchForm.productId,
      areaId: areaId,
      sortOrder: index
    }))

    const response = await batchAddProductAreaRelations(relations)
    if (response.code === 200) {
      ElMessage.success('批量添加关系成功')
      batchDialogVisible.value = false
      batchForm.productId = ''
      batchForm.areaIds = []
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '批量添加关系失败')
    }
  } catch (error) {
    console.error('批量添加关系失败:', error)
    ElMessage.error('批量添加关系失败')
  } finally {
    batchLoading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  fetchRelationList()
}

// 获取产品名称
const getProductName = (productId) => {
  const product = productList.value.find(p => p.productId === productId)
  return product ? product.title : `产品${productId}`
}

// 获取区域名称
const getAreaName = (areaId) => {
  const area = areaList.value.find(a => a.areaId === areaId)
  return area ? area.areaName : `区域${areaId}`
}

// 查看区域的讲解点
const handleViewAreaPoints = async (areaId) => {
  try {
    pointsLoading.value = true
    selectedAreaName.value = getAreaName(areaId)

    const response = await request({
      url: `/relations/area-point/area/${areaId}`,
      method: 'get'
    })

    if (response.code === 200) {
      areaPointsList.value = response.data || []
      viewPointsDialogVisible.value = true
    } else {
      ElMessage.error('获取讲解点列表失败')
    }
  } catch (error) {
    console.error('获取讲解点列表失败:', error)
    ElMessage.error('获取讲解点列表失败')
  } finally {
    pointsLoading.value = false
  }
}

// 查看讲解点的音频
const handleViewPointAudios = async (pointId) => {
  try {
    audiosLoading.value = true
    const point = areaPointsList.value.find(p => p.pointId === pointId)
    selectedPointName.value = point ? point.pointName : `讲解点${pointId}`

    const response = await request({
      url: `/relations/point-audio/point/${pointId}`,
      method: 'get'
    })

    if (response.code === 200) {
      pointAudiosList.value = response.data || []
      viewAudiosDialogVisible.value = true
    } else {
      ElMessage.error('获取音频列表失败')
    }
  } catch (error) {
    console.error('获取音频列表失败:', error)
    ElMessage.error('获取音频列表失败')
  } finally {
    audiosLoading.value = false
  }
}

// 播放音频
const playAudio = (audioUrl) => {
  if (audioPlayer.value) {
    audioPlayer.value.src = audioUrl
    audioPlayer.value.play()
  }
}

// 初始化
onMounted(() => {
  fetchProductList()
  fetchAreaList()
})
</script>

<style lang="scss" scoped>
.relation-container {
  .operation-bar {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .count-badge {
        background: #f0f2f5;
        color: #606266;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }

  .dialog-header {
    margin-bottom: 15px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }
  }
}
</style>
