.page {
  background-color: rgba(239,234,228,1.000000);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #ff4444;
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.retry-btn {
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 讲解包内容 */
.bundle-content {
  flex: 1;
  width: 100%;
}

.bundle-long-image {
  width: 100%;
  display: block;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  background-color: #f5f5f5;
  color: #999;
  font-size: 28rpx;
}

/* 底部固定悬浮购买按钮 */
.fixed-buy-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.price-info {
  display: flex;
  align-items: baseline;
  color: white;
}

.discount-label {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.price-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.price-unit {
  font-size: 24rpx;
  color: white;
  margin-left: 4rpx;
}

.buy-button {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  min-width: 200rpx;
  text-align: center;
}

.buy-button-active {
  background-color: rgba(0, 0, 0, 0.8);
}

.buy-button-disabled {
  background-color: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.6);
}
