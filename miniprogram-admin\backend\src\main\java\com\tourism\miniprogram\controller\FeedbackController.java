package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Feedback;
import com.tourism.miniprogram.service.FeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 意见反馈控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/feedback")
@Api(tags = "意见反馈管理")
public class FeedbackController {

    @Autowired
    private FeedbackService feedbackService;

    /**
     * 分页获取反馈列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取反馈列表", notes = "分页获取反馈列表，支持按姓名、电话、状态筛选")
    public Result<IPage<Feedback>> getFeedbackPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "姓名") @RequestParam(required = false) String name,
            @ApiParam(value = "电话") @RequestParam(required = false) String phone,
            @ApiParam(value = "状态") @RequestParam(required = false) String status,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId) {
        try {
            Page<Feedback> page = new Page<>(current, size);
            QueryWrapper<Feedback> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (StringUtils.hasText(phone)) {
                queryWrapper.like("phone", phone);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            queryWrapper.orderByDesc("id");

            IPage<Feedback> feedbackPage = feedbackService.page(page, queryWrapper);
            return Result.success(feedbackPage);
        } catch (Exception e) {
            log.error("分页获取反馈列表失败", e);
            return Result.error("获取反馈列表失败");
        }
    }

    /**
     * 获取反馈列表
     */
    @GetMapping
    @ApiOperation(value = "获取反馈列表", notes = "获取所有反馈列表")
    public Result<List<Feedback>> getFeedbacks() {
        try {
            QueryWrapper<Feedback> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<Feedback> feedbacks = feedbackService.list(queryWrapper);
            return Result.success(feedbacks);
        } catch (Exception e) {
            log.error("获取反馈列表失败", e);
            return Result.error("获取反馈列表失败");
        }
    }

    /**
     * 获取反馈详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取反馈详情", notes = "根据ID获取反馈详情")
    public Result<Feedback> getFeedbackById(@ApiParam(value = "反馈ID", required = true) @PathVariable Integer id) {
        try {
            Feedback feedback = feedbackService.getById(id);
            if (feedback == null) {
                return Result.error(404, "反馈不存在");
            }
            return Result.success(feedback);
        } catch (Exception e) {
            log.error("获取反馈详情失败，id: {}", id, e);
            return Result.error("获取反馈详情失败");
        }
    }

    /**
     * 创建反馈
     */
    @PostMapping
    @ApiOperation(value = "创建反馈", notes = "创建新的反馈")
    public Result<Feedback> createFeedback(@RequestBody @Valid Feedback feedback) {
        try {
            // 设置默认状态
            if (!StringUtils.hasText(feedback.getStatus())) {
                feedback.setStatus("pending");
            }
            
            boolean success = feedbackService.save(feedback);
            if (success) {
                Feedback createdFeedback = feedbackService.getById(feedback.getId());
                return Result.success(createdFeedback);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建反馈失败", e);
            return Result.error("创建反馈失败");
        }
    }

    /**
     * 更新反馈
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新反馈", notes = "根据ID更新反馈信息")
    public Result<String> updateFeedback(
            @ApiParam(value = "反馈ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Feedback feedback) {
        try {
            feedback.setId(id);
            boolean success = feedbackService.updateById(feedback);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新反馈失败，id: {}", id, e);
            return Result.error("更新反馈失败");
        }
    }

    /**
     * 删除反馈
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除反馈", notes = "根据ID删除反馈")
    public Result<String> deleteFeedback(@ApiParam(value = "反馈ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = feedbackService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除反馈失败，id: {}", id, e);
            return Result.error("删除反馈失败");
        }
    }

    /**
     * 回复反馈
     */
    @PostMapping("/{id}/reply")
    @ApiOperation(value = "回复反馈", notes = "回复指定的反馈")
    public Result<String> replyFeedback(
            @ApiParam(value = "反馈ID", required = true) @PathVariable Integer id,
            @RequestBody Map<String, Object> request) {
        try {
            String reply = (String) request.get("reply");

            if (!StringUtils.hasText(reply)) {
                return Result.error("回复内容不能为空");
            }

            boolean success = feedbackService.replyFeedback(id, reply);
            if (success) {
                return Result.success("回复成功");
            } else {
                return Result.error("回复失败");
            }
        } catch (Exception e) {
            log.error("回复反馈失败，id: {}", id, e);
            return Result.error("回复反馈失败");
        }
    }

    /**
     * 更新反馈状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation(value = "更新反馈状态", notes = "更新指定反馈的状态")
    public Result<String> updateFeedbackStatus(
            @ApiParam(value = "反馈ID", required = true) @PathVariable Integer id,
            @RequestBody Map<String, String> request) {
        try {
            String status = request.get("status");
            if (!StringUtils.hasText(status)) {
                return Result.error("状态不能为空");
            }
            
            boolean success = feedbackService.updateFeedbackStatus(id, status);
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新反馈状态失败，id: {}", id, e);
            return Result.error("更新反馈状态失败");
        }
    }

    /**
     * 搜索反馈
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索反馈", notes = "根据关键词搜索反馈")
    public Result<List<Feedback>> searchFeedbacks(
            @ApiParam(value = "搜索关键词", required = true) @RequestParam String keyword) {
        try {
            List<Feedback> feedbacks = feedbackService.searchFeedbacks(keyword);
            return Result.success(feedbacks);
        } catch (Exception e) {
            log.error("搜索反馈失败，keyword: {}", keyword, e);
            return Result.error("搜索反馈失败");
        }
    }

    /**
     * 获取待处理反馈数量
     */
    @GetMapping("/pending-count")
    @ApiOperation(value = "获取待处理反馈数量", notes = "获取状态为待处理的反馈数量")
    public Result<Integer> getPendingFeedbackCount() {
        try {
            Integer count = feedbackService.getPendingFeedbackCount();
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取待处理反馈数量失败", e);
            return Result.error("获取待处理反馈数量失败");
        }
    }

    /**
     * 批量更新反馈状态
     */
    @PutMapping("/batch-status")
    @ApiOperation(value = "批量更新反馈状态", notes = "批量更新多个反馈的状态")
    public Result<String> batchUpdateFeedbackStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> feedbackIds = (List<Integer>) request.get("feedbackIds");
            String status = (String) request.get("status");
            
            if (feedbackIds == null || feedbackIds.isEmpty()) {
                return Result.error("反馈ID列表不能为空");
            }
            if (!StringUtils.hasText(status)) {
                return Result.error("状态不能为空");
            }
            
            boolean success = feedbackService.batchUpdateFeedbackStatus(feedbackIds, status);
            if (success) {
                return Result.success("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新反馈状态失败", e);
            return Result.error("批量更新反馈状态失败");
        }
    }
}
