package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.dto.PointAudioRelationDTO;
import com.tourism.miniprogram.entity.PointAudioRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 讲解点音频关系Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Mapper
public interface PointAudioRelationMapper extends BaseMapper<PointAudioRelation> {

    /**
     * 根据讲解点ID获取音频关系列表
     *
     * @param pointId 讲解点ID
     * @return 音频关系列表
     */
    List<PointAudioRelation> selectByPointId(@Param("pointId") Integer pointId);

    /**
     * 根据讲解点ID获取音频关系详细列表
     *
     * @param pointId 讲解点ID
     * @return 音频关系详细列表
     */
    List<PointAudioRelationDTO> selectByPointIdWithDetails(@Param("pointId") Integer pointId);

    /**
     * 根据音频ID获取讲解点关系列表
     *
     * @param audioId 音频ID
     * @return 讲解点关系列表
     */
    List<PointAudioRelation> selectByAudioId(@Param("audioId") Integer audioId);

    /**
     * 批量插入关系
     *
     * @param relations 关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<PointAudioRelation> relations);

    /**
     * 根据讲解点ID删除所有关系
     *
     * @param pointId 讲解点ID
     * @return 删除数量
     */
    int deleteByPointId(@Param("pointId") Integer pointId);

    /**
     * 根据音频ID删除所有关系
     *
     * @param audioId 音频ID
     * @return 删除数量
     */
    int deleteByAudioId(@Param("audioId") Integer audioId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 更新数量
     */
    int updateSortOrder(@Param("relationId") Integer relationId, @Param("sortOrder") Integer sortOrder);
}
