<template>
  <div class="user-edit">
    <el-page-header @back="goBack" content="编辑用户" />
    
    <el-card v-loading="loading" class="edit-card">
      <template #header>
        <div class="card-header">
          <span>编辑用户信息</span>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="edit-form"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="用户ID">
              <el-input v-model="form.id" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12">
            <el-form-item label="微信OpenID">
              <el-input v-model="form.openid" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12">
            <el-form-item label="用户昵称" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入用户昵称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12">
            <el-form-item label="用户地区" prop="province">
              <el-input
                v-model="form.province"
                placeholder="请输入用户地区"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号码"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12">
            <el-form-item label="注册时间">
              <el-input :value="formatDate(form.createdAt)" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="头像地址" prop="avatarUrl">
              <el-input
                v-model="form.avatarUrl"
                placeholder="请输入头像URL地址"
                type="url"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="头像预览">
              <div class="avatar-preview">
                <el-avatar
                  :size="80"
                  :src="form.avatarUrl"
                  :alt="form.nickname"
                >
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="avatar-tips">
                  <p>头像预览</p>
                  <p class="tips-text">请确保头像URL地址有效</p>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item class="form-actions">
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            保存修改
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserInfo, updateUser } from '@/api/user'
import { formatDate } from '@/utils'
import { validPhone, validURL } from '@/utils/validate'

const router = useRouter()

const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive({
  id: '',
  openid: '',
  nickname: '',
  province: '',
  phone: '',
  avatarUrl: '',
  createdAt: ''
})

// 原始数据备份
let originalData = {}

// 表单验证规则
const rules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  province: [
    { max: 50, message: '地区长度不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value && !validPhone(value)) {
          callback(new Error('请输入正确的手机号码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  avatarUrl: [
    {
      validator: (rule, value, callback) => {
        if (value && !validURL(value)) {
          callback(new Error('请输入正确的URL地址'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true
  try {
    const response = await getUserInfo(props.id)
    if (response.code === 200) {
      const userData = response.data
      Object.keys(form).forEach(key => {
        form[key] = userData[key] || ''
      })
      // 备份原始数据
      originalData = { ...userData }
    } else {
      ElMessage.error('用户不存在')
      goBack()
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 只提交有变化的字段
    const updateData = {}
    Object.keys(form).forEach(key => {
      if (key !== 'id' && key !== 'openid' && key !== 'createdAt') {
        if (form[key] !== originalData[key]) {
          updateData[key] = form[key]
        }
      }
    })
    
    if (Object.keys(updateData).length === 0) {
      ElMessage.info('没有需要更新的数据')
      return
    }
    
    const response = await updateUser(props.id, updateData)
    if (response.code === 200) {
      ElMessage.success('用户信息更新成功')
      goBack()
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.keys(form).forEach(key => {
    form[key] = originalData[key] || ''
  })
  formRef.value?.clearValidate()
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.user-edit {
  .edit-card {
    margin-top: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .edit-form {
    max-width: 800px;
    
    .avatar-preview {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .avatar-tips {
        .tips-text {
          font-size: 12px;
          color: #909399;
          margin: 5px 0 0;
        }
      }
    }
    
    .form-actions {
      margin-top: 30px;
      text-align: center;
      
      .el-button {
        margin: 0 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .user-edit {
    .edit-form {
      .avatar-preview {
        flex-direction: column;
        text-align: center;
        gap: 10px;
      }
      
      .form-actions {
        .el-button {
          margin: 5px;
          width: 120px;
        }
      }
    }
  }
}
</style>
