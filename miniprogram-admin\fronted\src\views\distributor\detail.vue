<template>
  <div class="distributor-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>分销员详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <div>
              <el-button type="primary" size="small" @click="handleEdit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                v-if="distributorInfo.status === 'active'"
                type="warning" 
                size="small" 
                @click="handleFreeze"
              >
                <el-icon><Lock /></el-icon>
                冻结
              </el-button>
              <el-button 
                v-if="distributorInfo.status === 'frozen'"
                type="success" 
                size="small" 
                @click="handleUnfreeze"
              >
                <el-icon><Unlock /></el-icon>
                解冻
              </el-button>
            </div>
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <label>分销员ID：</label>
            <span>{{ distributorInfo.id }}</span>
          </div>
          <div class="info-item">
            <label>分销员编号：</label>
            <el-tag type="info">{{ distributorInfo.distributorCode }}</el-tag>
          </div>
          <div class="info-item">
            <label>绑定用户ID：</label>
            <span>{{ distributorInfo.userId }}</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="getStatusType(distributorInfo.status)">
              {{ getStatusText(distributorInfo.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>佣金比例：</label>
            <el-tag :type="distributorInfo.customRate ? 'success' : 'info'">
              {{ distributorInfo.commissionRate }}%
              {{ distributorInfo.customRate ? '(自定义)' : '(默认)' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>今日激活人数：</label>
            <el-tag type="warning">{{ distributorInfo.todayActivated || 0 }}</el-tag>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(distributorInfo.createdAt) }}</span>
          </div>
          <div class="info-item">
            <label>更新时间：</label>
            <span>{{ formatDate(distributorInfo.updatedAt) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 佣金信息 -->
      <el-card class="commission-card">
        <template #header>
          <span>佣金信息</span>
        </template>

        <div class="commission-stats">
          <div class="stat-item total">
            <div class="stat-value">¥{{ formatMoney(distributorInfo.totalCommission) }}</div>
            <div class="stat-label">累积佣金总额</div>
          </div>
          <div class="stat-item available">
            <div class="stat-value">¥{{ formatMoney(distributorInfo.availableCommission) }}</div>
            <div class="stat-label">可提现金额</div>
          </div>
          <div class="stat-item withdrawn">
            <div class="stat-value">¥{{ formatMoney(distributorInfo.withdrawnCommission) }}</div>
            <div class="stat-label">已提现金额</div>
          </div>
          <div class="stat-item unsettled">
            <div class="stat-value">¥{{ formatMoney(distributorInfo.unsettledCommission) }}</div>
            <div class="stat-label">未结算金额</div>
          </div>
        </div>
      </el-card>

      <!-- 专属二维码 -->
      <el-card class="qrcode-card">
        <template #header>
          <div class="card-header">
            <span>专属二维码</span>
            <el-button type="primary" size="small" @click="handleGenerateQrCode">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>
          </div>
        </template>

        <div class="qrcode-content">
          <div v-if="distributorInfo.qrcodeUrl" class="qrcode-display">
            <img :src="distributorInfo.qrcodeUrl" alt="专属二维码" class="qrcode-image" />
            <div class="qrcode-actions">
              <el-button type="primary" @click="handleCopyQrCode">
                <el-icon><CopyDocument /></el-icon>
                复制链接
              </el-button>
              <el-button type="success" @click="handleDownloadQrCode">
                <el-icon><Download /></el-icon>
                下载二维码
              </el-button>
            </div>
          </div>
          <div v-else class="no-qrcode">
            <el-empty description="暂无二维码" />
            <el-button type="primary" @click="handleGenerateQrCode">
              生成二维码
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="records-card">
        <template #header>
          <div class="card-header">
            <span>相关记录</span>
            <div>
              <el-button type="primary" size="small" @click="viewWithdrawalRecords">
                <el-icon><Money /></el-icon>
                提现记录
              </el-button>
              <el-button type="success" size="small" @click="viewDistributionOrders">
                <el-icon><Document /></el-icon>
                分销订单
              </el-button>
            </div>
          </div>
        </template>

        <div class="records-summary">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="总订单数">
              <el-tag type="info">{{ orderStatistics.orderCount || 0 }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="待结算佣金">
              <span class="money-text pending">¥{{ formatMoney(orderStatistics.pendingCommissionAmount) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="已结算佣金">
              <span class="money-text settled">¥{{ formatMoney(orderStatistics.settledCommissionAmount) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="待处理提现">
              <span class="money-text pending">¥{{ formatMoney(withdrawalStatistics.pendingWithdrawalAmount) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getDistributorDetail,
  freezeDistributor,
  unfreezeDistributor,
  generateQrCode
} from '@/api/distributor'
import { getCommissionStatistics, getWithdrawalStatistics } from '@/api/distributor'
import { formatDate, formatMoney } from '@/utils'

const router = useRouter()
const route = useRoute()

const distributorId = route.params.id
const loading = ref(false)
const distributorInfo = ref({})
const orderStatistics = ref({})
const withdrawalStatistics = ref({})

// 获取分销员详情
const fetchDistributorDetail = async () => {
  loading.value = true
  try {
    const response = await getDistributorDetail(distributorId)
    if (response.code === 200) {
      distributorInfo.value = response.data
    }
  } catch (error) {
    console.error('获取分销员详情失败:', error)
    ElMessage.error('获取分销员详情失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    // 获取订单统计
    const orderResponse = await getCommissionStatistics(distributorId)
    if (orderResponse.code === 200) {
      orderStatistics.value = orderResponse.data
    }

    // 获取提现统计
    const withdrawalResponse = await getWithdrawalStatistics(distributorId)
    if (withdrawalResponse.code === 200) {
      withdrawalStatistics.value = withdrawalResponse.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 状态相关方法
const getStatusType = (status) => {
  const types = {
    active: 'success',
    inactive: 'warning',
    frozen: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    inactive: '非活跃',
    frozen: '冻结'
  }
  return texts[status] || '未知'
}

// 返回列表
const goBack = () => {
  router.push('/distributor/list')
}

// 编辑
const handleEdit = () => {
  router.push(`/distributor/edit/${distributorId}`)
}

// 冻结账户
const handleFreeze = async () => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入冻结原因',
      '冻结分销员账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入冻结原因'
      }
    )
    
    const response = await freezeDistributor(distributorId, reason)
    if (response.code === 200) {
      ElMessage.success('账户冻结成功')
      fetchDistributorDetail()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('冻结账户失败:', error)
      ElMessage.error('冻结账户失败')
    }
  }
}

// 解冻账户
const handleUnfreeze = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要解冻该分销员账户吗？',
      '解冻账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await unfreezeDistributor(distributorId)
    if (response.code === 200) {
      ElMessage.success('账户解冻成功')
      fetchDistributorDetail()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('解冻账户失败:', error)
      ElMessage.error('解冻账户失败')
    }
  }
}

// 生成二维码
const handleGenerateQrCode = async () => {
  try {
    const response = await generateQrCode(distributorId)
    if (response.code === 200) {
      ElMessage.success('二维码生成成功')
      fetchDistributorDetail()
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

// 复制二维码链接
const handleCopyQrCode = async () => {
  try {
    await navigator.clipboard.writeText(distributorInfo.value.qrcodeUrl)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 下载二维码
const handleDownloadQrCode = () => {
  const link = document.createElement('a')
  link.href = distributorInfo.value.qrcodeUrl
  link.download = `distributor-${distributorInfo.value.distributorCode}-qrcode.png`
  link.click()
}

// 查看提现记录
const viewWithdrawalRecords = () => {
  router.push(`/distributor/withdrawal/${distributorId}`)
}

// 查看分销订单
const viewDistributionOrders = () => {
  router.push(`/distributor/orders/${distributorId}`)
}

onMounted(() => {
  fetchDistributorDetail()
  fetchStatistics()
})
</script>

<style lang="scss" scoped>
.distributor-detail {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .detail-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .info-card {
      margin-bottom: 20px;
      
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        
        .info-item {
          display: flex;
          align-items: center;
          
          label {
            font-weight: 500;
            color: #606266;
            margin-right: 10px;
            min-width: 120px;
          }
        }
      }
    }
    
    .commission-card {
      margin-bottom: 20px;
      
      .commission-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        
        .stat-item {
          text-align: center;
          padding: 20px;
          border-radius: 8px;
          
          &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
          
          &.available {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }
          
          &.withdrawn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
          }
          
          &.unsettled {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
          }
          
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }
    }
    
    .qrcode-card {
      margin-bottom: 20px;
      
      .qrcode-content {
        text-align: center;
        
        .qrcode-display {
          .qrcode-image {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            margin-bottom: 20px;
          }
          
          .qrcode-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
          }
        }
        
        .no-qrcode {
          padding: 40px;
        }
      }
    }
    
    .records-card {
      .records-summary {
        .money-text {
          font-weight: 500;
          
          &.pending {
            color: #E6A23C;
          }
          
          &.settled {
            color: #67C23A;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .distributor-detail {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .info-grid {
      grid-template-columns: 1fr !important;
      
      .info-item {
        flex-direction: column;
        align-items: flex-start;
        
        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }
    
    .commission-stats {
      grid-template-columns: 1fr !important;
    }
    
    .qrcode-actions {
      flex-direction: column;
      align-items: center;
    }
  }
}
</style>
