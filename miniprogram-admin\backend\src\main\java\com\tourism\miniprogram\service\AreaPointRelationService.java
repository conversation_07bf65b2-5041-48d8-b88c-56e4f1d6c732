package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.dto.AreaPointRelationDTO;
import com.tourism.miniprogram.entity.AreaPointRelation;

import java.util.List;

/**
 * 区域讲解点关系服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
public interface AreaPointRelationService extends IService<AreaPointRelation> {

    /**
     * 根据区域ID获取讲解点关系列表
     *
     * @param areaId 区域ID
     * @return 讲解点关系列表
     */
    List<AreaPointRelation> getByAreaId(Integer areaId);

    /**
     * 根据区域ID获取讲解点关系详细列表
     *
     * @param areaId 区域ID
     * @return 讲解点关系详细列表
     */
    List<AreaPointRelationDTO> getByAreaIdWithDetails(Integer areaId);

    /**
     * 根据讲解点ID获取区域关系列表
     *
     * @param pointId 讲解点ID
     * @return 区域关系列表
     */
    List<AreaPointRelation> getByPointId(Integer pointId);

    /**
     * 添加区域讲解点关系
     *
     * @param areaId 区域ID
     * @param pointId 讲解点ID
     * @param sortOrder 排序
     * @return 是否成功
     */
    boolean addRelation(Integer areaId, Integer pointId, Integer sortOrder);

    /**
     * 删除区域讲解点关系
     *
     * @param areaId 区域ID
     * @param pointId 讲解点ID
     * @return 是否成功
     */
    boolean removeRelation(Integer areaId, Integer pointId);

    /**
     * 批量添加关系
     *
     * @param relations 关系列表
     * @return 是否成功
     */
    boolean batchAddRelations(List<AreaPointRelation> relations);

    /**
     * 根据区域ID删除所有关系
     *
     * @param areaId 区域ID
     * @return 是否成功
     */
    boolean removeByAreaId(Integer areaId);

    /**
     * 根据讲解点ID删除所有关系
     *
     * @param pointId 讲解点ID
     * @return 是否成功
     */
    boolean removeByPointId(Integer pointId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 是否成功
     */
    boolean updateSortOrder(Integer relationId, Integer sortOrder);

    /**
     * 批量更新排序
     *
     * @param relationIds 关系ID列表
     * @param sortOrders 排序值列表
     * @return 是否成功
     */
    boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders);
}
