<template>
  <div class="carousel-detail">
    <div class="page-header">
      <h2>轮播图详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="轮播图ID">
          {{ detail.id }}
        </el-descriptions-item>
        <el-descriptions-item label="标题">
          {{ detail.title }}
        </el-descriptions-item>
        <el-descriptions-item label="轮播图片" :span="2">
          <el-image
            v-if="detail.image"
            :src="detail.image"
            style="width: 300px; height: 100px"
            fit="cover"
            :preview-src-list="[detail.image]"
          />
        </el-descriptions-item>
        <el-descriptions-item label="所属省份">
          {{ detail.provinceName || '全国' }}
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          <el-tag v-if="detail.type === 'home'" type="primary">首页轮播</el-tag>
          <el-tag v-else-if="detail.type === 'activity'" type="success">活动轮播</el-tag>
          <el-tag v-else-if="detail.type === 'recommend'" type="warning">推荐轮播</el-tag>
          <el-tag v-else>{{ detail.type }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
        <el-descriptions-item label="组合包ID">
          {{ detail.productBundlesId || '无' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCarouselInfo } from '@/api/carousel'
import { getProvinceList } from '@/api/province'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const provinceList = ref([])

const detail = reactive({
  id: '',
  title: '',
  image: '',
  provinceId: '',
  provinceName: '',
  type: '',
  sort: 0,
  status: 1,
  createdAt: '',
  updatedAt: '',
  productBundlesId: ''
})

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getCarouselInfo(route.params.id)
    Object.assign(detail, data)
    
    // 设置省份名称
    const province = provinceList.value.find(p => p.id === data.provinceId)
    detail.provinceName = province ? province.name : '全国'
  } catch (error) {
    ElMessage.error('获取轮播图详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/carousel/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/carousel/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  fetchDetail()
})
</script>

<style scoped>
.carousel-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
