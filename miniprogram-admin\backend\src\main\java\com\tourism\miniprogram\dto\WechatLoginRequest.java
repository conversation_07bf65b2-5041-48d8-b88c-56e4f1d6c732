package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@ApiModel(value = "WechatLoginRequest", description = "微信登录请求")
public class WechatLoginRequest {

    @ApiModelProperty(value = "微信登录code", required = true)
    @NotBlank(message = "登录code不能为空")
    private String code;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "用户地区")
    private String region;

    @ApiModelProperty(value = "手机号码")
    private String phone;
}
