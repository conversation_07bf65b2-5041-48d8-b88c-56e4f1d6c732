<template>
  <div class="order-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>订单详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 订单基本信息 -->
      <el-card class="order-info-card">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <div>
              <el-tag :type="getOrderStatusType(orderInfo.status)" size="large">
                {{ getOrderStatusText(orderInfo.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="order-info-grid">
          <div class="info-section">
            <h4>基本信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>订单ID：</label>
                <span>{{ orderInfo.id }}</span>
              </div>
              <div class="info-item">
                <label>订单编号：</label>
                <span>{{ orderInfo.orderNumber }}</span>
              </div>
              <div class="info-item">
                <label>用户ID：</label>
                <span>{{ orderInfo.userId }}</span>
              </div>
              <div class="info-item">
                <label>订单金额：</label>
                <span class="money-text">¥{{ formatMoney(orderInfo.totalAmount) }}</span>
              </div>
              <div class="info-item">
                <label>支付金额：</label>
                <span class="money-text">¥{{ formatMoney(orderInfo.payAmount) }}</span>
              </div>
              <div class="info-item">
                <label>支付方式：</label>
                <span>{{ orderInfo.paymentMethod }}</span>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h4>时间信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDate(orderInfo.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>支付时间：</label>
                <span>{{ orderInfo.paidAt ? formatDate(orderInfo.paidAt) : '-' }}</span>
              </div>
              <div class="info-item">
                <label>完成时间：</label>
                <span>{{ orderInfo.completedAt ? formatDate(orderInfo.completedAt) : '-' }}</span>
              </div>
              <div class="info-item">
                <label>取消时间：</label>
                <span>{{ orderInfo.cancelledAt ? formatDate(orderInfo.cancelledAt) : '-' }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 订单商品信息 -->
      <el-card class="products-card">
        <template #header>
          <div class="card-header">
            <span>商品信息</span>
            <div>
              <el-button type="primary" size="small" @click="handleAddProduct">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
              <el-button type="success" size="small" @click="handleManageProducts">
                <el-icon><Setting /></el-icon>
                管理商品
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="orderProducts"
          style="width: 100%"
          stripe
          border
        >
          <el-table-column prop="productId" label="商品ID" width="100" align="center" />

          <el-table-column prop="productName" label="商品名称" min-width="200">
            <template #default="scope">
              <div class="product-info">
                <img v-if="scope.row.productImage" :src="scope.row.productImage" class="product-image" />
                <div>
                  <div class="product-name">{{ scope.row.productName }}</div>
                  <div class="product-desc">{{ scope.row.productDescription }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="productType" label="商品类型" width="120" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.productType === 'single' ? 'primary' : 'success'" size="small">
                {{ scope.row.productType === 'single' ? '单品' : '组合包' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="quantity" label="数量" width="80" align="center" />

          <el-table-column prop="unitPrice" label="单价" width="120" align="right">
            <template #default="scope">
              <span class="money-text">¥{{ formatMoney(scope.row.unitPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="totalPrice" label="小计" width="120" align="right">
            <template #default="scope">
              <span class="money-text">¥{{ formatMoney(scope.row.totalPrice) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleViewProduct(scope.row)"
              >
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleRemoveProduct(scope.row)"
              >
                <el-icon><Delete /></el-icon>
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 分销信息 -->
      <el-card v-if="distributionInfo" class="distribution-card">
        <template #header>
          <span>分销信息</span>
        </template>

        <div class="distribution-info">
          <div class="info-item">
            <label>分销员ID：</label>
            <span>{{ distributionInfo.distributorId }}</span>
          </div>
          <div class="info-item">
            <label>分销员编号：</label>
            <span>{{ distributionInfo.distributorCode }}</span>
          </div>
          <div class="info-item">
            <label>佣金比例：</label>
            <span>{{ distributionInfo.commissionRate }}%</span>
          </div>
          <div class="info-item">
            <label>佣金金额：</label>
            <span class="money-text commission">¥{{ formatMoney(distributionInfo.commissionAmount) }}</span>
          </div>
          <div class="info-item">
            <label>结算状态：</label>
            <el-tag :type="getDistributionStatusType(distributionInfo.status)" size="small">
              {{ getDistributionStatusText(distributionInfo.status) }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="operations-card">
        <template #header>
          <span>操作记录</span>
        </template>

        <el-timeline>
          <el-timeline-item
            v-for="(operation, index) in operationLogs"
            :key="index"
            :timestamp="formatDate(operation.createdAt)"
            placement="top"
          >
            <div class="operation-content">
              <div class="operation-title">{{ operation.operation }}</div>
              <div class="operation-desc">{{ operation.description }}</div>
              <div class="operation-user">操作人：{{ operation.operatorName }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <!-- 产品管理对话框 -->
    <el-dialog
      v-model="productManageDialogVisible"
      title="商品管理"
      width="80%"
      :before-close="handleProductManageDialogClose"
    >
      <div class="product-manage-content">
        <!-- 产品搜索 -->
        <div class="search-section">
          <el-form :model="productSearchForm" :inline="true">
            <el-form-item label="商品名称">
              <el-input
                v-model="productSearchForm.name"
                placeholder="请输入商品名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="商品类型">
              <el-select
                v-model="productSearchForm.type"
                placeholder="请选择类型"
                clearable
                style="width: 150px"
              >
                <el-option label="单品" value="single" />
                <el-option label="组合包" value="bundle" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleProductSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleProductSearchReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 产品列表 -->
        <el-table
          v-loading="productLoading"
          :data="productList"
          style="width: 100%"
          stripe
          border
          @selection-change="handleProductSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="name" label="商品名称" min-width="200">
            <template #default="scope">
              <div class="product-info">
                <img v-if="scope.row.image" :src="scope.row.image" class="product-image" />
                <div>
                  <div class="product-name">{{ scope.row.name }}</div>
                  <div class="product-desc">{{ scope.row.description }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="type" label="类型" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'single' ? 'primary' : 'success'" size="small">
                {{ scope.row.type === 'single' ? '单品' : '组合包' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="price" label="价格" width="120" align="right">
            <template #default="scope">
              <span class="money-text">¥{{ formatMoney(scope.row.price) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 'active' ? '上架' : '下架' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleAddProductToOrder(scope.row)"
              >
                <el-icon><Plus /></el-icon>
                添加到订单
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleEditProduct(scope.row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="productPagination.current"
            v-model:page-size="productPagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="productPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleProductSizeChange"
            @current-change="handleProductCurrentChange"
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productManageDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="handleBatchAddProducts"
            :disabled="selectedProducts.length === 0"
          >
            批量添加选中商品
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate, formatMoney } from '@/utils'

const router = useRouter()
const route = useRoute()

const orderId = route.params.id
const loading = ref(false)

// 订单信息
const orderInfo = ref({})
const orderProducts = ref([])
const distributionInfo = ref(null)
const operationLogs = ref([])

// 产品管理对话框
const productManageDialogVisible = ref(false)
const productLoading = ref(false)
const productList = ref([])
const selectedProducts = ref([])

// 产品搜索表单
const productSearchForm = reactive({
  name: '',
  type: ''
})

// 产品分页
const productPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const response = {
      code: 200,
      data: {
        id: orderId,
        orderNumber: 'ORD' + Date.now(),
        userId: 1001,
        totalAmount: 299.00,
        payAmount: 299.00,
        paymentMethod: '微信支付',
        status: 'completed',
        createdAt: new Date().toISOString(),
        paidAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        cancelledAt: null
      }
    }

    if (response.code === 200) {
      orderInfo.value = response.data
      await fetchOrderProducts()
      await fetchDistributionInfo()
      await fetchOperationLogs()
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取订单商品
const fetchOrderProducts = async () => {
  try {
    // 模拟API调用
    const response = {
      code: 200,
      data: [
        {
          productId: 1,
          productName: '黄山风景区门票',
          productDescription: '成人票，含索道',
          productImage: 'https://example.com/image1.jpg',
          productType: 'single',
          quantity: 2,
          unitPrice: 149.50,
          totalPrice: 299.00
        }
      ]
    }

    if (response.code === 200) {
      orderProducts.value = response.data
    }
  } catch (error) {
    console.error('获取订单商品失败:', error)
  }
}

// 获取分销信息
const fetchDistributionInfo = async () => {
  try {
    // 模拟API调用 - 检查是否有分销记录
    const response = {
      code: 200,
      data: {
        distributorId: 1,
        distributorCode: 'DIST12345678',
        commissionRate: 10.00,
        commissionAmount: 29.90,
        status: 'settled'
      }
    }

    if (response.code === 200) {
      distributionInfo.value = response.data
    }
  } catch (error) {
    console.error('获取分销信息失败:', error)
  }
}

// 获取操作记录
const fetchOperationLogs = async () => {
  try {
    // 模拟API调用
    const response = {
      code: 200,
      data: [
        {
          operation: '订单创建',
          description: '用户创建订单',
          operatorName: '系统',
          createdAt: new Date().toISOString()
        },
        {
          operation: '订单支付',
          description: '用户完成支付',
          operatorName: '系统',
          createdAt: new Date().toISOString()
        }
      ]
    }

    if (response.code === 200) {
      operationLogs.value = response.data
    }
  } catch (error) {
    console.error('获取操作记录失败:', error)
  }
}

// 获取产品列表
const fetchProductList = async () => {
  productLoading.value = true
  try {
    // 模拟API调用
    const response = {
      code: 200,
      data: {
        records: [
          {
            id: 1,
            name: '黄山风景区门票',
            description: '成人票，含索道',
            image: 'https://example.com/image1.jpg',
            type: 'single',
            price: 149.50,
            status: 'active'
          },
          {
            id: 2,
            name: '黄山二日游套餐',
            description: '门票+住宿+餐饮',
            image: 'https://example.com/image2.jpg',
            type: 'bundle',
            price: 599.00,
            status: 'active'
          }
        ],
        total: 2
      }
    }

    if (response.code === 200) {
      productList.value = response.data.records || []
      productPagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    productLoading.value = false
  }
}

// 状态相关方法
const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getDistributionStatusType = (status) => {
  const types = {
    pending: 'warning',
    settled: 'success',
    canceled: 'danger'
  }
  return types[status] || 'info'
}

const getDistributionStatusText = (status) => {
  const texts = {
    pending: '待结算',
    settled: '已结算',
    canceled: '已取消'
  }
  return texts[status] || '未知'
}

// 返回列表
const goBack = () => {
  router.push('/order')
}

// 商品操作
const handleAddProduct = () => {
  productManageDialogVisible.value = true
  fetchProductList()
}

const handleManageProducts = () => {
  productManageDialogVisible.value = true
  fetchProductList()
}

const handleViewProduct = (product) => {
  // 查看商品详情
  console.log('查看商品:', product)
}

const handleRemoveProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要从订单中移除商品"${product.productName}"吗？`,
      '移除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里调用API移除商品
    ElMessage.success('商品移除成功')
    fetchOrderProducts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除商品失败:', error)
      ElMessage.error('移除商品失败')
    }
  }
}

// 产品管理对话框
const handleProductManageDialogClose = () => {
  productSearchForm.name = ''
  productSearchForm.type = ''
  selectedProducts.value = []
}

const handleProductSearch = () => {
  productPagination.current = 1
  fetchProductList()
}

const handleProductSearchReset = () => {
  productSearchForm.name = ''
  productSearchForm.type = ''
  productPagination.current = 1
  fetchProductList()
}

const handleProductSizeChange = (size) => {
  productPagination.size = size
  productPagination.current = 1
  fetchProductList()
}

const handleProductCurrentChange = (current) => {
  productPagination.current = current
  fetchProductList()
}

const handleProductSelectionChange = (selection) => {
  selectedProducts.value = selection
}

const handleAddProductToOrder = async (product) => {
  try {
    // 这里调用API添加商品到订单
    ElMessage.success(`商品"${product.name}"添加成功`)
    fetchOrderProducts()
  } catch (error) {
    console.error('添加商品失败:', error)
    ElMessage.error('添加商品失败')
  }
}

const handleEditProduct = (product) => {
  // 编辑商品 - 可以打开编辑对话框或跳转到编辑页面
  console.log('编辑商品:', product)
}

const handleBatchAddProducts = async () => {
  try {
    // 这里调用API批量添加商品
    ElMessage.success(`成功添加${selectedProducts.value.length}个商品`)
    productManageDialogVisible.value = false
    fetchOrderProducts()
  } catch (error) {
    console.error('批量添加商品失败:', error)
    ElMessage.error('批量添加商品失败')
  }
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
    }
  }

  .detail-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .order-info-card {
      margin-bottom: 20px;

      .order-info-grid {
        .info-section {
          margin-bottom: 30px;

          &:last-child {
            margin-bottom: 0;
          }

          h4 {
            margin: 0 0 15px 0;
            color: #409EFF;
            font-size: 16px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
          }

          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;

            .info-item {
              display: flex;
              align-items: center;

              label {
                font-weight: 500;
                color: #606266;
                margin-right: 10px;
                min-width: 100px;
              }

              .money-text {
                font-weight: 600;
                color: #67C23A;
              }
            }
          }
        }
      }
    }

    .products-card {
      margin-bottom: 20px;

      .product-info {
        display: flex;
        align-items: center;
        gap: 10px;

        .product-image {
          width: 50px;
          height: 50px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
        }

        .product-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .product-desc {
          font-size: 12px;
          color: #909399;
        }
      }

      .money-text {
        font-weight: 500;
        color: #67C23A;
      }
    }

    .distribution-card {
      margin-bottom: 20px;

      .distribution-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          label {
            font-weight: 500;
            color: #606266;
            margin-right: 10px;
            min-width: 100px;
          }

          .money-text {
            font-weight: 600;

            &.commission {
              color: #E6A23C;
            }
          }
        }
      }
    }

    .operations-card {
      .operation-content {
        .operation-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 5px;
        }

        .operation-desc {
          color: #606266;
          margin-bottom: 5px;
        }

        .operation-user {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .product-manage-content {
    .search-section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .el-form-item {
        margin-bottom: 0;
      }
    }

    .product-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .product-image {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
      }

      .product-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .product-desc {
        font-size: 12px;
        color: #909399;
      }
    }

    .money-text {
      font-weight: 500;
      color: #67C23A;
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

@media (max-width: 768px) {
  .order-detail {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .info-grid {
      grid-template-columns: 1fr !important;

      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }

    .distribution-info {
      grid-template-columns: 1fr !important;

      .info-item {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
          min-width: auto;
        }
      }
    }

    .pagination-container {
      text-align: center;

      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>