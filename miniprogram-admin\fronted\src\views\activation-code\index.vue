<template>
  <div class="activation-code-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>激活码管理</h2>
      <el-button type="primary" @click="handleGenerate">批量生成激活码</el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="激活码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入激活码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="未使用" value="unused" />
            <el-option label="已使用" value="used" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item label="景区ID">
          <el-input-number
            v-model="searchForm.scenicId"
            placeholder="请输入景区ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="产品ID">
          <el-input-number
            v-model="searchForm.productId"
            placeholder="请输入产品ID"
            style="width: 150px"
            :min="1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column prop="code" label="激活码" min-width="200" />
        <el-table-column prop="scenicId" label="景区ID" width="100" />
        <el-table-column prop="productId" label="产品ID" width="100" />
        <el-table-column prop="bundleId" label="组合包ID" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usedAt" label="使用时间" width="180" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量生成对话框 -->
    <el-dialog v-model="generateDialogVisible" title="批量生成激活码" width="500px">
      <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="100px">
        <el-form-item label="景区ID" prop="scenicId">
          <el-input-number
            v-model="generateForm.scenicId"
            :min="1"
            placeholder="请输入景区ID"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="产品ID" prop="productId">
          <el-input-number
            v-model="generateForm.productId"
            :min="1"
            placeholder="请输入产品ID（可选）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="组合包ID" prop="bundleId">
          <el-input-number
            v-model="generateForm.bundleId"
            :min="1"
            placeholder="请输入组合包ID（可选）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number
            v-model="generateForm.count"
            :min="1"
            :max="1000"
            placeholder="请输入生成数量"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmGenerate" :loading="generateLoading">
            生成
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getActivationCodePage, deleteActivationCode, generateActivationCodes } from '@/api/activationCode'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const generateLoading = ref(false)
const generateDialogVisible = ref(false)
const tableData = ref([])
const generateFormRef = ref()

const searchForm = reactive({
  code: '',
  status: '',
  scenicId: null,
  productId: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const generateForm = reactive({
  scenicId: null,
  productId: null,
  bundleId: null,
  count: 10
})

const generateRules = {
  scenicId: [
    { required: true, message: '请输入景区ID', trigger: 'blur' }
  ],
  count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '生成数量必须在 1 到 1000 之间', trigger: 'blur' }
  ]
}

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'unused': 'success',
    'used': 'warning',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'unused': '未使用',
    'used': '已使用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const { data } = await getActivationCodePage(params)
    tableData.value = data.records
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取激活码列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    code: '',
    status: '',
    scenicId: null,
    productId: null
  })
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 操作
const handleGenerate = () => {
  generateDialogVisible.value = true
  Object.assign(generateForm, {
    scenicId: null,
    productId: null,
    bundleId: null,
    count: 10
  })
}

const handleConfirmGenerate = async () => {
  if (!generateFormRef.value) return

  try {
    await generateFormRef.value.validate()
    generateLoading.value = true

    await generateActivationCodes(generateForm)
    ElMessage.success('激活码生成成功')
    generateDialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('生成激活码失败')
      console.error(error)
    }
  } finally {
    generateLoading.value = false
  }
}

const handleEdit = (row) => {
  router.push(`/activation-code/edit/${row.id}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除激活码"${row.code}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteActivationCode(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.activation-code-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
