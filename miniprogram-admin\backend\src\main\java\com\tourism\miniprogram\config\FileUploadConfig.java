package com.tourism.miniprogram.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 允许的图片格式
     */
    private String imageTypes = "jpg,jpeg,png,gif,bmp,webp";

    /**
     * 允许的视频格式
     */
    private String videoTypes = "mp4,avi,mov,wmv,flv,3gp,mkv";

    /**
     * 允许的音频格式
     */
    private String audioTypes = "mp3,wav,flac,aac,ogg,wma,m4a";

    /**
     * 文件大小限制（字节）
     */
    private long maxSize = 1048576000L; // 1000MB

    /**
     * 文件存储路径前缀
     */
    private String pathPrefix = "tourism/files/";

    /**
     * 获取图片类型列表
     */
    public List<String> getImageTypeList() {
        return Arrays.asList(imageTypes.toLowerCase().split(","));
    }

    /**
     * 获取视频类型列表
     */
    public List<String> getVideoTypeList() {
        return Arrays.asList(videoTypes.toLowerCase().split(","));
    }

    /**
     * 获取音频类型列表
     */
    public List<String> getAudioTypeList() {
        return Arrays.asList(audioTypes.toLowerCase().split(","));
    }

    /**
     * 检查是否为允许的图片类型
     */
    public boolean isAllowedImageType(String fileExtension) {
        return getImageTypeList().contains(fileExtension.toLowerCase());
    }

    /**
     * 检查是否为允许的视频类型
     */
    public boolean isAllowedVideoType(String fileExtension) {
        return getVideoTypeList().contains(fileExtension.toLowerCase());
    }

    /**
     * 检查是否为允许的音频类型
     */
    public boolean isAllowedAudioType(String fileExtension) {
        return getAudioTypeList().contains(fileExtension.toLowerCase());
    }

    /**
     * 检查是否为允许的文件类型
     */
    public boolean isAllowedFileType(String fileExtension) {
        return isAllowedImageType(fileExtension) || 
               isAllowedVideoType(fileExtension) || 
               isAllowedAudioType(fileExtension);
    }
}
