<template>
  <div class="guide-point-list">
    <div class="page-header">
      <h2>讲解点管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="handleBatchCreate">
          <el-icon><Plus /></el-icon>
          批量创建讲解点
        </el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="讲解点标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入讲解点标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="产品ID">
          <el-input
            v-model="searchForm.productId"
            placeholder="请输入产品ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="位置">
          <el-input
            v-model="searchForm.location"
            placeholder="请输入位置"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="pointId" label="ID" width="80" sortable="custom" />
        <el-table-column prop="productId" label="产品ID" width="100" />
        <el-table-column prop="title" label="讲解点标题" min-width="150" />
        <el-table-column prop="location" label="位置" min-width="120" />
        <el-table-column prop="duration" label="讲解时长" width="120" />
        <el-table-column prop="exampleImageUrl" label="示例图片" width="120">
          <template #default="{ row }">
            <el-image
              v-if="row.exampleImageUrl"
              :src="row.exampleImageUrl"
              style="width: 80px; height: 50px"
              fit="cover"
              :preview-src-list="[row.exampleImageUrl]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="audioUrl" label="音频" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.audioUrl" type="success">有音频</el-tag>
            <el-tag v-else type="info">无音频</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="categoryTags" label="分类标签" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" sortable="custom" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getGuidePointPage, deleteGuidePoint } from '@/api/guidePoint'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])

const searchForm = reactive({
  title: '',
  productId: '',
  location: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const { data } = await getGuidePointPage(params)
    tableData.value = data.records || []
    pagination.total = data.total || 0
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 操作
const handleBatchCreate = () => {
  router.push('/guide-point/batch-create')
}

const handleDetail = (row) => {
  router.push(`/guide-point/detail/${row.pointId}`)
}

const handleEdit = (row) => {
  router.push(`/guide-point/edit/${row.pointId}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除讲解点"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteGuidePoint(row.pointId)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.guide-point-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
