<template>
  <div class="relation-container">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="selectedPointId"
            placeholder="选择讲解点"
            filterable
            clearable
            style="width: 100%"
            @change="handlePointChange"
          >
            <el-option
              v-for="point in pointList"
              :key="point.pointId"
              :label="point.pointName"
              :value="point.pointId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="selectedAudioId"
            placeholder="选择音频"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="audio in audioList"
              :key="audio.audioId"
              :label="audio.audioName"
              :value="audio.audioId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleAddRelation" :disabled="!selectedPointId || !selectedAudioId">
            添加关系
          </el-button>
          <el-button @click="handleRefresh">刷新</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 关系列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>讲解点-音频关系列表</span>
          <span class="count-badge">共 {{ relationList.length }} 条关系</span>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="relationList"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="relationId" label="关系ID" width="100" />
        <el-table-column prop="pointId" label="讲解点ID" width="120" />
        <el-table-column prop="pointName" label="讲解点名称" min-width="160" />
        <el-table-column prop="audioId" label="音频ID" width="100" />
        <el-table-column label="音频名称" min-width="150">
          <template #default="{ row }">
            {{ getAudioName(row.audioId) }}
          </template>
        </el-table-column>
        <el-table-column label="音频预览" width="120">
          <template #default="{ row }">
            <el-button
              v-if="getAudioUrl(row.audioId)"
              type="text"
              size="small"
              @click="playAudio(getAudioUrl(row.audioId))"
            >
              播放
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="140" sortable="custom">
          <template #default="{ row }">
            <el-input-number
              v-model="row.sortOrder"
              :min="0"
              :max="999"
              size="small"
              @change="handleSortOrderChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteRelation(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" controls style="display: none;"></audio>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAudiosByPointId,
  addPointAudioRelation,
  removePointAudioRelation,
  updatePointAudioSortOrder,
  getAllAudios
} from '@/api/relations'
import { getGuidePointPage } from '@/api/guidePoint'

// 响应式数据
const loading = ref(false)
const relationList = ref([])
const pointList = ref([])
const audioList = ref([])
const selectedPointId = ref('')
const selectedAudioId = ref('')
const audioPlayer = ref(null)

// 获取讲解点列表
const fetchPointList = async () => {
  try {
    const response = await getGuidePointPage({ current: 1, size: 1000 })
    if (response.code === 200) {
      pointList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取讲解点列表失败:', error)
    ElMessage.error('获取讲解点列表失败')
  }
}

// 获取音频列表
const fetchAudioList = async () => {
  try {
    const response = await getAllAudios()
    if (response.code === 200) {
      audioList.value = response.data || []
    }
  } catch (error) {
    console.error('获取音频列表失败:', error)
    ElMessage.error('获取音频列表失败')
  }
}

// 获取关系列表
const fetchRelationList = async () => {
  if (!selectedPointId.value) {
    relationList.value = []
    return
  }

  try {
    loading.value = true
    const response = await getAudiosByPointId(selectedPointId.value)
    if (response.code === 200) {
      relationList.value = response.data || []
    }
  } catch (error) {
    console.error('获取关系列表失败:', error)
    ElMessage.error('获取关系列表失败')
  } finally {
    loading.value = false
  }
}

// 讲解点选择变化
const handlePointChange = () => {
  fetchRelationList()
}

// 添加关系
const handleAddRelation = async () => {
  try {
    const data = {
      pointId: selectedPointId.value,
      audioId: selectedAudioId.value,
      sortOrder: 0
    }
    
    const response = await addPointAudioRelation(data)
    if (response.code === 200) {
      ElMessage.success('添加关系成功')
      selectedAudioId.value = ''
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '添加关系失败')
    }
  } catch (error) {
    console.error('添加关系失败:', error)
    ElMessage.error('添加关系失败')
  }
}

// 删除关系
const handleDeleteRelation = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个关系吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await removePointAudioRelation(row.pointId, row.audioId)
    if (response.code === 200) {
      ElMessage.success('删除关系成功')
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '删除关系失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除关系失败:', error)
      ElMessage.error('删除关系失败')
    }
  }
}

// 排序变化
const handleSortOrderChange = async (row) => {
  try {
    const response = await updatePointAudioSortOrder(row.relationId, row.sortOrder)
    if (response.code === 200) {
      ElMessage.success('更新排序成功')
    } else {
      ElMessage.error(response.message || '更新排序失败')
    }
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
  }
}

// 表格排序
const handleSortChange = ({ prop, order }) => {
  if (prop === 'sortOrder') {
    if (order === 'ascending') {
      relationList.value.sort((a, b) => a.sortOrder - b.sortOrder)
    } else if (order === 'descending') {
      relationList.value.sort((a, b) => b.sortOrder - a.sortOrder)
    }
  }
}

// 播放音频
const playAudio = (audioUrl) => {
  if (audioPlayer.value) {
    audioPlayer.value.src = audioUrl
    audioPlayer.value.play()
  }
}

// 刷新
const handleRefresh = () => {
  fetchRelationList()
}

// 获取讲解点名称
const getPointName = (pointId) => {
  const point = pointList.value.find(p => p.pointId === pointId)
  return point ? point.title : `讲解点${pointId}`
}

// 获取音频名称
const getAudioName = (audioId) => {
  const audio = audioList.value.find(a => a.audioId === audioId)
  return audio ? audio.audioName : `音频${audioId}`
}

// 获取音频URL
const getAudioUrl = (audioId) => {
  const audio = audioList.value.find(a => a.audioId === audioId)
  return audio ? audio.audioUrl : ''
}

// 初始化
onMounted(() => {
  fetchPointList()
  fetchAudioList()
})
</script>

<style lang="scss" scoped>
.relation-container {
  .operation-bar {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .count-badge {
        background: #f0f2f5;
        color: #606266;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}
</style>
