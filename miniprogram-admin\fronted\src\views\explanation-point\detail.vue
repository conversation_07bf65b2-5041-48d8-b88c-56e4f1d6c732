<template>
  <div class="explanation-point-detail">
    <div class="page-header">
      <h2>讲解点详情</h2>
      <div class="header-actions">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="讲解点ID">
          {{ detail.pointId }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解点名称">
          {{ detail.pointName }}
        </el-descriptions-item>

        <el-descriptions-item label="排序">
          {{ detail.sortOrder }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解点图片" :span="2">
          <el-image
            v-if="detail.pointImage"
            :src="detail.pointImage"
            style="width: 300px; height: 200px"
            fit="cover"
            :preview-src-list="[detail.pointImage]"
          />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(detail.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(detail.updatedAt) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getExplanationPointById } from '@/api/explanationPoint'
import { formatDateTime } from '@/utils/date'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getExplanationPointById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取讲解点详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 操作
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/explanation-point/edit/${route.params.id}`)
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.explanation-point-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
