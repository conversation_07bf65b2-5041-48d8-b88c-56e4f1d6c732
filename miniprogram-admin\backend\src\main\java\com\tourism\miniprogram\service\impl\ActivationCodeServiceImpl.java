package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.ActivationCode;
import com.tourism.miniprogram.mapper.ActivationCodeMapper;
import com.tourism.miniprogram.service.ActivationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 激活码服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ActivationCodeServiceImpl extends ServiceImpl<ActivationCodeMapper, ActivationCode> implements ActivationCodeService {

    @Override
    public List<ActivationCode> getActivationCodesByScenicId(Integer scenicId) {
        try {
            return baseMapper.selectActivationCodesByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取激活码列表失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取激活码列表失败");
        }
    }

    @Override
    public List<ActivationCode> getActivationCodesByStatus(String status) {
        try {
            return baseMapper.selectActivationCodesByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取激活码列表失败，status: {}", status, e);
            throw new RuntimeException("根据状态获取激活码列表失败");
        }
    }

    @Override
    public ActivationCode getByCode(String code) {
        try {
            return baseMapper.selectByCode(code);
        } catch (Exception e) {
            log.error("根据激活码查找失败，code: {}", code, e);
            throw new RuntimeException("根据激活码查找失败");
        }
    }

    @Override
    public String generateActivationCode() {
        // 生成唯一的激活码
        return "ACT" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }

    @Override
    @Transactional
    public boolean batchGenerateActivationCodes(Integer count, Integer scenicId, Integer productId, Integer bundleId) {
        try {
            List<ActivationCode> activationCodes = new ArrayList<>();
            
            for (int i = 0; i < count; i++) {
                ActivationCode activationCode = new ActivationCode();
                activationCode.setCode(generateActivationCode());
                activationCode.setScenicId(scenicId);
                activationCode.setProductId(productId);
                activationCode.setBundleId(bundleId);
                activationCode.setStatus("unused");
                activationCode.setCreatedAt(LocalDateTime.now());
                
                activationCodes.add(activationCode);
            }
            
            boolean success = saveBatch(activationCodes);
            if (success) {
                log.info("批量生成激活码成功，数量: {}, 景区ID: {}", count, scenicId);
                return true;
            } else {
                log.error("批量生成激活码失败");
                return false;
            }
            
        } catch (Exception e) {
            log.error("批量生成激活码失败，count: {}, scenicId: {}", count, scenicId, e);
            throw new RuntimeException("批量生成激活码失败");
        }
    }
}
