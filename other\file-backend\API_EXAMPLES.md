# API使用示例

本文档提供了旅游讲解小程序文件管理API的详细使用示例。

## 基础信息

- **基础URL**: `http://localhost:8080/api`
- **API文档**: `http://localhost:8080/api/doc.html`
- **健康检查**: `http://localhost:8080/api/health/check`

## 1. 健康检查

### 检查系统状态

```bash
curl -X GET "http://localhost:8080/api/health/check"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "系统运行正常",
  "data": {
    "status": "UP",
    "timestamp": "2023-12-01 12:34:56",
    "service": "Tourism File Management API",
    "version": "1.0.0"
  },
  "timestamp": 1640995200000
}
```

## 2. 文件上传

### 单文件上传

```bash
curl -X POST "http://localhost:8080/api/file/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/image.jpg"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "a1b2c3d4e5f6",
    "originalFileName": "景区图片.jpg",
    "fileType": "image",
    "fileSize": 1024000,
    "downloadUrl": "https://example.cos.ap-beijing.myqcloud.com/tourism/files/image/2023/12/01/20231201_123456_abc123.jpg?sign=xxx",
    "cdnUrl": "https://cdn.example.com/tourism/files/image/2023/12/01/20231201_123456_abc123.jpg",
    "uploadTime": "2023-12-01 12:34:56",
    "urlExpireTime": 3600
  },
  "timestamp": 1640995200000
}
```

### 批量文件上传

```bash
curl -X POST "http://localhost:8080/api/file/upload/batch" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@/path/to/image1.jpg" \
  -F "files=@/path/to/video1.mp4" \
  -F "files=@/path/to/image2.png"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "批量文件上传成功",
  "data": [
    {
      "fileId": "a1b2c3d4e5f6",
      "originalFileName": "image1.jpg",
      "fileType": "image",
      "fileSize": 1024000,
      "downloadUrl": "https://example.cos.ap-beijing.myqcloud.com/...",
      "cdnUrl": "https://cdn.example.com/...",
      "uploadTime": "2023-12-01 12:34:56",
      "urlExpireTime": 3600
    },
    {
      "fileId": "b2c3d4e5f6g7",
      "originalFileName": "video1.mp4",
      "fileType": "video",
      "fileSize": 5120000,
      "downloadUrl": "https://example.cos.ap-beijing.myqcloud.com/...",
      "cdnUrl": "https://cdn.example.com/...",
      "uploadTime": "2023-12-01 12:34:57",
      "urlExpireTime": 3600
    }
  ],
  "timestamp": 1640995200000
}
```

## 3. 文件下载

### 生成下载URL

```bash
curl -X GET "http://localhost:8080/api/file/download-url?filePath=tourism/files/image/2023/12/01/20231201_123456_abc123.jpg"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "下载URL生成成功",
  "data": "https://example.cos.ap-beijing.myqcloud.com/tourism/files/image/2023/12/01/20231201_123456_abc123.jpg?sign=xxx&expires=1640998800",
  "timestamp": 1640995200000
}
```

### 直接下载文件

使用生成的URL直接下载：

```bash
curl -o downloaded_file.jpg "https://example.cos.ap-beijing.myqcloud.com/tourism/files/image/2023/12/01/20231201_123456_abc123.jpg?sign=xxx&expires=1640998800"
```

## 4. 文件删除

```bash
curl -X DELETE "http://localhost:8080/api/file/delete?filePath=tourism/files/image/2023/12/01/20231201_123456_abc123.jpg"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "文件删除成功",
  "data": true,
  "timestamp": 1640995200000
}
```

## 5. JavaScript示例

### 使用Fetch API上传文件

```javascript
// 单文件上传
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await fetch('http://localhost:8080/api/file/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('上传成功:', result.data);
            return result.data;
        } else {
            console.error('上传失败:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('上传异常:', error);
        throw error;
    }
}

// 批量文件上传
async function uploadFiles(files) {
    const formData = new FormData();
    
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    
    try {
        const response = await fetch('http://localhost:8080/api/file/upload/batch', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('批量上传成功:', result.data);
            return result.data;
        } else {
            console.error('批量上传失败:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('批量上传异常:', error);
        throw error;
    }
}

// 生成下载URL
async function generateDownloadUrl(filePath) {
    try {
        const response = await fetch(`http://localhost:8080/api/file/download-url?filePath=${encodeURIComponent(filePath)}`);
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('下载URL生成成功:', result.data);
            return result.data;
        } else {
            console.error('下载URL生成失败:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('下载URL生成异常:', error);
        throw error;
    }
}

// 使用示例
document.getElementById('fileInput').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const uploadResult = await uploadFile(file);
            console.log('文件上传成功，下载URL:', uploadResult.downloadUrl);
            
            // 如果需要重新生成URL
            const newUrl = await generateDownloadUrl(uploadResult.filePath);
            console.log('新的下载URL:', newUrl);
        } catch (error) {
            alert('文件上传失败: ' + error.message);
        }
    }
});
```

## 6. 错误处理

### 常见错误响应

#### 文件格式不支持
```json
{
  "code": 400,
  "message": "不支持的文件格式: txt，支持的格式: jpg,jpeg,png,gif,bmp,webp, mp4,avi,mov,wmv,flv,3gp,mkv",
  "data": null,
  "timestamp": 1640995200000
}
```

#### 文件大小超限
```json
{
  "code": 400,
  "message": "文件大小超出限制，最大允许 100.00 MB，当前文件 150.00 MB",
  "data": null,
  "timestamp": 1640995200000
}
```

#### 文件为空
```json
{
  "code": 400,
  "message": "文件不能为空",
  "data": null,
  "timestamp": 1640995200000
}
```

#### 系统异常
```json
{
  "code": 500,
  "message": "文件上传失败: COS服务异常",
  "data": null,
  "timestamp": 1640995200000
}
```

## 7. 最佳实践

### 前端文件上传最佳实践

1. **文件类型检查**: 在前端先检查文件类型，避免无效请求
2. **文件大小检查**: 在前端先检查文件大小，提升用户体验
3. **上传进度显示**: 使用XMLHttpRequest显示上传进度
4. **错误处理**: 完善的错误处理和用户提示
5. **重试机制**: 网络异常时的重试机制

### 后端集成最佳实践

1. **异步处理**: 大文件上传使用异步处理
2. **限流控制**: 控制上传频率，防止滥用
3. **日志记录**: 完整的操作日志记录
4. **监控告警**: 监控上传成功率和异常情况
5. **缓存策略**: 合理使用CDN和缓存策略
