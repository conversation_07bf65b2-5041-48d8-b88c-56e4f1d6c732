<template>
  <div class="city-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑城市' : '新增城市' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="城市名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入城市名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="所属省份" prop="provinceId">
          <el-select
            v-model="form.provinceId"
            placeholder="请选择省份"
            style="width: 100%"
          >
            <el-option
              v-for="province in provinceList"
              :key="province.id"
              :label="province.name"
              :value="province.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="城市代码" prop="code">
          <el-input
            v-model="form.code"
            placeholder="请输入城市代码"
            maxlength="10"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCityInfo, createCity, updateCity } from '@/api/city'
import { getProvinceList } from '@/api/province'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const formRef = ref()
const provinceList = ref([])

const form = reactive({
  name: '',
  provinceId: '',
  code: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入城市名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  provinceId: [
    { required: true, message: '请选择所属省份', trigger: 'change' }
  ],
  code: [
    { required: true, message: '请输入城市代码', trigger: 'blur' },
    { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/, message: '城市代码只能包含大写字母和数字', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getCityInfo(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取城市详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateCity(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createCity(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/city/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.city-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
