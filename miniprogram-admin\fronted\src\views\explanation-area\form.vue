<template>
  <div class="explanation-area-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解区域' : '新增讲解区域' }}</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="区域名称" prop="areaName">
          <el-input
            v-model="form.areaName"
            placeholder="请输入区域名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>



        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getExplanationAreaById,
  createExplanationArea,
  updateExplanationArea
} from '@/api/explanationArea'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const form = reactive({
  areaName: '',
  sortOrder: 0
})

// 验证规则
const rules = {
  areaName: [
    { required: true, message: '请输入区域名称', trigger: 'blur' },
    { min: 1, max: 100, message: '区域名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 获取详情数据（编辑模式）
const fetchDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const { data } = await getExplanationAreaById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取讲解区域详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateExplanationArea(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createExplanationArea(form)
      ElMessage.success('创建成功')
    }
    
    router.back()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.explanation-area-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
