import request from '@/utils/request'

// 分页获取门票列表
export function getCouponPage(params) {
  return request({
    url: '/coupons/page',
    method: 'get',
    params
  })
}

// 获取门票详情
export function getCouponById(id) {
  return request({
    url: `/coupons/${id}`,
    method: 'get'
  })
}

// 根据用户ID获取门票列表
export function getCouponsByUserId(userId) {
  return request({
    url: `/coupons/user/${userId}`,
    method: 'get'
  })
}

// 根据景区ID获取门票列表
export function getCouponsByScenicId(scenicId) {
  return request({
    url: `/coupons/scenic/${scenicId}`,
    method: 'get'
  })
}

// 根据状态获取门票列表
export function getCouponsByStatus(status) {
  return request({
    url: `/coupons/status/${status}`,
    method: 'get'
  })
}

// 激活门票
export function activateCoupon(id, activationCode) {
  return request({
    url: `/coupons/${id}/activate`,
    method: 'post',
    data: { activationCode }
  })
}

// 使用门票
export function useCoupon(id, data) {
  return request({
    url: `/coupons/${id}/use`,
    method: 'post',
    data
  })
}

// 创建门票
export function createCoupon(data) {
  return request({
    url: '/coupons',
    method: 'post',
    data
  })
}

// 更新门票
export function updateCoupon(id, data) {
  return request({
    url: `/coupons/${id}`,
    method: 'put',
    data
  })
}

// 删除门票
export function deleteCoupon(id) {
  return request({
    url: `/coupons/${id}`,
    method: 'delete'
  })
}
