// 指南点服务模块
const httpService = require('./httpService');

class GuidePointService {
  constructor() {
    this.cacheKeyPrefix = 'guide_point_cache_';
    this.cacheTime = 3 * 60 * 1000; // 3分钟缓存
  }

  // 获取指南点列表
  async getGuidePointList(params = {}) {
    try {
      const {
        productId,
        current = 1,
        size = 20,
        status = '1'
      } = params;

      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('list', { productId, current, size, status });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的指南点列表数据');
        return cachedData;
      }

      console.log(`从服务器获取指南点列表 (产品ID: ${productId})`);
      const queryParams = {
        productId: productId.toString(),
        current: current.toString(),
        size: size.toString(),
        status: status
      };

      const guidePoints = await httpService.get('/api/guide-points/page', queryParams, {
        loadingText: '加载指南点中...'
      });

      // 处理返回的数据结构
      let listData = { list: [], total: 0, current: 1, size: 20 };

      if (guidePoints) {
        if (guidePoints.records && Array.isArray(guidePoints.records)) {
          // 标准分页数据结构
          listData = {
            list: guidePoints.records,
            total: guidePoints.total || guidePoints.records.length,
            current: guidePoints.current || current,
            size: guidePoints.size || size,
            pages: guidePoints.pages || 1
          };
          console.log('使用分页数据结构，指南点数量:', guidePoints.records.length);
        } else if (Array.isArray(guidePoints)) {
          // 直接数组结构
          listData = {
            list: guidePoints,
            total: guidePoints.length,
            current: current,
            size: size,
            pages: 1
          };
          console.log('使用直接数组数据结构，指南点数量:', guidePoints.length);
        } else {
          console.log('未识别的数据结构:', typeof guidePoints, guidePoints);
        }
      } else {
        console.log('guidePoints为空或undefined');
      }

      // 不在服务端进行分组，直接返回原始数据
      // 分组逻辑移到页面端处理

      // 缓存数据
      this.setCache(cacheKey, listData);

      console.log('指南点列表获取成功，数量:', listData.list.length);
      return listData;
    } catch (error) {
      console.error('获取指南点列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据示例图片URL分组指南点
  groupByExampleImage(guidePoints) {
    console.log('开始分组指南点，数量:', guidePoints ? guidePoints.length : 0);

    if (!Array.isArray(guidePoints)) {
      console.log('guidePoints不是数组，返回空数组');
      return [];
    }

    const grouped = {};

    guidePoints.forEach((point, index) => {
      const formattedPoint = this.formatGuidePointForDisplay(point);

      if (formattedPoint) {
        const exampleImageUrl = formattedPoint.exampleImageUrl || 'default';

        if (!grouped[exampleImageUrl]) {
          grouped[exampleImageUrl] = {
            exampleImageUrl: exampleImageUrl === 'default' ? '' : exampleImageUrl,
            points: []
          };
        }

        grouped[exampleImageUrl].points.push(formattedPoint);
      } else {
        console.log(`第${index + 1}个指南点格式化失败，原始数据:`, point);
      }
    });

    // 转换为数组格式，并按排序字段排序
    const result = Object.values(grouped).map(group => ({
      ...group,
      points: group.points.sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }));

    console.log('分组完成，分组数量:', result.length);
    return result;
  }

  // 获取单个指南点详情
  async getGuidePointDetail(pointId) {
    try {
      if (!pointId) {
        throw new Error('指南点ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('detail', { pointId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的指南点详情数据');
        return cachedData;
      }

      console.log(`从服务器获取指南点详情 (指南点ID: ${pointId})`);
      const pointDetail = await httpService.get(`/api/guide-points/${pointId}`, {}, {
        loadingText: '加载指南点详情中...'
      });

      // 处理返回的数据结构
      let detailData = null;
      if (pointDetail && pointDetail.data) {
        detailData = pointDetail.data;
      } else if (pointDetail) {
        detailData = pointDetail;
      }

      if (!detailData) {
        throw new Error('指南点详情数据为空');
      }

      // 缓存数据
      this.setCache(cacheKey, detailData);

      console.log('指南点详情获取成功:', detailData.title || detailData.pointId);
      return detailData;
    } catch (error) {
      console.error('获取指南点详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 缓存相关方法
  buildCacheKey(type, params) {
    const paramsStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramsStr}`;
  }

  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取指南点缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置指南点缓存失败:', error);
    }
  }

  clearCache(pattern = null) {
    try {
      const storage = wx.getStorageInfoSync();
      storage.keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          if (!pattern || key.includes(pattern)) {
            wx.removeStorageSync(key);
          }
        }
      });
      console.log('指南点缓存已清除');
    } catch (error) {
      console.error('清除指南点缓存失败:', error);
    }
  }

  // 验证指南点数据
  validateGuidePoint(point) {
    if (!point) {
      return false;
    }

    // 放宽验证条件，只要有id或者有title就认为是有效的
    const hasId = point.id || point.pointId || point.point_id;
    const hasTitle = point.title || point.name;

    const isValid = hasId || hasTitle; // 只要有其中一个就认为有效

    if (!isValid) {
      console.log('指南点验证失败，数据:', point);
    }

    return isValid;
  }

  // 格式化指南点数据用于显示
  formatGuidePointForDisplay(point) {
    if (!this.validateGuidePoint(point)) {
      return null;
    }

    const formatted = {
      id: point.id || point.pointId || point.point_id || 'unknown',
      pointId: point.point_id || point.pointId || point.id || 'unknown',
      title: point.title || point.name || '未知指南点',
      location: point.location || point.position || '',
      audioUrl: point.audio_url || point.audioUrl || point.audio || '',
      duration: point.duration || point.time || 0,
      exampleImageUrl: point.example_image_url || point.exampleImageUrl || point.exampleImage || '',
      descriptionImageUrl: point.description_image_url || point.descriptionImageUrl || point.descriptionImage || '',
      categoryTag: point.category_tag || point.categoryTag || point.category || '',
      productId: point.product_id || point.productId || point.guideProductId || '',
      status: point.status || 1,
      sort: point.sort || point.order || 0,
      createdAt: point.created_at || point.createdAt || point.createTime,
      updatedAt: point.updated_at || point.updatedAt || point.updateTime
    };

    return formatted;
  }

  // 格式化时长显示
  formatDuration(duration) {
    if (!duration || duration <= 0) {
      return '00:00:00';
    }

    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
}

// 创建单例实例
const guidePointService = new GuidePointService();

module.exports = guidePointService;
