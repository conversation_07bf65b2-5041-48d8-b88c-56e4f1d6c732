<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="产品名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-input
        v-model="listQuery.scenicId"
        placeholder="景区ID"
        style="width: 150px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-input
        v-model="listQuery.orderId"
        placeholder="订单ID"
        style="width: 150px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.productType"
        placeholder="产品类型"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option label="单品" value="single" />
        <el-option label="组合包" value="bundle" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="启用" :value="1" />
        <el-option label="禁用" :value="0" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        添加产品
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="产品名称" min-width="150">
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column label="景区ID" width="100" align="center">
        <template #default="{ row }">
          {{ row.scenicId }}
        </template>
      </el-table-column>
      <el-table-column label="订单ID" width="100" align="center">
        <template #default="{ row }">
          {{ row.orderId }}
        </template>
      </el-table-column>
      <el-table-column label="产品类型" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.productType === 'single' ? 'primary' : 'success'">
            {{ row.productType === 'single' ? '单品' : '组合包' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="价格" width="100" align="center">
        <template #default="{ row }">
          ¥{{ row.price }}
        </template>
      </el-table-column>
      <el-table-column label="有效期(小时)" width="120" align="center">
        <template #default="{ row }">
          {{ row.validityHours }}
        </template>
      </el-table-column>
      <el-table-column label="需要激活" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.requireActivation ? 'warning' : 'info'">
            {{ row.requireActivation ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="listQuery.current"
        v-model:page-size="listQuery.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { getProductPage, deleteProduct } from '@/api/product'

const router = useRouter()

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(false)

const listQuery = reactive({
  current: 1,
  size: 10,
  name: '',
  scenicId: '',
  orderId: '',
  productType: '',
  status: ''
})

// 获取列表数据
const getList = async () => {
  listLoading.value = true
  try {
    const params = { ...listQuery }
    // 清空空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const { data } = await getProductPage(params)
    list.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    listLoading.value = false
  }
}

// 搜索
const handleFilter = () => {
  listQuery.current = 1
  getList()
}

// 创建
const handleCreate = () => {
  router.push('/product/create')
}

// 编辑
const handleUpdate = (row) => {
  router.push(`/product/edit/${row.id}`)
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除产品"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteProduct(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除产品失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页
const handleSizeChange = (size) => {
  listQuery.size = size
  listQuery.current = 1
  getList()
}

const handleCurrentChange = (current) => {
  listQuery.current = current
  getList()
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.filter-container {
  padding: 10px 0;
}

.filter-item {
  margin-right: 10px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
