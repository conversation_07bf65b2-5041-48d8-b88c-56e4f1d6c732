<template>
  <div class="review-detail">
    <div class="page-header">
      <h2>评价详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="评价ID">
          {{ detail.reviewId }}
        </el-descriptions-item>
        <el-descriptions-item label="用户ID">
          {{ detail.userId }}
        </el-descriptions-item>
        <el-descriptions-item label="产品ID">
          {{ detail.productId }}
        </el-descriptions-item>
        <el-descriptions-item label="评分">
          <el-rate
            v-model="detail.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </el-descriptions-item>
        <el-descriptions-item label="评价内容" :span="2">
          <div class="content-text">{{ detail.content }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="评价时间">
          {{ detail.reviewTime }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '显示' : '隐藏' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getReviewById } from '@/api/review'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getReviewById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取评价详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/review/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/review/list')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.review-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}
</style>
