package com.tourism.miniprogram.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置类
 * 
 * 配置定时任务的线程池和相关参数
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Configuration
public class ScheduleConfig {

    /**
     * 配置定时任务线程池
     * 
     * @return TaskScheduler 任务调度器
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        // 设置线程池大小
        scheduler.setPoolSize(5);
        
        // 设置线程名前缀
        scheduler.setThreadNamePrefix("tourism-scheduled-task-");
        
        // 设置线程池关闭时等待所有任务完成
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        
        // 设置等待时间，超过这个时间强制关闭线程池
        scheduler.setAwaitTerminationSeconds(60);
        
        // 设置拒绝策略：由调用线程处理该任务
        scheduler.setRejectedExecutionHandler((r, executor) -> {
            log.warn("定时任务线程池已满，任务被拒绝执行: {}", r.toString());
        });
        
        // 初始化线程池
        scheduler.initialize();
        
        log.info("定时任务线程池配置完成，线程池大小: {}", scheduler.getPoolSize());
        
        return scheduler;
    }
}
