package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.dto.PointAudioRelationDTO;
import com.tourism.miniprogram.entity.PointAudioRelation;
import com.tourism.miniprogram.mapper.PointAudioRelationMapper;
import com.tourism.miniprogram.service.PointAudioRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 讲解点音频关系服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Slf4j
@Service
public class PointAudioRelationServiceImpl extends ServiceImpl<PointAudioRelationMapper, PointAudioRelation> implements PointAudioRelationService {

    @Override
    public List<PointAudioRelation> getByPointId(Integer pointId) {
        try {
            if (pointId == null) {
                throw new IllegalArgumentException("讲解点ID不能为空");
            }
            return baseMapper.selectByPointId(pointId);
        } catch (Exception e) {
            log.error("根据讲解点ID获取音频关系列表失败，pointId: {}", pointId, e);
            throw new RuntimeException("获取音频关系列表失败");
        }
    }

    @Override
    public List<PointAudioRelationDTO> getByPointIdWithDetails(Integer pointId) {
        try {
            if (pointId == null) {
                throw new IllegalArgumentException("讲解点ID不能为空");
            }
            return baseMapper.selectByPointIdWithDetails(pointId);
        } catch (Exception e) {
            log.error("根据讲解点ID获取音频关系详细列表失败，pointId: {}", pointId, e);
            throw new RuntimeException("获取音频关系详细列表失败");
        }
    }

    @Override
    public List<PointAudioRelation> getByAudioId(Integer audioId) {
        try {
            if (audioId == null) {
                throw new IllegalArgumentException("音频ID不能为空");
            }
            return baseMapper.selectByAudioId(audioId);
        } catch (Exception e) {
            log.error("根据音频ID获取讲解点关系列表失败，audioId: {}", audioId, e);
            throw new RuntimeException("获取讲解点关系列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRelation(Integer pointId, Integer audioId, Integer sortOrder) {
        try {
            if (pointId == null || audioId == null) {
                throw new IllegalArgumentException("讲解点ID和音频ID不能为空");
            }

            // 检查关系是否已存在
            QueryWrapper<PointAudioRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("point_id", pointId).eq("audio_id", audioId);
            if (baseMapper.selectCount(queryWrapper) > 0) {
                throw new RuntimeException("该关系已存在");
            }

            PointAudioRelation relation = new PointAudioRelation();
            relation.setPointId(pointId);
            relation.setAudioId(audioId);
            relation.setSortOrder(sortOrder != null ? sortOrder : 0);

            return baseMapper.insert(relation) > 0;
        } catch (Exception e) {
            log.error("添加讲解点音频关系失败，pointId: {}, audioId: {}", pointId, audioId, e);
            throw new RuntimeException("添加关系失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRelation(Integer pointId, Integer audioId) {
        try {
            if (pointId == null || audioId == null) {
                throw new IllegalArgumentException("讲解点ID和音频ID不能为空");
            }

            QueryWrapper<PointAudioRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("point_id", pointId).eq("audio_id", audioId);
            return baseMapper.delete(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("删除讲解点音频关系失败，pointId: {}, audioId: {}", pointId, audioId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddRelations(List<PointAudioRelation> relations) {
        try {
            if (relations == null || relations.isEmpty()) {
                throw new IllegalArgumentException("关系列表不能为空");
            }
            return baseMapper.batchInsert(relations) > 0;
        } catch (Exception e) {
            log.error("批量添加讲解点音频关系失败", e);
            throw new RuntimeException("批量添加关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByPointId(Integer pointId) {
        try {
            if (pointId == null) {
                throw new IllegalArgumentException("讲解点ID不能为空");
            }
            return baseMapper.deleteByPointId(pointId) >= 0;
        } catch (Exception e) {
            log.error("根据讲解点ID删除关系失败，pointId: {}", pointId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByAudioId(Integer audioId) {
        try {
            if (audioId == null) {
                throw new IllegalArgumentException("音频ID不能为空");
            }
            return baseMapper.deleteByAudioId(audioId) >= 0;
        } catch (Exception e) {
            log.error("根据音频ID删除关系失败，audioId: {}", audioId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Integer relationId, Integer sortOrder) {
        try {
            if (relationId == null || sortOrder == null) {
                throw new IllegalArgumentException("关系ID和排序值不能为空");
            }
            return baseMapper.updateSortOrder(relationId, sortOrder) > 0;
        } catch (Exception e) {
            log.error("更新排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            throw new RuntimeException("更新排序失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders) {
        try {
            if (relationIds == null || sortOrders == null || relationIds.size() != sortOrders.size()) {
                throw new IllegalArgumentException("关系ID列表和排序值列表不能为空且长度必须一致");
            }

            for (int i = 0; i < relationIds.size(); i++) {
                baseMapper.updateSortOrder(relationIds.get(i), sortOrders.get(i));
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新排序失败", e);
            throw new RuntimeException("批量更新排序失败");
        }
    }
}
