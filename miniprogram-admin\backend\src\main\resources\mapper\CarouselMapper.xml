<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.CarouselMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.Carousel">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="subtitle" property="subtitle" />
        <result column="image" property="image" />
        <result column="scenic_id" property="scenicId" />
        <result column="province_id" property="provinceId" />
        <result column="type" property="type" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, image, product_bundles_id, province_id, sort, status, created_at, updated_at
    </sql>

    <!-- 根据条件获取轮播图列表 -->
    <select id="selectCarouselsByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM carousels
        WHERE status = 1
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        ORDER BY sort ASC, id DESC
    </select>

</mapper>
