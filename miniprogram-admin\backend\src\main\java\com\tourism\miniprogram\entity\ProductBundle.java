package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tourism.miniprogram.handler.StringListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品组合包实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product_bundles")
@ApiModel(value = "ProductBundle对象", description = "产品组合包信息")
public class ProductBundle implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组合包ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "组合包名称")
    @TableField("name")
    @NotBlank(message = "组合包名称不能为空")
    private String name;

    @ApiModelProperty(value = "组合包描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "组合包优惠金额")
    @TableField("discount")
    @NotNull(message = "优惠金额不能为空")
    @DecimalMin(value = "0.00", message = "优惠金额不能小于0")
    private BigDecimal discount;

    @ApiModelProperty(value = "包含的景区ID数组")
    @TableField(value = "scenic_ids", typeHandler = StringListTypeHandler.class)
    @NotNull(message = "景区ID列表不能为空")
    private List<String> scenicIds;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
