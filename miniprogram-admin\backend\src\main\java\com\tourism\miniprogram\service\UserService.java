package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.User;

/**
 * 用户服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface UserService extends IService<User> {

    /**
     * 根据openid获取用户信息
     *
     * @param openid 微信openid
     * @return 用户信息
     */
    User getUserByOpenid(String openid);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(User user);
}
