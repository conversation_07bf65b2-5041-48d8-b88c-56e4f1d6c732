import request from '@/utils/request'

// 分销员管理API

// 获取分销员列表
export function getDistributorList(params) {
  return request({
    url: '/distributor/list',
    method: 'get',
    params
  })
}

// 获取分销员详情
export function getDistributorDetail(id) {
  return request({
    url: `/distributor/${id}`,
    method: 'get'
  })
}

// 根据用户ID获取分销员信息
export function getDistributorByUserId(userId) {
  return request({
    url: `/distributor/user/${userId}`,
    method: 'get'
  })
}

// 根据分销员编号获取信息
export function getDistributorByCode(code) {
  return request({
    url: `/distributor/code/${code}`,
    method: 'get'
  })
}

// 创建分销员
export function createDistributor(data) {
  return request({
    url: '/distributor/create',
    method: 'post',
    data
  })
}

// 更新分销员信息
export function updateDistributor(id, data) {
  return request({
    url: `/distributor/${id}`,
    method: 'put',
    data
  })
}

// 更新分销员状态
export function updateDistributorStatus(id, status) {
  return request({
    url: `/distributor/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 冻结分销员
export function freezeDistributor(id, reason) {
  return request({
    url: `/distributor/${id}/freeze`,
    method: 'put',
    params: { reason }
  })
}

// 解冻分销员
export function unfreezeDistributor(id) {
  return request({
    url: `/distributor/${id}/unfreeze`,
    method: 'put'
  })
}

// 生成专属二维码
export function generateQrCode(id) {
  return request({
    url: `/distributor/${id}/qrcode`,
    method: 'post'
  })
}

// 获取分销员统计信息
export function getDistributorStatistics() {
  return request({
    url: '/distributor/statistics',
    method: 'get'
  })
}

// 检查用户是否为分销员
export function checkIsDistributor(userId) {
  return request({
    url: `/distributor/check/${userId}`,
    method: 'get'
  })
}

// 分销提现记录API

// 获取提现记录列表
export function getWithdrawalRecordList(params) {
  return request({
    url: '/distribution/withdrawal/list',
    method: 'get',
    params
  })
}

// 获取提现记录详情
export function getWithdrawalRecordDetail(id) {
  return request({
    url: `/distribution/withdrawal/${id}`,
    method: 'get'
  })
}

// 创建提现申请
export function createWithdrawalRequest(data) {
  return request({
    url: '/distribution/withdrawal/apply',
    method: 'post',
    data
  })
}

// 审核提现申请
export function reviewWithdrawalRequest(id, data) {
  return request({
    url: `/distribution/withdrawal/${id}/review`,
    method: 'put',
    data
  })
}

// 完成提现处理
export function completeWithdrawal(id, transactionNo) {
  return request({
    url: `/distribution/withdrawal/${id}/complete`,
    method: 'put',
    params: { transactionNo }
  })
}

// 获取提现统计
export function getWithdrawalStatistics(distributorId) {
  const url = distributorId 
    ? `/distribution/withdrawal/statistics/${distributorId}`
    : '/distribution/withdrawal/statistics'
  return request({
    url,
    method: 'get'
  })
}

// 分销产品佣金比例API

// 获取佣金比例列表
export function getCommissionRateList(params) {
  return request({
    url: '/distribution/commission-rate/list',
    method: 'get',
    params
  })
}

// 设置产品佣金比例
export function setProductCommissionRate(data) {
  return request({
    url: '/distribution/commission-rate/product',
    method: 'post',
    data
  })
}

// 设置组合包佣金比例
export function setBundleCommissionRate(data) {
  return request({
    url: '/distribution/commission-rate/bundle',
    method: 'post',
    data
  })
}

// 获取有效佣金比例
export function getEffectiveCommissionRate(distributorId, productId, bundleId) {
  const url = productId 
    ? '/distribution/commission-rate/effective/product'
    : '/distribution/commission-rate/effective/bundle'
  const params = { distributorId }
  if (productId) params.productId = productId
  if (bundleId) params.bundleId = bundleId
  
  return request({
    url,
    method: 'get',
    params
  })
}

// 删除佣金比例
export function removeCommissionRate(distributorId, productId, bundleId) {
  const url = productId 
    ? '/distribution/commission-rate/product'
    : '/distribution/commission-rate/bundle'
  const params = { distributorId }
  if (productId) params.productId = productId
  if (bundleId) params.bundleId = bundleId
  
  return request({
    url,
    method: 'delete',
    params
  })
}

// 分销订单API

// 获取分销订单列表
export function getDistributionOrderList(params) {
  return request({
    url: '/distribution/order/list',
    method: 'get',
    params
  })
}

// 获取分销订单详情
export function getDistributionOrderDetail(id) {
  return request({
    url: `/distribution/order/${id}`,
    method: 'get'
  })
}

// 创建分销订单记录
export function createDistributionOrder(data) {
  return request({
    url: '/distribution/order/create',
    method: 'post',
    data
  })
}

// 结算分销订单
export function settleDistributionOrder(id) {
  return request({
    url: `/distribution/order/${id}/settle`,
    method: 'put'
  })
}

// 批量结算分销订单
export function batchSettleDistributionOrders(ids) {
  return request({
    url: '/distribution/order/batch-settle',
    method: 'put',
    data: ids
  })
}

// 取消分销订单
export function cancelDistributionOrder(id) {
  return request({
    url: `/distribution/order/${id}/cancel`,
    method: 'put'
  })
}

// 获取佣金统计
export function getCommissionStatistics(distributorId) {
  const url = distributorId 
    ? `/distribution/order/statistics/${distributorId}`
    : '/distribution/order/statistics'
  return request({
    url,
    method: 'get'
  })
}

// 计算佣金金额
export function calculateCommissionAmount(orderAmount, commissionRate) {
  return request({
    url: '/distribution/order/calculate-commission',
    method: 'get',
    params: { orderAmount, commissionRate }
  })
}

// 检查分销记录
export function checkDistributionRecord(orderId) {
  return request({
    url: `/distribution/order/check/${orderId}`,
    method: 'get'
  })
}
