// 省份服务模块
const httpService = require('./httpService');

class ProvinceService {
  constructor() {
    this.cacheKey = 'provinces_cache';
    this.cacheTime = 10 * 60 * 1000; // 10分钟缓存
  }

  // 获取省份列表
  async getProvinces() {
    try {
      // 检查缓存
      const cachedData = this.getCache();
      if (cachedData) {
        console.log('使用缓存的省份数据');
        return cachedData;
      }

      console.log('从服务器获取省份数据');
      const provinces = await httpService.get('/api/provinces', {}, {
        loadingText: '加载省份中...'
      });

      // 缓存数据
      this.setCache(provinces);
      
      return provinces;
    } catch (error) {
      console.error('获取省份列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据省份ID获取省份信息
  async getProvinceById(provinceId) {
    try {
      if (!provinceId) {
        throw new Error('省份ID不能为空');
      }

      const province = await httpService.get(`/api/provinces/${provinceId}`, {}, {
        loadingText: '获取省份信息中...'
      });

      return province;
    } catch (error) {
      console.error('获取省份信息失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 搜索省份
  async searchProvinces(keyword) {
    try {
      if (!keyword) {
        return await this.getProvinces();
      }

      const provinces = await httpService.get('/api/provinces/search', {
        keyword: keyword
      }, {
        loadingText: '搜索中...'
      });

      return provinces;
    } catch (error) {
      console.error('搜索省份失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取热门省份
  async getHotProvinces(limit = 5) {
    try {
      const provinces = await httpService.get('/api/provinces/hot', {
        limit: limit
      }, {
        showLoading: false
      });

      return provinces;
    } catch (error) {
      console.error('获取热门省份失败:', error);
      // 热门省份失败时不显示错误提示
      throw error;
    }
  }

  // 缓存相关方法
  getCache() {
    try {
      const cached = wx.getStorageSync(this.cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(this.cacheKey);
        }
      }
    } catch (error) {
      console.error('读取省份缓存失败:', error);
    }
    return null;
  }

  setCache(data) {
    try {
      wx.setStorageSync(this.cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置省份缓存失败:', error);
    }
  }

  clearCache() {
    try {
      wx.removeStorageSync(this.cacheKey);
      console.log('省份缓存已清除');
    } catch (error) {
      console.error('清除省份缓存失败:', error);
    }
  }

  // 处理省份数据，添加选中状态
  processProvincesData(provinces, selectedIndex = 0) {
    return provinces.map((province, index) => ({
      ...province,
      selected: index === selectedIndex
    }));
  }

  // 验证省份数据
  validateProvince(province) {
    if (!province) {
      return false;
    }

    const requiredFields = ['id', 'name', 'code'];
    return requiredFields.every(field => province.hasOwnProperty(field) && province[field]);
  }

  // 格式化省份数据用于显示
  formatProvinceForDisplay(province) {
    if (!this.validateProvince(province)) {
      return null;
    }

    return {
      id: province.id,
      name: province.name,
      code: province.code,
      displayName: province.name,
      selected: province.selected || false,
      sort: province.sort || 0,
      status: province.status || 1
    };
  }

  // 批量格式化省份数据
  formatProvincesForDisplay(provinces) {
    if (!Array.isArray(provinces)) {
      return [];
    }

    return provinces
      .map(province => this.formatProvinceForDisplay(province))
      .filter(province => province !== null)
      .sort((a, b) => (a.sort || 0) - (b.sort || 0));
  }
}

// 创建单例实例
const provinceService = new ProvinceService();

module.exports = provinceService;
