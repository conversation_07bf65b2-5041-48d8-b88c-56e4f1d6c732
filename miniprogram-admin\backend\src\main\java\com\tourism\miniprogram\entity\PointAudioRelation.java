package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲解点音频关系实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("point_audio_relation")
@ApiModel(value = "PointAudioRelation对象", description = "讲解点音频关系信息")
public class PointAudioRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关系ID")
    @TableId(value = "relation_id", type = IdType.AUTO)
    private Integer relationId;

    @ApiModelProperty(value = "讲解点ID")
    @TableField("point_id")
    @NotNull(message = "讲解点ID不能为空")
    private Integer pointId;

    @ApiModelProperty(value = "音频ID")
    @TableField("audio_id")
    @NotNull(message = "音频ID不能为空")
    private Integer audioId;

    @ApiModelProperty(value = "音频在点中的顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
