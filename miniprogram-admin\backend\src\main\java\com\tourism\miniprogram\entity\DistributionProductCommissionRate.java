package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分销产品佣金比例实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("distribution_product_commission_rates")
@ApiModel(value = "DistributionProductCommissionRate对象", description = "分销产品佣金比例信息")
public class DistributionProductCommissionRate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "佣金比例ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分销员ID")
    @TableField("distributor_id")
    @NotNull(message = "分销员ID不能为空")
    private Integer distributorId;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    private Integer productId;

    @ApiModelProperty(value = "组合包ID")
    @TableField("bundle_id")
    private Integer bundleId;

    @ApiModelProperty(value = "特定产品佣金比例")
    @TableField("commission_rate")
    @NotNull(message = "佣金比例不能为空")
    @DecimalMin(value = "0.00", message = "佣金比例不能小于0")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
