<template>
  <div class="coupon-detail">
    <div class="page-header">
      <h2>门票详情</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="门票ID">{{ detail.id }}</el-descriptions-item>
        <el-descriptions-item label="卡券编码">{{ detail.code }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ detail.userId }}</el-descriptions-item>
        <el-descriptions-item label="景区ID">{{ detail.scenicId }}</el-descriptions-item>
        <el-descriptions-item label="产品ID">{{ detail.productId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="组合包ID">{{ detail.bundleId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detail.orderId }}</el-descriptions-item>
        <el-descriptions-item label="激活码ID">{{ detail.activationCodeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(detail.status)">
            {{ getStatusText(detail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="生效时间">{{ detail.validFrom || '-' }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ detail.validTo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="使用时间">{{ detail.usedAt || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detail.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detail.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCouponById } from '@/api/coupon'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = reactive({})

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'unactivated': 'info',
    'active': 'success',
    'used': 'warning',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'unactivated': '未激活',
    'active': '已激活',
    'used': '已使用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取详情数据
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getCouponById(route.params.id)
    Object.assign(detail, data)
  } catch (error) {
    ElMessage.error('获取门票详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.push('/coupon')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.coupon-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
