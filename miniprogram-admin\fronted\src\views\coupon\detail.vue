<template>
  <div class="coupon-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="handleBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>门票详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 门票基本信息 -->
      <el-card class="coupon-info-card">
        <template #header>
          <div class="card-header">
            <span>门票信息</span>
            <div>
              <el-tag :type="getStatusType(detail.status)" size="large">
                {{ getStatusText(detail.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <label>门票ID：</label>
            <span>{{ detail.id }}</span>
          </div>
          <div class="info-item">
            <label>卡券编码：</label>
            <span>{{ detail.code }}</span>
          </div>
          <div class="info-item">
            <label>用户ID：</label>
            <span>{{ detail.userId }}</span>
          </div>
          <div class="info-item">
            <label>景区ID：</label>
            <span>{{ detail.scenicId }}</span>
          </div>
          <div class="info-item">
            <label>产品ID：</label>
            <span>{{ detail.productId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>组合包ID：</label>
            <span>{{ detail.bundleId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>订单ID：</label>
            <span>{{ detail.orderId }}</span>
          </div>
          <div class="info-item">
            <label>激活码ID：</label>
            <span>{{ detail.activationCodeId || '-' }}</span>
          </div>
          <div class="info-item">
            <label>生效时间：</label>
            <span>{{ formatDate(detail.validFrom) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>过期时间：</label>
            <span>{{ formatDate(detail.validTo) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>使用时间：</label>
            <span>{{ formatDate(detail.usedAt) || '-' }}</span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(detail.createdAt) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 订单详细信息 -->
      <el-card v-if="orderInfo.id" class="order-info-card">
        <template #header>
          <div class="card-header">
            <span>关联订单信息</span>
            <div>
              <el-tag :type="getOrderStatusType(orderInfo.status)" size="small">
                {{ getOrderStatusText(orderInfo.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="order-info-section">
          <div class="info-grid">
            <div class="info-item">
              <label>订单编号：</label>
              <span>{{ orderInfo.orderNumber }}</span>
            </div>
            <div class="info-item">
              <label>订单金额：</label>
              <span class="money-text">¥{{ formatMoney(orderInfo.totalAmount) }}</span>
            </div>
            <div class="info-item">
              <label>支付金额：</label>
              <span class="money-text">¥{{ formatMoney(orderInfo.payAmount) }}</span>
            </div>
            <div class="info-item">
              <label>支付方式：</label>
              <span>{{ orderInfo.paymentMethod || '-' }}</span>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(orderInfo.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>支付时间：</label>
              <span>{{ formatDate(orderInfo.paidAt) || '-' }}</span>
            </div>
            <div class="info-item">
              <label>完成时间：</label>
              <span>{{ formatDate(orderInfo.completedAt) || '-' }}</span>
            </div>
            <div class="info-item">
              <label>取消时间：</label>
              <span>{{ formatDate(orderInfo.cancelledAt) || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 产品详细信息 -->
      <el-card v-if="productInfo.id" class="product-info-card">
        <template #header>
          <div class="card-header">
            <span>关联产品信息</span>
            <div>
              <el-tag :type="productInfo.status === 'active' ? 'success' : 'danger'" size="small">
                {{ productInfo.status === 'active' ? '上架' : '下架' }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="product-content">
          <div class="product-main-info">
            <div v-if="productInfo.imageUrl" class="product-image">
              <img :src="productInfo.imageUrl" :alt="productInfo.name" />
            </div>
            <div class="product-details">
              <h3>{{ productInfo.name }}</h3>
              <p class="product-description">{{ productInfo.description }}</p>
              <div class="product-meta">
                <div class="meta-item">
                  <label>产品类型：</label>
                  <span>{{ getProductTypeText(productInfo.productType) }}</span>
                </div>
                <div class="meta-item">
                  <label>价格：</label>
                  <span class="price-text">¥{{ formatMoney(productInfo.price) }}</span>
                </div>
                <div class="meta-item">
                  <label>景区：</label>
                  <span>{{ productInfo.scenicName || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="product-additional-info">
            <div class="info-grid">
              <div class="info-item">
                <label>有效期：</label>
                <span>{{ productInfo.validityPeriod || '-' }}</span>
              </div>
              <div class="info-item">
                <label>退改政策：</label>
                <span>{{ productInfo.refundPolicy || '-' }}</span>
              </div>
              <div class="info-item">
                <label>使用须知：</label>
                <span>{{ productInfo.usageInstructions || '-' }}</span>
              </div>
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDate(productInfo.createdAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 组合包详细信息 -->
      <el-card v-if="bundleInfo.id" class="bundle-info-card">
        <template #header>
          <div class="card-header">
            <span>关联组合包信息</span>
            <div>
              <el-tag type="success" size="small">组合包</el-tag>
            </div>
          </div>
        </template>

        <div class="bundle-content">
          <div class="bundle-main-info">
            <div v-if="bundleInfo.imageUrl" class="bundle-image">
              <img :src="bundleInfo.imageUrl" :alt="bundleInfo.name" />
            </div>
            <div class="bundle-details">
              <h3>{{ bundleInfo.name }}</h3>
              <p class="bundle-description">{{ bundleInfo.description }}</p>
              <div class="bundle-meta">
                <div class="meta-item">
                  <label>组合包价格：</label>
                  <span class="price-text">¥{{ formatMoney(bundleInfo.totalPrice) }}</span>
                </div>
                <div class="meta-item">
                  <label>包含产品数：</label>
                  <span>{{ bundleInfo.productCount || 0 }}个</span>
                </div>
                <div class="meta-item">
                  <label>有效期：</label>
                  <span>{{ bundleInfo.validityPeriod || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 组合包包含的产品列表 -->
          <div v-if="bundleProducts.length > 0" class="bundle-products">
            <h4>包含产品</h4>
            <el-table :data="bundleProducts" style="width: 100%" stripe border>
              <el-table-column prop="productName" label="产品名称" min-width="200" />
              <el-table-column prop="productType" label="类型" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.productType === 'ticket' ? 'primary' : 'success'" size="small">
                    {{ getProductTypeText(scope.row.productType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" align="center" />
              <el-table-column prop="unitPrice" label="单价" width="120" align="right">
                <template #default="scope">
                  <span class="money-text">¥{{ formatMoney(scope.row.unitPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="subtotal" label="小计" width="120" align="right">
                <template #default="scope">
                  <span class="money-text">¥{{ formatMoney(scope.row.subtotal) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 分销信息 -->
      <el-card v-if="distributionInfo" class="distribution-card">
        <template #header>
          <span>分销信息</span>
        </template>

        <div class="distribution-info">
          <div class="info-item">
            <label>分销员ID：</label>
            <span>{{ distributionInfo.distributorId }}</span>
          </div>
          <div class="info-item">
            <label>分销员编号：</label>
            <span>{{ distributionInfo.distributorCode }}</span>
          </div>
          <div class="info-item">
            <label>佣金比例：</label>
            <span>{{ distributionInfo.commissionRate }}%</span>
          </div>
          <div class="info-item">
            <label>佣金金额：</label>
            <span class="money-text commission">¥{{ formatMoney(distributionInfo.commissionAmount) }}</span>
          </div>
          <div class="info-item">
            <label>结算状态：</label>
            <el-tag :type="getDistributionStatusType(distributionInfo.status)" size="small">
              {{ getDistributionStatusText(distributionInfo.status) }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCouponById } from '@/api/coupon'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = reactive({})

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'unactivated': 'info',
    'active': 'success',
    'used': 'warning',
    'expired': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'unactivated': '未激活',
    'active': '已激活',
    'used': '已使用',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取详情数据
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getCouponById(route.params.id)
    Object.assign(detail, data)
  } catch (error) {
    ElMessage.error('获取门票详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.push('/coupon')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.coupon-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
