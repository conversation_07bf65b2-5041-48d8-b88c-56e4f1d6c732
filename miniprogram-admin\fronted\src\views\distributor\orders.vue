<template>
  <div class="distribution-orders">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回详情
      </el-button>
      <h2>分销订单管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="订单ID">
          <el-input
            v-model="searchForm.orderId"
            placeholder="请输入订单ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="结算状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待结算" value="pending" />
            <el-option label="已结算" value="settled" />
            <el-option label="已取消" value="canceled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分销订单表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分销订单列表</span>
          <div>
            <el-button type="primary" @click="handleCreateOrder">
              <el-icon><Plus /></el-icon>
              手动创建订单
            </el-button>
            <el-button 
              type="success" 
              @click="handleBatchSettle"
              :disabled="selectedOrders.length === 0"
            >
              <el-icon><Money /></el-icon>
              批量结算
            </el-button>
            <el-button type="info" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="orderList"
        style="width: 100%"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column prop="id" label="分销订单ID" width="120" align="center" />
        
        <el-table-column prop="orderId" label="原订单ID" width="120" align="center">
          <template #default="scope">
            <el-link type="primary" @click="viewOriginalOrder(scope.row.orderId)">
              {{ scope.row.orderId }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="orderAmount" label="订单金额" min-width="120" align="right">
          <template #default="scope">
            <span class="money-text">¥{{ formatMoney(scope.row.orderAmount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="commissionRate" label="佣金比例" width="100" align="center">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.commissionRate }}%</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="commissionAmount" label="佣金金额" min-width="120" align="right">
          <template #default="scope">
            <span class="money-text commission">¥{{ formatMoney(scope.row.commissionAmount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="结算状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="settledAt" label="结算时间" min-width="160">
          <template #default="scope">
            {{ scope.row.settledAt ? formatDate(scope.row.settledAt) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="success"
              size="small"
              @click="handleSettle(scope.row)"
            >
              <el-icon><Money /></el-icon>
              结算
            </el-button>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="danger"
              size="small"
              @click="handleCancel(scope.row)"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建分销订单对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="手动创建分销订单"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="原订单ID" prop="orderId">
          <el-input
            v-model.number="createForm.orderId"
            placeholder="请输入原订单ID"
            type="number"
          />
        </el-form-item>
        <el-form-item label="订单金额" prop="orderAmount">
          <el-input
            v-model.number="createForm.orderAmount"
            placeholder="请输入订单金额"
            type="number"
            :step="0.01"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="佣金比例" prop="commissionRate">
          <el-input
            v-model.number="createForm.commissionRate"
            placeholder="请输入佣金比例"
            type="number"
            :step="0.01"
          >
            <template #append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="基础比例">
          <el-input
            v-model.number="createForm.baseRate"
            placeholder="请输入基础比例（默认10%）"
            type="number"
            :step="0.01"
          >
            <template #append>%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateSubmit" :loading="createLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getDistributionOrderList,
  createDistributionOrder,
  settleDistributionOrder,
  batchSettleDistributionOrders,
  cancelDistributionOrder
} from '@/api/distributor'
import { formatDate, formatMoney } from '@/utils'

const router = useRouter()
const route = useRoute()

const distributorId = route.params.id

// 搜索表单
const searchForm = reactive({
  orderId: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const orderList = ref([])
const loading = ref(false)
const selectedOrders = ref([])

// 创建订单对话框
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const createForm = reactive({
  orderId: '',
  orderAmount: '',
  commissionRate: '',
  baseRate: 10.00
})

const createRules = {
  orderId: [
    { required: true, message: '请输入原订单ID', trigger: 'blur' },
    { type: 'number', message: '订单ID必须为数字', trigger: 'blur' }
  ],
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单金额必须大于0', trigger: 'blur' }
  ],
  commissionRate: [
    { required: true, message: '请输入佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ]
}

// 获取分销订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      distributorId: distributorId,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getDistributionOrderList(params)
    if (response.code === 200) {
      orderList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取分销订单列表失败:', error)
    ElMessage.error('获取分销订单列表失败')
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    settled: 'success',
    canceled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待结算',
    settled: '已结算',
    canceled: '已取消'
  }
  return texts[status] || '未知'
}

// 返回详情页
const goBack = () => {
  router.push(`/distributor/detail/${distributorId}`)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchOrderList()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchOrderList()
}

// 刷新
const handleRefresh = () => {
  fetchOrderList()
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchOrderList()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchOrderList()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection.filter(item => item.status === 'pending')
}

// 查看详情
const handleView = (row) => {
  // 这里可以打开订单详情对话框或跳转到详情页
  console.log('查看订单详情:', row)
}

// 查看原订单
const viewOriginalOrder = (orderId) => {
  router.push(`/order/detail/${orderId}`)
}

// 创建分销订单
const handleCreateOrder = () => {
  createDialogVisible.value = true
}

const handleCreateDialogClose = () => {
  createFormRef.value?.resetFields()
  Object.keys(createForm).forEach(key => {
    if (key !== 'baseRate') {
      createForm[key] = ''
    }
  })
}

const handleCreateSubmit = async () => {
  try {
    await createFormRef.value?.validate()
    createLoading.value = true
    
    const data = {
      distributorId: distributorId,
      orderId: createForm.orderId,
      orderAmount: createForm.orderAmount,
      commissionRate: createForm.commissionRate,
      baseRate: createForm.baseRate
    }
    
    const response = await createDistributionOrder(data)
    if (response.code === 200) {
      ElMessage.success('分销订单创建成功')
      createDialogVisible.value = false
      handleRefresh()
    }
  } catch (error) {
    console.error('创建分销订单失败:', error)
    ElMessage.error(error.response?.data?.message || '创建分销订单失败')
  } finally {
    createLoading.value = false
  }
}

// 结算订单
const handleSettle = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要结算订单 ${row.id} 吗？佣金金额：¥${formatMoney(row.commissionAmount)}`,
      '结算确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await settleDistributionOrder(row.id)
    if (response.code === 200) {
      ElMessage.success('订单结算成功')
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('结算订单失败:', error)
      ElMessage.error('结算订单失败')
    }
  }
}

// 批量结算
const handleBatchSettle = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要结算的订单')
    return
  }
  
  try {
    const totalAmount = selectedOrders.value.reduce((sum, item) => sum + parseFloat(item.commissionAmount), 0)
    
    await ElMessageBox.confirm(
      `确定要批量结算 ${selectedOrders.value.length} 个订单吗？总佣金金额：¥${formatMoney(totalAmount)}`,
      '批量结算确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const orderIds = selectedOrders.value.map(item => item.id)
    const response = await batchSettleDistributionOrders(orderIds)
    if (response.code === 200) {
      ElMessage.success(`成功结算 ${response.data} 个订单`)
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量结算失败:', error)
      ElMessage.error('批量结算失败')
    }
  }
}

// 取消订单
const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 ${row.id} 吗？`,
      '取消确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await cancelDistributionOrder(row.id)
    if (response.code === 200) {
      ElMessage.success('订单取消成功')
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

onMounted(() => {
  fetchOrderList()
})
</script>

<style lang="scss" scoped>
.distribution-orders {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .money-text {
      font-weight: 500;
      
      &.commission {
        color: #67C23A;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

@media (max-width: 768px) {
  .distribution-orders {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 10px;
        
        .el-input, .el-select {
          width: 100% !important;
        }
      }
    }
    
    .pagination-container {
      text-align: center;
      
      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>
