<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <img src="/404.svg" alt="404" />
      </div>
      <div class="error-info">
        <h1>404</h1>
        <h2>页面不存在</h2>
        <p>抱歉，您访问的页面不存在或已被删除</p>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  
  .error-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    
    .error-image {
      margin-bottom: 40px;
      
      img {
        max-width: 300px;
        width: 100%;
        height: auto;
      }
    }
    
    .error-info {
      h1 {
        font-size: 72px;
        color: #409EFF;
        margin: 0 0 20px;
        font-weight: bold;
      }
      
      h2 {
        font-size: 24px;
        color: #303133;
        margin: 0 0 16px;
      }
      
      p {
        font-size: 16px;
        color: #606266;
        margin: 0 0 40px;
        line-height: 1.6;
      }
      
      .error-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: wrap;
        
        .el-button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    .error-content {
      padding: 20px;
      
      .error-image img {
        max-width: 200px;
      }
      
      .error-info {
        h1 {
          font-size: 48px;
        }
        
        h2 {
          font-size: 20px;
        }
        
        .error-actions {
          flex-direction: column;
          align-items: center;
          
          .el-button {
            width: 200px;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
