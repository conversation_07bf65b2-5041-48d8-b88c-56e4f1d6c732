-- 分销员系统数据库表结构
-- 创建时间：2025-01-06
-- 说明：包含分销员系统的所有数据表

-- 1. 分销员表
CREATE TABLE `distributors` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `user_id` int(11) NOT NULL COMMENT '绑定的用户ID',
  `distributor_code` varchar(50) NOT NULL COMMENT '分销员编号',
  `today_activated` int(11) DEFAULT 0 COMMENT '今日已激活人数',
  `total_commission` decimal(10,2) DEFAULT 0.00 COMMENT '累积佣金总额',
  `available_commission` decimal(10,2) DEFAULT 0.00 COMMENT '可提现金额',
  `withdrawn_commission` decimal(10,2) DEFAULT 0.00 COMMENT '已提现金额',
  `unsettled_commission` decimal(10,2) DEFAULT 0.00 COMMENT '未结算金额',
  `qrcode_url` varchar(255) DEFAULT NULL COMMENT '专属二维码URL',
  `status` enum('active','inactive','frozen') NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，inactive-非活跃，frozen-冻结',
  `commission_rate` decimal(5,2) DEFAULT 10.00 COMMENT '分销佣金比例(百分比)',
  `custom_rate` tinyint(1) DEFAULT 0 COMMENT '是否使用自定义比例(0-默认比例,1-自定义比例)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_distributor_code` (`distributor_code`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员表';

-- 2. 分销提现记录表
CREATE TABLE `distribution_withdrawal_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `distributor_id` int(11) NOT NULL COMMENT '分销员ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `transaction_no` varchar(100) DEFAULT NULL COMMENT '交易流水号',
  `status` enum('pending','completed','rejected') NOT NULL DEFAULT 'pending' COMMENT '提现状态：pending-待处理，completed-已完成，rejected-已拒绝',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `applied_at` timestamp NOT NULL COMMENT '申请时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_distributor_id` (`distributor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_applied_at` (`applied_at`),
  KEY `idx_transaction_no` (`transaction_no`),
  CONSTRAINT `fk_withdrawal_distributor` FOREIGN KEY (`distributor_id`) REFERENCES `distributors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销提现记录表';

-- 3. 分销产品佣金比例表
CREATE TABLE `distribution_product_commission_rates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '佣金比例ID',
  `distributor_id` int(11) NOT NULL COMMENT '分销员ID',
  `product_id` int(11) DEFAULT NULL COMMENT '产品ID',
  `bundle_id` int(11) DEFAULT NULL COMMENT '组合包ID',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '特定产品佣金比例',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_distributor_product` (`distributor_id`, `product_id`),
  UNIQUE KEY `uk_distributor_bundle` (`distributor_id`, `bundle_id`),
  KEY `idx_distributor_id` (`distributor_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  CONSTRAINT `fk_commission_rate_distributor` FOREIGN KEY (`distributor_id`) REFERENCES `distributors` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_product_or_bundle` CHECK ((`product_id` IS NOT NULL AND `bundle_id` IS NULL) OR (`product_id` IS NULL AND `bundle_id` IS NOT NULL))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销产品佣金比例表';

-- 4. 分销订单表
CREATE TABLE `distribution_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分销订单ID',
  `distributor_id` int(11) NOT NULL COMMENT '分销员ID',
  `order_id` int(11) NOT NULL COMMENT '关联订单ID',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `status` enum('pending','settled','canceled') NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算，settled-已结算，canceled-已取消',
  `settled_at` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `base_rate` decimal(5,2) NOT NULL COMMENT '基础佣金比例',
  `applied_rate` decimal(5,2) NOT NULL COMMENT '实际应用比例',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_distributor_id` (`distributor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_settled_at` (`settled_at`),
  CONSTRAINT `fk_distribution_order_distributor` FOREIGN KEY (`distributor_id`) REFERENCES `distributors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销订单表';

-- 插入初始数据（可选）
-- 系统配置数据
INSERT INTO `distributors` (`user_id`, `distributor_code`, `status`, `commission_rate`, `custom_rate`) VALUES
(1, 'DIST00000001', 'active', 15.00, 1),
(2, 'DIST00000002', 'active', 10.00, 0);

-- 创建索引优化查询性能
-- 分销员表索引
CREATE INDEX `idx_distributors_commission` ON `distributors` (`total_commission`, `available_commission`);
CREATE INDEX `idx_distributors_today_activated` ON `distributors` (`today_activated`);

-- 分销提现记录表索引
CREATE INDEX `idx_withdrawal_amount` ON `distribution_withdrawal_records` (`amount`);
CREATE INDEX `idx_withdrawal_date_range` ON `distribution_withdrawal_records` (`applied_at`, `completed_at`);



-- 分销订单表索引
CREATE INDEX `idx_distribution_order_amount` ON `distribution_orders` (`order_amount`, `commission_amount`);
CREATE INDEX `idx_distribution_order_date_range` ON `distribution_orders` (`created_at`, `settled_at`);

-- 创建视图方便查询统计数据
-- 分销员统计视图
CREATE VIEW `v_distributor_statistics` AS
SELECT
    d.id,
    d.distributor_code,
    d.status,
    d.total_commission,
    d.available_commission,
    d.withdrawn_commission,
    d.unsettled_commission,
    COALESCE(order_stats.order_count, 0) as order_count,
    COALESCE(order_stats.pending_commission, 0) as pending_commission,
    COALESCE(order_stats.settled_commission, 0) as settled_commission,
    COALESCE(withdrawal_stats.pending_withdrawal, 0) as pending_withdrawal,
    COALESCE(withdrawal_stats.completed_withdrawal, 0) as completed_withdrawal
FROM distributors d
LEFT JOIN (
    SELECT
        distributor_id,
        COUNT(*) as order_count,
        SUM(CASE WHEN status = 'pending' THEN commission_amount ELSE 0 END) as pending_commission,
        SUM(CASE WHEN status = 'settled' THEN commission_amount ELSE 0 END) as settled_commission
    FROM distribution_orders
    GROUP BY distributor_id
) order_stats ON d.id = order_stats.distributor_id
LEFT JOIN (
    SELECT
        distributor_id,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_withdrawal,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_withdrawal
    FROM distribution_withdrawal_records
    GROUP BY distributor_id
) withdrawal_stats ON d.id = withdrawal_stats.distributor_id;

-- 系统整体统计视图
CREATE VIEW `v_system_distribution_statistics` AS
SELECT 
    (SELECT COUNT(*) FROM distributors WHERE status = 'active') as active_distributor_count,
    (SELECT COUNT(*) FROM distributors) as total_distributor_count,
    (SELECT COALESCE(SUM(total_commission), 0) FROM distributors) as total_commission_sum,
    (SELECT COALESCE(SUM(available_commission), 0) FROM distributors) as total_available_commission,
    (SELECT COALESCE(SUM(withdrawn_commission), 0) FROM distributors) as total_withdrawn_commission,
    (SELECT COUNT(*) FROM distribution_orders) as total_distribution_orders,
    (SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders WHERE status = 'settled') as total_settled_commission,
    (SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders WHERE status = 'pending') as total_pending_commission,
    (SELECT COUNT(*) FROM distribution_withdrawal_records WHERE status = 'pending') as pending_withdrawal_count,
    (SELECT COALESCE(SUM(amount), 0) FROM distribution_withdrawal_records WHERE status = 'completed') as total_withdrawal_amount;
