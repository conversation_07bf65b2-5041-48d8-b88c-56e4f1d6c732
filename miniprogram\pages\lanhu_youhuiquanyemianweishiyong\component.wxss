.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 264rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_1 {
  width: 402rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 0 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.text-wrapper_1 {
  width: 640rpx;
  height: 44rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 44rpx 0 22rpx 56rpx;
}
.text_2 {
  width: 142rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_3 {
  width: 158rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-left: 80rpx;
}
.text_4 {
  width: 180rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 80rpx;
}
.group_2 {
  position: relative;
  width: 750rpx;
  height: 1362rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.box_2 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
  margin: 30rpx 0 0 20rpx;
}
.text-wrapper_2 {
  width: 670rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0 0 30rpx;
}
.text_5 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_6 {
  width: 82rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(253,0,1,1.000000);
  font-size: 27rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 27rpx;
  margin-top: 20rpx;
}
.section_1 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 10rpx 0 0 30rpx;
}
.text-wrapper_3 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_7 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.section_2 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_8 {
  width: 144rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_4 {
  background-color: rgba(242,243,245,1.000000);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_9 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(96,96,96,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 46rpx;
}
.box_3 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
  margin: 20rpx 0 0 20rpx;
}
.text-wrapper_5 {
  width: 670rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0 0 30rpx;
}
.text_10 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_11 {
  width: 82rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(253,0,1,1.000000);
  font-size: 27rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 27rpx;
  margin-top: 20rpx;
}
.box_4 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 10rpx 0 0 30rpx;
}
.text-wrapper_6 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_12 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.box_5 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_13 {
  width: 144rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_7 {
  background-color: rgba(242,243,245,1.000000);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_14 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(96,96,96,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 46rpx;
}
.box_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
  margin: 20rpx 0 0 20rpx;
}
.text-wrapper_8 {
  width: 670rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0 0 30rpx;
}
.text_15 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_16 {
  width: 82rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(253,0,1,1.000000);
  font-size: 27rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 27rpx;
  margin-top: 20rpx;
}
.group_3 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 10rpx 0 0 30rpx;
}
.text-wrapper_9 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_17 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.group_4 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_18 {
  width: 144rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_10 {
  background-color: rgba(242,243,245,1.000000);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_19 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(96,96,96,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 46rpx;
}
.box_7 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 404rpx 0 22rpx 232rpx;
}
.image_3 {
  position: absolute;
  left: 578rpx;
  top: 58rpx;
  width: 162rpx;
  height: 128rpx;
}
.image_4 {
  position: absolute;
  left: 578rpx;
  top: 364rpx;
  width: 162rpx;
  height: 128rpx;
}
.image_5 {
  position: absolute;
  left: 578rpx;
  top: 670rpx;
  width: 162rpx;
  height: 128rpx;
}