# 城市接口集成说明

## 1. 接口规范

根据您提供的API文档，城市接口的规范如下：

### 1.1 接口地址
```
GET /api/cities
```

### 1.2 请求参数
| 参数名称 | 参数说明 | 请求类型 | 是否必须 | 数据类型 |
|---------|---------|---------|---------|---------|
| provinceId | 省份ID | query | true | integer |

### 1.3 响应格式
```json
{
  "code": 200,
  "message": "success",
  "timestamp": 1640995200000,
  "data": [
    {
      "id": 1,
      "name": "南京",
      "code": "NJ",
      "provinceId": 1,
      "sort": 1,
      "status": 1,
      "createdAt": "2024-01-01T00:00:00",
      "updatedAt": "2024-01-01T00:00:00"
    }
  ]
}
```

## 2. 代码实现

### 2.1 城市服务 (utils/cityService.js)

已经更新为使用正确的参数名 `provinceId`：

```javascript
// 根据省份ID获取城市列表
async getCitiesByProvince(provinceId) {
  const cities = await httpService.get('/api/cities', {
    provinceId: provinceId  // 使用 provinceId 参数名
  }, {
    loadingText: '加载城市中...'
  });
  
  return cities;
}
```

### 2.2 首页组件使用

```javascript
// pages/lanhu_shouye/component.js
const apiService = require('../../utils/apiService');

Component({
  methods: {
    // 加载省份相关数据
    async loadProvinceRelatedData(provinceId, provinceName) {
      try {
        // 获取城市列表
        const cities = await apiService.getCities(provinceId);
        
        // 处理城市数据，添加"全部"选项
        const processedCities = apiService.cityService.processCitiesData(cities, true);
        
        this.setData({
          cityList: processedCities,
          selectedCityId: 0
        });
        
        console.log('城市数据加载成功:', cities.length, '个城市');
      } catch (error) {
        console.error('加载城市数据失败:', error);
        apiService.handleError(error);
      }
    }
  }
});
```

## 3. 数据处理

### 3.1 添加"全部"选项

城市服务会自动在城市列表前添加"全部"选项：

```javascript
// 处理后的城市数据结构
[
  { id: 0, name: '全部', code: 'ALL', provinceId: 1 },
  { id: 1, name: '南京', code: 'NJ', provinceId: 1 },
  { id: 2, name: '苏州', code: 'SZ', provinceId: 1 }
]
```

### 3.2 城市选择处理

```javascript
// 城市选择事件
async selectCity(e) {
  const cityId = e.currentTarget.dataset.cityId;
  const cityName = e.currentTarget.dataset.cityName;
  
  this.setData({
    selectedCityId: cityId
  });
  
  // 根据选中的城市更新景区数据
  await this.updateScenicDataByCity(cityId, cityName);
}
```

## 4. 错误处理

### 4.1 网络错误处理

```javascript
try {
  const cities = await apiService.getCities(provinceId);
  // 处理成功数据
} catch (error) {
  if (error.message.includes('网络')) {
    wx.showModal({
      title: '网络错误',
      content: '请检查网络连接后重试',
      showCancel: false
    });
  } else if (error.message.includes('省份ID')) {
    wx.showToast({
      title: '省份信息错误',
      icon: 'none'
    });
  } else {
    wx.showToast({
      title: error.message || '加载城市失败',
      icon: 'none'
    });
  }
}
```

### 4.2 数据验证

```javascript
// 验证城市数据
validateCity(city) {
  if (!city) return false;
  
  const requiredFields = ['id', 'name', 'provinceId'];
  return requiredFields.every(field => 
    city.hasOwnProperty(field) && 
    city[field] !== null && 
    city[field] !== undefined
  );
}
```

## 5. 缓存机制

### 5.1 自动缓存

城市数据会自动缓存5分钟：

```javascript
// 缓存配置
constructor() {
  this.cacheKeyPrefix = 'cities_cache_';
  this.cacheTime = 5 * 60 * 1000; // 5分钟缓存
}
```

### 5.2 缓存管理

```javascript
// 清除特定省份的城市缓存
cityService.clearCache(provinceId);

// 清除所有城市缓存
cityService.clearCache();
```

## 6. 测试工具

### 6.1 使用API测试工具

```javascript
const apiTest = require('../../utils/apiTest');

// 测试城市接口
await apiTest.testCities(provinceId);

// 完整API测试
await apiTest.runFullTest();
```

### 6.2 在开发者工具中测试

在微信开发者工具的控制台中执行：

```javascript
// 获取页面实例
const page = getCurrentPages()[0];

// 测试城市接口
page.testCityApi();

// 完整API测试
page.testApi();
```

## 7. 调试信息

### 7.1 开启调试日志

城市服务会自动输出详细的调试信息：

```
从服务器获取城市数据 (省份ID: 1)
城市数据加载成功: 5 个城市
使用缓存的城市数据 (省份ID: 1)
```

### 7.2 查看网络请求

在微信开发者工具的Network面板中可以查看实际的网络请求：

```
Request URL: https://your-api-domain.com/api/cities?provinceId=1
Request Method: GET
Status Code: 200 OK
```

## 8. 常见问题

### 8.1 参数名不匹配

**问题：** 后端期望 `provinceId`，但前端发送 `province_id`
**解决：** 已更新为使用 `provinceId` 参数名

### 8.2 数据格式不匹配

**问题：** 响应数据中字段名不一致
**解决：** 检查API文档，确保字段名匹配

### 8.3 缓存问题

**问题：** 数据更新后仍显示旧数据
**解决：** 清除对应的缓存

```javascript
// 清除特定省份的城市缓存
apiService.cityService.clearCache(provinceId);
```

## 9. 性能优化

### 9.1 预加载

```javascript
// 预加载热门省份的城市数据
async preloadHotCities() {
  const hotProvinces = await apiService.getHotProvinces();
  
  // 并行预加载城市数据
  const preloadPromises = hotProvinces.map(province => 
    apiService.getCities(province.id)
  );
  
  await Promise.allSettled(preloadPromises);
}
```

### 9.2 批量请求

```javascript
// 批量加载省份相关数据
const [citiesResult, carouselsResult, scenicsResult] = await apiService.batchRequest([
  apiService.getCities(provinceId),
  apiService.getCarousels(provinceId),
  apiService.getRecommendScenics({ province_id: provinceId })
]);
```

## 10. 部署注意事项

1. **域名配置**：确保API域名已添加到小程序的服务器域名白名单
2. **HTTPS要求**：API必须使用HTTPS协议
3. **超时设置**：合理设置请求超时时间
4. **错误监控**：建立完善的错误监控和日志系统

通过以上配置和使用方法，城市接口已经完全按照您的API规范进行了集成。
