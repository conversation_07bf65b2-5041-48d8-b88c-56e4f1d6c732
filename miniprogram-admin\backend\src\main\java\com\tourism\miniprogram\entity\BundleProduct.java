package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 组合包产品关联实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bundle_products")
@ApiModel(value = "BundleProduct对象", description = "组合包产品关联信息")
public class BundleProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "组合包ID")
    @TableField("bundle_id")
    @NotNull(message = "组合包ID不能为空")
    private Integer bundleId;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    @NotNull(message = "产品ID不能为空")
    private Integer productId;

    @ApiModelProperty(value = "关联景区ID")
    @TableField("scenic_id")
    @NotNull(message = "景区ID不能为空")
    private Integer scenicId;

    @ApiModelProperty(value = "包含数量")
    @TableField("quantity")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量不能小于1")
    private Integer quantity;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
