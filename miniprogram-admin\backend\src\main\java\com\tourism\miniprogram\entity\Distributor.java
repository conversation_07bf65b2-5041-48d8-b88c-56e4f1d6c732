package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分销员实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("distributors")
@ApiModel(value = "Distributor对象", description = "分销员信息")
public class Distributor implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分销员ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "绑定的用户ID")
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "分销员编号")
    @TableField("distributor_code")
    @NotBlank(message = "分销员编号不能为空")
    private String distributorCode;

    @ApiModelProperty(value = "今日已激活人数")
    @TableField("today_activated")
    private Integer todayActivated;

    @ApiModelProperty(value = "累积佣金总额")
    @TableField("total_commission")
    @DecimalMin(value = "0.00", message = "累积佣金总额不能小于0")
    private BigDecimal totalCommission;

    @ApiModelProperty(value = "可提现金额")
    @TableField("available_commission")
    @DecimalMin(value = "0.00", message = "可提现金额不能小于0")
    private BigDecimal availableCommission;

    @ApiModelProperty(value = "已提现金额")
    @TableField("withdrawn_commission")
    @DecimalMin(value = "0.00", message = "已提现金额不能小于0")
    private BigDecimal withdrawnCommission;

    @ApiModelProperty(value = "未结算金额")
    @TableField("unsettled_commission")
    @DecimalMin(value = "0.00", message = "未结算金额不能小于0")
    private BigDecimal unsettledCommission;

    @ApiModelProperty(value = "专属二维码URL")
    @TableField("qrcode_url")
    private String qrcodeUrl;

    @ApiModelProperty(value = "状态：active-活跃，inactive-非活跃，frozen-冻结")
    @TableField("status")
    @NotBlank(message = "状态不能为空")
    private String status;

    @ApiModelProperty(value = "分销佣金比例(百分比)")
    @TableField("commission_rate")
    @DecimalMin(value = "0.00", message = "佣金比例不能小于0")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "是否使用自定义比例(0-默认比例,1-自定义比例)")
    @TableField("custom_rate")
    private Boolean customRate;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
