package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Distributor;
import com.tourism.miniprogram.service.DistributorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 分销员控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/distributor")
@Api(tags = "分销员管理")
public class DistributorController {

    @Autowired
    private DistributorService distributorService;

    /**
     * 获取分销员列表（分页查询）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param status 分销员状态
     * @param distributorCode 分销员编号
     * @return 分销员列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取分销员列表", notes = "分页查询分销员列表，支持按状态、编号筛选")
    public Result<IPage<Distributor>> getDistributorList(
            @ApiParam(value = "当前页码", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "分销员状态") @RequestParam(required = false) String status,
            @ApiParam(value = "分销员编号") @RequestParam(required = false) String distributorCode) {
        try {
            Page<Distributor> page = new Page<>(current, size);
            QueryWrapper<Distributor> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            if (StringUtils.hasText(distributorCode)) {
                queryWrapper.like("distributor_code", distributorCode);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_at");

            IPage<Distributor> distributorPage = distributorService.page(page, queryWrapper);
            return Result.success(distributorPage);
        } catch (Exception e) {
            log.error("获取分销员列表失败", e);
            return Result.error("获取分销员列表失败");
        }
    }

    /**
     * 获取分销员详情
     *
     * @param distributorId 分销员ID
     * @return 分销员信息
     */
    @GetMapping("/{distributorId}")
    @ApiOperation(value = "获取分销员详情", notes = "根据分销员ID获取详细信息")
    public Result<Distributor> getDistributorInfo(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            Distributor distributor = distributorService.getById(distributorId);
            if (distributor == null) {
                return Result.error(404, "分销员不存在");
            }
            return Result.success(distributor);
        } catch (Exception e) {
            log.error("获取分销员详情失败，distributorId: {}", distributorId, e);
            return Result.error("获取分销员详情失败");
        }
    }

    /**
     * 根据用户ID获取分销员信息
     *
     * @param userId 用户ID
     * @return 分销员信息
     */
    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID获取分销员信息", notes = "检查用户是否为分销员")
    public Result<Distributor> getDistributorByUserId(
            @ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            Distributor distributor = distributorService.getByUserId(userId);
            if (distributor == null) {
                return Result.error(404, "用户不是分销员");
            }
            return Result.success(distributor);
        } catch (Exception e) {
            log.error("根据用户ID获取分销员信息失败，userId: {}", userId, e);
            return Result.error("获取分销员信息失败");
        }
    }

    /**
     * 根据分销员编号获取分销员信息
     *
     * @param distributorCode 分销员编号
     * @return 分销员信息
     */
    @GetMapping("/code/{distributorCode}")
    @ApiOperation(value = "根据分销员编号获取信息", notes = "通过分销员编号查询分销员信息")
    public Result<Distributor> getDistributorByCode(
            @ApiParam(value = "分销员编号", required = true) @PathVariable String distributorCode) {
        try {
            Distributor distributor = distributorService.getByDistributorCode(distributorCode);
            if (distributor == null) {
                return Result.error(404, "分销员不存在");
            }
            return Result.success(distributor);
        } catch (Exception e) {
            log.error("根据分销员编号获取信息失败，distributorCode: {}", distributorCode, e);
            return Result.error("获取分销员信息失败");
        }
    }

    /**
     * 创建分销员
     *
     * @param userId 用户ID
     * @param commissionRate 佣金比例
     * @return 创建结果
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建分销员", notes = "为用户创建分销员身份")
    public Result<Distributor> createDistributor(
            @ApiParam(value = "用户ID", required = true) @RequestParam Integer userId,
            @ApiParam(value = "佣金比例") @RequestParam(required = false) BigDecimal commissionRate) {
        try {
            // 检查用户是否已是分销员
            if (distributorService.isDistributor(userId)) {
                return Result.error(400, "用户已是分销员");
            }

            Distributor distributor = distributorService.createDistributor(userId, commissionRate);
            return Result.success(distributor);
        } catch (Exception e) {
            log.error("创建分销员失败，userId: {}", userId, e);
            return Result.error("创建分销员失败: " + e.getMessage());
        }
    }

    /**
     * 更新分销员信息
     *
     * @param distributorId 分销员ID
     * @param distributor 分销员信息
     * @return 更新结果
     */
    @PutMapping("/{distributorId}")
    @ApiOperation(value = "更新分销员信息", notes = "更新分销员的基本信息")
    public Result<String> updateDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId,
            @RequestBody @Valid Distributor distributor) {
        try {
            distributor.setId(distributorId);
            boolean success = distributorService.updateById(distributor);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新分销员信息失败，distributorId: {}", distributorId, e);
            return Result.error("更新分销员信息失败");
        }
    }

    /**
     * 更新分销员状态
     *
     * @param distributorId 分销员ID
     * @param status 新状态
     * @return 更新结果
     */
    @PutMapping("/{distributorId}/status")
    @ApiOperation(value = "更新分销员状态", notes = "更新分销员的状态（active/inactive/frozen）")
    public Result<String> updateDistributorStatus(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId,
            @ApiParam(value = "新状态", required = true) @RequestParam String status) {
        try {
            boolean success = distributorService.updateStatus(distributorId, status);
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新分销员状态失败，distributorId: {}, status: {}", distributorId, status, e);
            return Result.error("更新分销员状态失败");
        }
    }

    /**
     * 冻结分销员
     *
     * @param distributorId 分销员ID
     * @param reason 冻结原因
     * @return 冻结结果
     */
    @PutMapping("/{distributorId}/freeze")
    @ApiOperation(value = "冻结分销员", notes = "冻结分销员账户")
    public Result<String> freezeDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId,
            @ApiParam(value = "冻结原因") @RequestParam(required = false) String reason) {
        try {
            boolean success = distributorService.freezeDistributor(distributorId, reason);
            if (success) {
                return Result.success("分销员冻结成功");
            } else {
                return Result.error("分销员冻结失败");
            }
        } catch (Exception e) {
            log.error("冻结分销员失败，distributorId: {}", distributorId, e);
            return Result.error("冻结分销员失败");
        }
    }

    /**
     * 解冻分销员
     *
     * @param distributorId 分销员ID
     * @return 解冻结果
     */
    @PutMapping("/{distributorId}/unfreeze")
    @ApiOperation(value = "解冻分销员", notes = "解冻分销员账户")
    public Result<String> unfreezeDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            boolean success = distributorService.unfreezeDistributor(distributorId);
            if (success) {
                return Result.success("分销员解冻成功");
            } else {
                return Result.error("分销员解冻失败");
            }
        } catch (Exception e) {
            log.error("解冻分销员失败，distributorId: {}", distributorId, e);
            return Result.error("解冻分销员失败");
        }
    }

    /**
     * 生成分销员专属二维码
     *
     * @param distributorId 分销员ID
     * @return 二维码URL
     */
    @PostMapping("/{distributorId}/qrcode")
    @ApiOperation(value = "生成专属二维码", notes = "为分销员生成专属推广二维码")
    public Result<String> generateQrCode(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            String qrCodeUrl = distributorService.generateQrCode(distributorId);
            if (qrCodeUrl != null) {
                return Result.success(qrCodeUrl);
            } else {
                return Result.error("生成二维码失败");
            }
        } catch (Exception e) {
            log.error("生成分销员专属二维码失败，distributorId: {}", distributorId, e);
            return Result.error("生成二维码失败");
        }
    }

    /**
     * 获取分销员统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "获取分销员统计信息", notes = "获取系统分销员相关统计数据")
    public Result<Object> getDistributorStatistics() {
        try {
            Integer activeCount = distributorService.getActiveDistributorCount();
            BigDecimal totalCommission = distributorService.getTotalCommissionSum();
            
            return Result.success(new Object() {
                public final Integer activeDistributorCount = activeCount;
                public final BigDecimal totalCommissionSum = totalCommission;
            });
        } catch (Exception e) {
            log.error("获取分销员统计信息失败", e);
            return Result.error("获取统计信息失败");
        }
    }

    /**
     * 检查用户是否为分销员
     *
     * @param userId 用户ID
     * @return 检查结果
     */
    @GetMapping("/check/{userId}")
    @ApiOperation(value = "检查用户是否为分销员", notes = "检查指定用户是否具有分销员身份")
    public Result<Boolean> checkIsDistributor(
            @ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            boolean isDistributor = distributorService.isDistributor(userId);
            return Result.success(isDistributor);
        } catch (Exception e) {
            log.error("检查用户是否为分销员失败，userId: {}", userId, e);
            return Result.error("检查失败");
        }
    }
}
