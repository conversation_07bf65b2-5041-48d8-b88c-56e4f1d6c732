package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.DistributionProductCommissionRate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销产品佣金比例服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface DistributionProductCommissionRateService extends IService<DistributionProductCommissionRate> {

    /**
     * 根据分销员ID获取佣金比例列表
     *
     * @param distributorId 分销员ID
     * @return 佣金比例列表
     */
    List<DistributionProductCommissionRate> getByDistributorId(Integer distributorId);

    /**
     * 根据分销员ID和产品ID获取佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 佣金比例记录
     */
    DistributionProductCommissionRate getByDistributorIdAndProductId(Integer distributorId, Integer productId);

    /**
     * 根据分销员ID和组合包ID获取佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 佣金比例记录
     */
    DistributionProductCommissionRate getByDistributorIdAndBundleId(Integer distributorId, Integer bundleId);

    /**
     * 根据产品ID获取所有分销员的佣金比例
     *
     * @param productId 产品ID
     * @return 佣金比例列表
     */
    List<DistributionProductCommissionRate> getByProductId(Integer productId);

    /**
     * 根据组合包ID获取所有分销员的佣金比例
     *
     * @param bundleId 组合包ID
     * @return 佣金比例列表
     */
    List<DistributionProductCommissionRate> getByBundleId(Integer bundleId);

    /**
     * 设置分销员产品佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @param commissionRate 佣金比例
     * @return 佣金比例记录
     */
    DistributionProductCommissionRate setProductCommissionRate(Integer distributorId, Integer productId, BigDecimal commissionRate);

    /**
     * 设置分销员组合包佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @param commissionRate 佣金比例
     * @return 佣金比例记录
     */
    DistributionProductCommissionRate setBundleCommissionRate(Integer distributorId, Integer bundleId, BigDecimal commissionRate);

    /**
     * 获取分销员产品的有效佣金比例
     * 优先使用特定产品佣金比例，如果没有则使用分销员默认比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 有效佣金比例
     */
    BigDecimal getEffectiveProductCommissionRate(Integer distributorId, Integer productId);

    /**
     * 获取分销员组合包的有效佣金比例
     * 优先使用特定组合包佣金比例，如果没有则使用分销员默认比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 有效佣金比例
     */
    BigDecimal getEffectiveBundleCommissionRate(Integer distributorId, Integer bundleId);

    /**
     * 检查分销员是否有产品特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 是否存在
     */
    boolean hasProductSpecificRate(Integer distributorId, Integer productId);

    /**
     * 检查分销员是否有组合包特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 是否存在
     */
    boolean hasBundleSpecificRate(Integer distributorId, Integer bundleId);

    /**
     * 删除分销员产品佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 是否成功
     */
    boolean removeProductCommissionRate(Integer distributorId, Integer productId);

    /**
     * 删除分销员组合包佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 是否成功
     */
    boolean removeBundleCommissionRate(Integer distributorId, Integer bundleId);
}
