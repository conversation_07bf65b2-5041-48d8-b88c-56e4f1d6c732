<view class="page">

  <!-- 景区详情内容 -->
  <view class="content-container">
    <!-- 单张图片显示 -->
    <image src="{{scenicDetail.imageList[0]}}" class="image_3" mode="aspectFill"></image>
    <view class="block_2">
      <view class="text-group_1">
        <text lines="1" class="text_2">{{scenicDetail.title || '景区名称'}}</text>
        <text lines="2" class="text_3">{{scenicDetail.subtitle || '暂无描述信息'}}</text>
      </view>
      <view class="section_2">
        <image src="../../images/lanhu_dulijingqu/FigmaDDSSlicePNGe81d6d6af67cdde8b204db0b508954d5.png" class="thumbnail_4"></image>
        <text lines="1" class="text_4">{{scenicDetail.openTime || '暂无开放时间'}}</text>
        <image src="../../images/lanhu_dulijingqu/FigmaDDSSlicePNG38663f64b6adb9bd307acb44f127994c.png" class="thumbnail_5" bindtap="onViewLocation"></image>
        <text lines="2" decode="true" class="text_5">{{scenicDetail.address || '暂无地址信息'}}</text>
      </view>
    </view>
  </view>
  <view class="block_3">
    <text lines="1" class="text_6">手机智能讲解</text>
    <view class="list_1" wx:if="{{commentaryProducts && commentaryProducts.list && commentaryProducts.list.length > 0}}">
      <block wx:for="{{commentaryProducts.list}}" wx:key="productId" wx:for-index="index">
        <view class="list-items_1-{{index}}" bindtap="onProductItemTap" data-product-id="{{item.productId}}" data-product-title="{{item.title}}">
          <view class="text-wrapper_1-{{index}}">
            <text lines="1" class="text_7-{{index}}">{{item.title}}</text>
          </view>
          <view class="group_1-{{index}}">
            <image src="../../images/lanhu_dulijingqu/FigmaDDSSlicePNG90931a5fb23a8f23ec93f02deeb12a90.png" class="thumbnail_6-{{index}}"></image>
            <text lines="1" class="text_8-{{index}}">{{item.pointCount || '0'}}个</text>
            <view class="box_1-{{index}}"></view>
            <text lines="1" class="text_9-{{index}}">{{item.duration || '0'}}</text>
            <view class="text-wrapper_2-{{index}}">
              <text lines="1" class="text_10-{{index}}">￥</text>
              <text lines="1" class="text_11-{{index}}">{{item.price || '0'}}</text>
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 无讲解产品时的提示 -->
    <view wx:else class="no-commentary-tips">
      <text class="no-data-text">暂无讲解产品</text>
    </view>
  </view>
  <!-- 评价区块 -->
  <view class="block_4" wx:if="{{reviews && reviews.list && reviews.list.length > 0}}">
    <view class="text-wrapper_3">
      <text lines="1" class="text_12">评价</text>
      <text lines="1" decode="true" class="text_13">{{reviews.total || 0}}条评论&gt;&gt;</text>
    </view>
    <block wx:for="{{reviews.list}}" wx:key="reviewId" wx:for-index="index" wx:if="{{index < 3}}">
      <view class="image-text_1">
        <image src="../../images/lanhu_dulijingqu/FigmaDDSSlicePNG517a41c5f12f8da66b021f7a784ce651.png" class="label_1"></image>
        <view class="text-group_2">
          <view class="text-wrapper_4">
            <text lines="1" class="text_14">匿名用户</text>
            <text lines="1" class="text_15">{{item.formattedTime || item.reviewTime || item.createdAt}}</text>
          </view>
          <text lines="2" class="text_16">{{item.content || '暂无评价内容'}}</text>
        </view>
      </view>
    </block>
  </view>

  <view class="block_6">
    <text lines="1" class="text_17">景点详情</text>
    <!-- 固定显示第二张图片（长图），完整渲染 -->
    <view wx:if="{{scenicDetail.imageList && scenicDetail.imageList.length > 1 && scenicDetail.imageList[1]}}">
      <image src="{{scenicDetail.imageList[1]}}" class="image_4" mode="widthFix"></image>
    </view>
    <!-- 如果没有第二张图片，显示默认内容 -->
    <view wx:else class="no-detail-images">
      <text class="no-data-text">暂无详情图片</text>
    </view>
  </view>
</view>