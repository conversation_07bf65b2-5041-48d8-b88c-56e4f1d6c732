package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.BusinessCooperation;

import java.util.List;

/**
 * 商务合作服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface BusinessCooperationService extends IService<BusinessCooperation> {

    /**
     * 根据用户ID获取合作申请列表
     *
     * @param userId 用户ID
     * @return 合作申请列表
     */
    List<BusinessCooperation> getCooperationsByUserId(Integer userId);



    /**
     * 根据关键词搜索合作申请
     *
     * @param keyword 关键词
     * @return 合作申请列表
     */
    List<BusinessCooperation> searchCooperations(String keyword);
}
