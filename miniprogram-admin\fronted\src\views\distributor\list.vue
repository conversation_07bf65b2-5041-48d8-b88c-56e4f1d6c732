<template>
  <div class="distributor-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="分销员编号">
          <el-input
            v-model="searchForm.distributorCode"
            placeholder="请输入分销员编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="冻结" value="frozen" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.activeDistributorCount || 0 }}</div>
          <div class="stat-label">活跃分销员</div>
        </div>
        <el-icon class="stat-icon active"><UserFilled /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">¥{{ formatMoney(statistics.totalCommissionSum) }}</div>
          <div class="stat-label">总佣金金额</div>
        </div>
        <el-icon class="stat-icon commission"><Money /></el-icon>
      </el-card>
    </div>

    <!-- 分销员表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分销员列表</span>
          <div>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新增分销员
            </el-button>
            <el-button type="success" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="distributorList"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column prop="distributorCode" label="分销员编号" min-width="140">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.distributorCode }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="userId" label="用户ID" width="100" align="center" />
        
        <el-table-column prop="todayActivated" label="今日激活" width="100" align="center">
          <template #default="scope">
            <el-tag type="warning" size="small">{{ scope.row.todayActivated || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="totalCommission" label="累积佣金" min-width="120" align="right">
          <template #default="scope">
            <span class="money-text">¥{{ formatMoney(scope.row.totalCommission) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="availableCommission" label="可提现金额" min-width="120" align="right">
          <template #default="scope">
            <span class="money-text available">¥{{ formatMoney(scope.row.availableCommission) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="commissionRate" label="佣金比例" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.customRate ? 'success' : 'info'" size="small">
              {{ scope.row.commissionRate }}%
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(scope.row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleAction(command, scope.row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="qrcode">生成二维码</el-dropdown-item>
                  <el-dropdown-item 
                    command="freeze" 
                    v-if="scope.row.status === 'active'"
                  >
                    冻结账户
                  </el-dropdown-item>
                  <el-dropdown-item 
                    command="unfreeze" 
                    v-if="scope.row.status === 'frozen'"
                  >
                    解冻账户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建分销员对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建分销员"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="选择用户" prop="userId">
          <div class="user-search-container">
            <el-input
              v-model="userSearchPhone"
              placeholder="请输入手机号搜索用户"
              clearable
              @input="handleUserSearch"
              @clear="handleUserSearchClear"
            >
              <template #append>
                <el-button @click="handleUserSearch" :loading="userSearchLoading">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>

            <!-- 用户搜索结果 -->
            <div v-if="userSearchResults.length > 0" class="user-search-results">
              <div class="search-results-header">搜索结果：</div>
              <div
                v-for="user in userSearchResults"
                :key="user.id"
                class="user-item"
                :class="{ 'selected': createForm.userId === user.id }"
                @click="selectUser(user)"
              >
                <div class="user-info">
                  <div class="user-name">{{ user.nickname || '未设置昵称' }}</div>
                  <div class="user-phone">{{ user.phone }}</div>
                  <div class="user-region">{{ user.province }} {{ user.city }}</div>
                </div>
                <div class="user-id">ID: {{ user.id }}</div>
              </div>
            </div>

            <!-- 已选择的用户 -->
            <div v-if="selectedUser" class="selected-user">
              <div class="selected-label">已选择用户：</div>
              <div class="user-card">
                <div class="user-info">
                  <div class="user-name">{{ selectedUser.nickname || '未设置昵称' }}</div>
                  <div class="user-phone">{{ selectedUser.phone }}</div>
                  <div class="user-region">{{ selectedUser.province }} {{ selectedUser.city }}</div>
                </div>
                <div class="user-id">ID: {{ selectedUser.id }}</div>
                <el-button type="danger" size="small" @click="clearSelectedUser">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="佣金比例" prop="commissionRate">
          <el-input
            v-model.number="createForm.commissionRate"
            placeholder="请输入佣金比例（%）"
            type="number"
            :min="0"
            :max="100"
          >
            <template #append>%</template>
          </el-input>
          <div class="form-tip">留空则使用系统默认比例（10%）</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateSubmit" :loading="createLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDistributorList,
  getDistributorStatistics,
  createDistributor,
  freezeDistributor,
  unfreezeDistributor,
  generateQrCode
} from '@/api/distributor'
import { getUserList } from '@/api/user'
import { formatDate, formatMoney, debounce } from '@/utils'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  distributorCode: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 统计信息
const statistics = ref({})

// 表格数据
const distributorList = ref([])
const loading = ref(false)

// 创建分销员对话框
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref()
const createForm = reactive({
  userId: '',
  commissionRate: ''
})

const createRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' },
    { type: 'number', message: '用户ID必须为数字', trigger: 'blur' }
  ]
}

// 获取分销员列表
const fetchDistributorList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getDistributorList(params)
    if (response.code === 200) {
      distributorList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取分销员列表失败:', error)
    ElMessage.error('获取分销员列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await getDistributorStatistics()
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 状态相关方法
const getStatusType = (status) => {
  const types = {
    active: 'success',
    inactive: 'warning',
    frozen: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    inactive: '非活跃',
    frozen: '冻结'
  }
  return texts[status] || '未知'
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchDistributorList()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchDistributorList()
}

// 刷新
const handleRefresh = () => {
  fetchDistributorList()
  fetchStatistics()
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchDistributorList()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchDistributorList()
}

// 查看详情
const handleView = (row) => {
  router.push(`/distributor/detail/${row.id}`)
}

// 编辑
const handleEdit = (row) => {
  router.push(`/distributor/edit/${row.id}`)
}

// 创建分销员
const handleCreate = () => {
  createDialogVisible.value = true
}

const handleCreateDialogClose = () => {
  createFormRef.value?.resetFields()
  Object.keys(createForm).forEach(key => {
    createForm[key] = ''
  })
}

const handleCreateSubmit = async () => {
  try {
    await createFormRef.value?.validate()
    createLoading.value = true
    
    const data = {
      userId: createForm.userId
    }
    if (createForm.commissionRate) {
      data.commissionRate = createForm.commissionRate
    }
    
    const response = await createDistributor(data)
    if (response.code === 200) {
      ElMessage.success('分销员创建成功')
      createDialogVisible.value = false
      handleRefresh()
    }
  } catch (error) {
    console.error('创建分销员失败:', error)
    ElMessage.error(error.response?.data?.message || '创建分销员失败')
  } finally {
    createLoading.value = false
  }
}

// 更多操作
const handleAction = async (command, row) => {
  switch (command) {
    case 'qrcode':
      await handleGenerateQrCode(row)
      break
    case 'freeze':
      await handleFreeze(row)
      break
    case 'unfreeze':
      await handleUnfreeze(row)
      break
  }
}

// 生成二维码
const handleGenerateQrCode = async (row) => {
  try {
    const response = await generateQrCode(row.id)
    if (response.code === 200) {
      ElMessage.success('二维码生成成功')
      // 这里可以显示二维码或复制链接
      console.log('二维码URL:', response.data)
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

// 冻结账户
const handleFreeze = async (row) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入冻结原因',
      '冻结分销员账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入冻结原因'
      }
    )
    
    const response = await freezeDistributor(row.id, reason)
    if (response.code === 200) {
      ElMessage.success('账户冻结成功')
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('冻结账户失败:', error)
      ElMessage.error('冻结账户失败')
    }
  }
}

// 解冻账户
const handleUnfreeze = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要解冻该分销员账户吗？',
      '解冻账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await unfreezeDistributor(row.id)
    if (response.code === 200) {
      ElMessage.success('账户解冻成功')
      handleRefresh()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('解冻账户失败:', error)
      ElMessage.error('解冻账户失败')
    }
  }
}

onMounted(() => {
  fetchDistributorList()
  fetchStatistics()
})
</script>

<style lang="scss" scoped>
.distributor-list {
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .statistics-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-card {
      flex: 1;
      
      :deep(.el-card__body) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
      }
      
      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
      
      .stat-icon {
        font-size: 40px;
        
        &.active {
          color: #67C23A;
        }
        
        &.commission {
          color: #E6A23C;
        }
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .money-text {
      font-weight: 500;
      
      &.available {
        color: #67C23A;
      }
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
}

@media (max-width: 768px) {
  .distributor-list {
    .statistics-cards {
      flex-direction: column;
    }
    
    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 10px;
        
        .el-input, .el-select {
          width: 100% !important;
        }
      }
    }
    
    .pagination-container {
      text-align: center;
      
      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>
