const apiService = require('../../utils/apiService');

Page({
  data: {
    scenicId: '',           // 景区ID
    scenicDetail: null,     // 景区详情数据
    commentaryProducts: {   // 讲解产品数据
      list: [],
      total: 0,
      current: 1,
      size: 20
    },
    reviews: {              // 评价数据
      list: [],
      total: 0,
      current: 1,
      size: 10
    },
    selectedProductId: null, // 选中的产品ID，用于加载评价
    loading: true,          // 加载状态
    error: false,           // 错误状态
    errorMessage: ''        // 错误信息
  },

  onLoad: function(options) {
    console.log('景区详情页面加载');
    console.log('页面参数:', options);
    this.initPage(options);
  },

  onShow: function() {
    console.log('景区详情页面显示');
  },

  onHide: function() {
    console.log('景区详情页面隐藏');
  },

  onUnload: function() {
    console.log('景区详情页面卸载');
  },
    // 初始化页面
    initPage: function(options) {
      try {
        console.log('初始化页面，参数:', options);

        const scenicId = options.scenicId;
        if (!scenicId) {
          console.error('景区ID参数缺失');
          this.setData({
            loading: false,
            error: true,
            errorMessage: '景区ID参数缺失'
          });
          wx.showToast({
            title: '参数错误',
            icon: 'none'
          });
          return;
        }

        this.setData({
          scenicId: scenicId
        });

        // 加载景区详情和讲解产品
        this.loadScenicDetail(scenicId);
        this.loadCommentaryProducts(scenicId);
      } catch (error) {
        console.error('初始化页面失败:', error);
        this.setData({
          loading: false,
          error: true,
          errorMessage: '页面初始化失败'
        });
      }
    },

    // 加载景区详情
    async loadScenicDetail(scenicId) {
      try {
        console.log('开始加载景区详情:', scenicId);

        this.setData({
          loading: true,
          error: false
        });

        const scenicDetail = await apiService.getScenicDetail(scenicId);

        console.log('景区详情加载成功:', scenicDetail);

        // 处理images字段（JSON字符串转数组）
        if (scenicDetail.images && typeof scenicDetail.images === 'string') {
          try {
            scenicDetail.imageList = JSON.parse(scenicDetail.images);
            console.log('解析图片列表成功:', scenicDetail.imageList);
          } catch (error) {
            console.error('解析图片列表失败:', error);
            scenicDetail.imageList = [];
          }
        } else if (Array.isArray(scenicDetail.images)) {
          scenicDetail.imageList = scenicDetail.images;
        } else {
          scenicDetail.imageList = [];
        }

        this.setData({
          scenicDetail: scenicDetail,
          loading: false,
          error: false
        });

      } catch (error) {
        console.error('加载景区详情失败:', error);

        this.setData({
          loading: false,
          error: true,
          errorMessage: error.message || '加载景区详情失败'
        });

        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },



    // 返回上一页
    onGoBack: function() {
      wx.navigateBack({
        delta: 1
      });
    },

    // 分享功能
    onShare: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail) {
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      }
    },

    // 收藏功能
    onFavorite: function() {
      // 这里可以实现收藏功能
      wx.showToast({
        title: '收藏功能待实现',
        icon: 'none'
      });
    },

    // 联系电话
    onCallPhone: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail && scenicDetail.phone) {
        wx.makePhoneCall({
          phoneNumber: scenicDetail.phone,
          success: () => {
            console.log('拨打电话成功');
          },
          fail: (err) => {
            console.error('拨打电话失败:', err);
            wx.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '暂无联系电话',
          icon: 'none'
        });
      }
    },

    // 查看位置
    onViewLocation: function() {
      const scenicDetail = this.data.scenicDetail;
      if (scenicDetail && scenicDetail.latitude && scenicDetail.longitude) {
        wx.openLocation({
          latitude: parseFloat(scenicDetail.latitude),
          longitude: parseFloat(scenicDetail.longitude),
          name: scenicDetail.title || '景区位置',
          address: scenicDetail.address || '',
          success: () => {
            console.log('打开位置成功');
          },
          fail: (err) => {
            console.error('打开位置失败:', err);
            wx.showToast({
              title: '打开位置失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '暂无位置信息',
          icon: 'none'
        });
      }
    },

    // 加载讲解产品
    async loadCommentaryProducts(scenicId) {
      try {
        console.log('开始加载讲解产品:', scenicId);

        const productsData = await apiService.getCommentaryProducts(scenicId, {
          current: 1,
          size: 20
        });

        console.log('讲解产品加载成功:', productsData);

        this.setData({
          commentaryProducts: productsData || { list: [], total: 0, current: 1, size: 20 }
        });

        // 如果有讲解产品，加载第一个产品的评价
        if (productsData && productsData.list && productsData.list.length > 0) {
          const firstProduct = productsData.list[0];
          console.log('第一个讲解产品:', firstProduct);
          console.log('产品ID:', firstProduct.productId);

          this.setData({
            selectedProductId: firstProduct.productId
          });

          // 确保productId存在再加载评价
          if (firstProduct.productId) {
            this.loadProductReviews(firstProduct.productId);
          } else {
            console.log('产品ID不存在，无法加载评价');
          }
        } else {
          console.log('没有讲解产品数据，无法加载评价');
        }

      } catch (error) {
        console.error('加载讲解产品失败:', error);
        // 讲解产品加载失败时不显示错误提示，使用空数据
        this.setData({
          commentaryProducts: { list: [], total: 0, current: 1, size: 20 }
        });
      }
    },

    // 加载产品评价
    async loadProductReviews(productId) {
      try {
        if (!productId) {
          console.log('产品ID为空，跳过评价加载');
          return;
        }

        console.log('开始加载产品评价，产品ID:', productId, '类型:', typeof productId);

        const reviewsData = await apiService.getProductReviews(productId, {
          current: 1,
          size: 5  // 只加载前5条评价
        });

        console.log('产品评价API返回数据:', reviewsData);
        console.log('评价列表长度:', reviewsData && reviewsData.list ? reviewsData.list.length : 0);

        // 格式化评价数据中的时间
        if (reviewsData && reviewsData.list && reviewsData.list.length > 0) {
          reviewsData.list = reviewsData.list.map(review => ({
            ...review,
            formattedTime: this.formatDate(review.reviewTime || review.createdAt)
          }));
        }

        this.setData({
          reviews: reviewsData || { list: [], total: 0, current: 1, size: 5 }
        });

        // 检查设置后的数据
        console.log('设置到页面的评价数据:', this.data.reviews);

      } catch (error) {
        console.error('加载产品评价失败:', error);
        console.error('错误详情:', error.message);
        // 评价加载失败时不显示错误提示，使用空数据
        this.setData({
          reviews: { list: [], total: 0, current: 1, size: 5 }
        });
      }
    },

    // 重新加载所有数据
    onRetryLoad: function() {
      if (this.data.scenicId) {
        this.loadScenicDetail(this.data.scenicId);
        this.loadCommentaryProducts(this.data.scenicId);
      }
    },

    // 讲解产品项点击事件
    onProductItemTap: function(e) {
      const dataset = e.currentTarget.dataset;
      const productId = dataset.productId;
      const productTitle = dataset.productTitle;

      console.log('点击讲解产品:', { productId, productTitle });

      if (!productId) {
        wx.showToast({
          title: '产品信息错误',
          icon: 'none'
        });
        return;
      }

      // 跳转到讲解产品详情页面
      wx.navigateTo({
        url: `/pages/lanhu_jingquxiangqing/component?productId=${encodeURIComponent(productId)}&title=${encodeURIComponent(productTitle || '')}`
      });
    },

    // 格式化日期，只保留年月日
    formatDate: function(dateString) {
      if (!dateString) return '';

      try {
        // 处理不同的日期格式
        let date;
        if (dateString.includes('T')) {
          // ISO格式: 2025-06-05T00:00:00
          date = new Date(dateString);
        } else if (dateString.includes('-')) {
          // 简单格式: 2025-06-05
          date = new Date(dateString);
        } else {
          return dateString; // 如果格式不识别，直接返回原字符串
        }

        if (isNaN(date.getTime())) {
          return dateString; // 如果日期无效，返回原字符串
        }

        // 格式化为 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化失败:', error);
        return dateString;
      }
    }
});
