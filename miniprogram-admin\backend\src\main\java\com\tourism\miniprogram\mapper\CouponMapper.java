package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Coupon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数字门票Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface CouponMapper extends BaseMapper<Coupon> {

    /**
     * 根据用户ID获取门票列表
     *
     * @param userId 用户ID
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByUserId(Integer userId);

    /**
     * 根据景区ID获取门票列表（通过产品关联）
     *
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE p.scenic_id = #{scenicId} " +
            "ORDER BY c.created_at DESC")
    List<Coupon> selectCouponsByScenicId(String scenicId);

    /**
     * 根据状态获取门票列表
     *
     * @param status 门票状态
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE status = #{status} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByStatus(String status);

    /**
     * 根据用户ID和景区ID获取有效门票（通过产品关联）
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND p.scenic_id = #{scenicId} " +
            "AND c.status IN ('active', 'unactivated') " +
            "ORDER BY c.created_at DESC")
    List<Coupon> selectValidCouponsByUserAndScenic(Integer userId, String scenicId);

    /**
     * 根据用户ID和景区ID查询未激活的数字门票（通过产品关联）
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 未激活的门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND p.scenic_id = #{scenicId} " +
            "AND c.status = 'unactivated'")
    List<Coupon> selectUnactivatedCouponsByUserAndScenic(@Param("userId") Integer userId,
                                                         @Param("scenicId") String scenicId);

    /**
     * 根据用户ID和产品ID查询未激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 未激活的门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} AND product_id = #{productId} AND status = 'unactivated'")
    List<Coupon> selectUnactivatedCouponsByUserAndProduct(@Param("userId") Integer userId,
                                                          @Param("productId") String productId);

    /**
     * 根据用户ID和景区ID查询已激活的数字门票（通过产品关联）
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 已激活的门票列表
     */
    @Select("SELECT c.* FROM coupons c " +
            "JOIN products p ON c.product_id = p.id " +
            "WHERE c.user_id = #{userId} " +
            "AND p.scenic_id = #{scenicId} " +
            "AND c.status = 'active'")
    List<Coupon> selectActivatedCouponsByUserAndScenic(@Param("userId") Integer userId,
                                                       @Param("scenicId") String scenicId);

    /**
     * 根据用户ID和产品ID查询已激活的数字门票
     *
     * @param userId 用户ID
     * @param productId 产品ID
     * @return 已激活的门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} AND product_id = #{productId} AND status = 'active'")
    List<Coupon> selectActivatedCouponsByUserAndProduct(@Param("userId") Integer userId,
                                                        @Param("productId") String productId);
}
