<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationAreaMapper">

    <!-- 根据产品ID获取区域列表 -->
    <select id="selectAreasByProductId" resultType="com.tourism.miniprogram.entity.ExplanationArea">
        SELECT ea.area_id, ea.area_name, ea.area_description, ea.area_image, ea.sort_order, ea.status, ea.created_at, ea.updated_at
        FROM explanation_area ea
        INNER JOIN product_area_relation par ON ea.area_id = par.area_id
        WHERE par.product_id = #{productId}
        ORDER BY par.sort_order ASC, ea.area_id DESC
    </select>

</mapper>
