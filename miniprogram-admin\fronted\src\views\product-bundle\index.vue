<template>
  <div class="product-bundle-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>组合包管理</h2>
      <el-button type="primary" @click="handleCreate">新增组合包</el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="组合包名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入组合包名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column prop="name" label="组合包名称" min-width="150" />
        <el-table-column prop="description" label="组合包长图" width="120">
          <template #default="{ row }">
            <div class="image-cell">
              <img
                v-if="row.description"
                :src="row.description"
                alt="组合包长图"
                class="bundle-image"
                @click="previewImage(row.description)"
              />
              <span v-else class="no-image">暂无图片</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="discount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ row.discount }}
          </template>
        </el-table-column>
        <el-table-column prop="scenicIds" label="包含景区" min-width="150">
          <template #default="{ row }">
            <div class="scenic-ids-cell">
              <el-tag
                v-for="(scenicId, index) in getScenicIdsArray(row.scenicIds)"
                :key="index"
                size="small"
                style="margin: 2px 4px 2px 0"
              >
                {{ scenicId }}
              </el-tag>
              <span v-if="!row.scenicIds || getScenicIdsArray(row.scenicIds).length === 0" class="no-scenics">
                暂无景区
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="组合包长图预览"
      width="60%"
      center
    >
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="组合包长图" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductBundlePage, deleteProductBundle } from '@/api/productBundle'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

const searchForm = reactive({
  name: '',
  status: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const { data } = await getProductBundlePage(params)
    console.log('组合包列表API响应:', data)
    console.log('组合包记录:', data.records)

    // 调试每个组合包的景区ID数据
    if (data.records) {
      data.records.forEach((bundle, index) => {
        console.log(`组合包${index + 1} (ID: ${bundle.id}):`, {
          name: bundle.name,
          scenicIds: bundle.scenicIds,
          scenicIdsType: typeof bundle.scenicIds,
          isArray: Array.isArray(bundle.scenicIds)
        })
      })
    }

    tableData.value = data.records
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取组合包列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: null
  })
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 处理景区ID数组显示
const getScenicIdsArray = (scenicIds) => {
  if (!scenicIds) return []

  // 如果是数组，直接返回
  if (Array.isArray(scenicIds)) {
    return scenicIds
  }

  // 如果是字符串，尝试解析为JSON
  if (typeof scenicIds === 'string') {
    try {
      const parsed = JSON.parse(scenicIds)
      return Array.isArray(parsed) ? parsed : []
    } catch (error) {
      // 如果JSON解析失败，尝试按逗号分割
      return scenicIds.split(',').filter(id => id.trim())
    }
  }

  return []
}

// 图片预览
const previewImage = (imageUrl) => {
  previewImageUrl.value = imageUrl
  imagePreviewVisible.value = true
}

// 操作
const handleCreate = () => {
  router.push('/product-bundle/create')
}

const handleEdit = (row) => {
  router.push(`/product-bundle/edit/${row.id}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除组合包"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteProductBundle(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.product-bundle-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 图片相关样式 */
.image-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}

.bundle-image {
  width: 80px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bundle-image:hover {
  transform: scale(1.05);
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.no-image {
  color: #909399;
  font-size: 12px;
  text-align: center;
  padding: 20px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
  width: 80px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片预览对话框样式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 景区ID相关样式 */
.scenic-ids-cell {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2px;
  max-width: 150px;
}

.no-scenics {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}
</style>
