package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.DistributionOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销订单Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface DistributionOrderMapper extends BaseMapper<DistributionOrder> {

    /**
     * 根据分销员ID获取分销订单列表
     *
     * @param distributorId 分销员ID
     * @return 分销订单列表
     */
    @Select("SELECT * FROM distribution_orders WHERE distributor_id = #{distributorId} ORDER BY created_at DESC")
    List<DistributionOrder> selectByDistributorId(Integer distributorId);

    /**
     * 根据订单ID获取分销订单
     *
     * @param orderId 订单ID
     * @return 分销订单
     */
    @Select("SELECT * FROM distribution_orders WHERE order_id = #{orderId}")
    DistributionOrder selectByOrderId(Integer orderId);

    /**
     * 根据状态获取分销订单列表
     *
     * @param status 结算状态
     * @return 分销订单列表
     */
    @Select("SELECT * FROM distribution_orders WHERE status = #{status} ORDER BY created_at DESC")
    List<DistributionOrder> selectByStatus(String status);

    /**
     * 根据分销员ID和状态获取分销订单列表
     *
     * @param distributorId 分销员ID
     * @param status 结算状态
     * @return 分销订单列表
     */
    @Select("SELECT * FROM distribution_orders WHERE distributor_id = #{distributorId} AND status = #{status} ORDER BY created_at DESC")
    List<DistributionOrder> selectByDistributorIdAndStatus(@Param("distributorId") Integer distributorId,
                                                          @Param("status") String status);

    /**
     * 获取分销员待结算佣金总额
     *
     * @param distributorId 分销员ID
     * @return 待结算佣金总额
     */
    @Select("SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders WHERE distributor_id = #{distributorId} AND status = 'pending'")
    BigDecimal getPendingCommissionAmount(Integer distributorId);

    /**
     * 获取分销员已结算佣金总额
     *
     * @param distributorId 分销员ID
     * @return 已结算佣金总额
     */
    @Select("SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders WHERE distributor_id = #{distributorId} AND status = 'settled'")
    BigDecimal getSettledCommissionAmount(Integer distributorId);

    /**
     * 获取分销员总佣金金额
     *
     * @param distributorId 分销员ID
     * @return 总佣金金额
     */
    @Select("SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders WHERE distributor_id = #{distributorId}")
    BigDecimal getTotalCommissionAmount(Integer distributorId);

    /**
     * 获取分销员订单数量统计
     *
     * @param distributorId 分销员ID
     * @return 订单数量
     */
    @Select("SELECT COUNT(*) FROM distribution_orders WHERE distributor_id = #{distributorId}")
    Integer countOrdersByDistributorId(Integer distributorId);

    /**
     * 获取系统总分销订单数量
     *
     * @return 总分销订单数量
     */
    @Select("SELECT COUNT(*) FROM distribution_orders")
    Integer countTotalDistributionOrders();

    /**
     * 获取系统总分销佣金金额
     *
     * @return 总分销佣金金额
     */
    @Select("SELECT COALESCE(SUM(commission_amount), 0) FROM distribution_orders")
    BigDecimal getTotalSystemCommissionAmount();

    /**
     * 检查订单是否已存在分销记录
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM distribution_orders WHERE order_id = #{orderId}")
    Boolean existsByOrderId(Integer orderId);
}
