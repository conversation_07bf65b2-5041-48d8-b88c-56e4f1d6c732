package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ActivationCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 激活码Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface ActivationCodeMapper extends BaseMapper<ActivationCode> {

    /**
     * 根据景区ID获取激活码列表
     *
     * @param scenicId 景区ID
     * @return 激活码列表
     */
    @Select("SELECT * FROM activation_codes WHERE scenic_id = #{scenicId} ORDER BY created_at DESC")
    List<ActivationCode> selectActivationCodesByScenicId(Integer scenicId);

    /**
     * 根据状态获取激活码列表
     *
     * @param status 激活码状态
     * @return 激活码列表
     */
    @Select("SELECT * FROM activation_codes WHERE status = #{status} ORDER BY created_at DESC")
    List<ActivationCode> selectActivationCodesByStatus(String status);

    /**
     * 根据激活码查找
     *
     * @param code 激活码
     * @return 激活码对象
     */
    @Select("SELECT * FROM activation_codes WHERE code = #{code}")
    ActivationCode selectByCode(String code);
}
