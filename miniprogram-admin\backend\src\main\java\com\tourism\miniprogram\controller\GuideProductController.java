package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.GuideProduct;
import com.tourism.miniprogram.service.GuideProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 讲解产品控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/guide-products")
@Api(tags = "讲解产品管理")
public class GuideProductController {

    @Autowired
    private GuideProductService guideProductService;

    /**
     * 分页获取讲解产品列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲解产品列表", notes = "分页获取讲解产品列表，支持按标题、景区ID、类型筛选")
    public Result<IPage<GuideProduct>> getGuideProductPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "产品标题") @RequestParam(required = false) String title,
            @ApiParam(value = "景区ID") @RequestParam(required = false) String scenicId,

            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<GuideProduct> page = new Page<>(current, size);
            QueryWrapper<GuideProduct> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(title)) {
                queryWrapper.like("title", title);
            }
            if (scenicId != null) {
                queryWrapper.eq("scenic_id", scenicId);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("product_id");

            IPage<GuideProduct> guideProductPage = guideProductService.page(page, queryWrapper);
            return Result.success(guideProductPage);
        } catch (Exception e) {
            log.error("分页获取讲解产品列表失败", e);
            return Result.error("获取讲解产品列表失败");
        }
    }

    /**
     * 获取讲解产品详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲解产品详情", notes = "根据ID获取讲解产品详细信息")
    public Result<GuideProduct> getGuideProductById(@ApiParam(value = "产品ID", required = true) @PathVariable Integer id) {
        try {
            GuideProduct guideProduct = guideProductService.getById(id);
            if (guideProduct == null) {
                return Result.error(404, "讲解产品不存在");
            }
            return Result.success(guideProduct);
        } catch (Exception e) {
            log.error("获取讲解产品详情失败，id: {}", id, e);
            return Result.error("获取讲解产品详情失败");
        }
    }

    /**
     * 创建讲解产品
     */
    @PostMapping
    @ApiOperation(value = "创建讲解产品", notes = "创建新的讲解产品")
    public Result<GuideProduct> createGuideProduct(@RequestBody @Valid GuideProduct guideProduct) {
        try {
            boolean success = guideProductService.save(guideProduct);
            if (success) {
                // 重新查询获取完整的讲解产品信息（包含自动生成的ID和时间戳）
                GuideProduct createdProduct = guideProductService.getById(guideProduct.getProductId());
                return Result.success(createdProduct);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲解产品失败", e);
            return Result.error("创建讲解产品失败");
        }
    }

    /**
     * 更新讲解产品
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲解产品", notes = "更新讲解产品信息")
    public Result<String> updateGuideProduct(
            @ApiParam(value = "产品ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid GuideProduct guideProduct) {
        try {
            GuideProduct existGuideProduct = guideProductService.getById(id);
            if (existGuideProduct == null) {
                return Result.error(404, "讲解产品不存在");
            }

            guideProduct.setProductId(id);
            boolean success = guideProductService.updateById(guideProduct);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲解产品失败，id: {}", id, e);
            return Result.error("更新讲解产品失败");
        }
    }

    /**
     * 删除讲解产品
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲解产品", notes = "删除讲解产品")
    public Result<String> deleteGuideProduct(@ApiParam(value = "产品ID", required = true) @PathVariable Integer id) {
        try {
            GuideProduct guideProduct = guideProductService.getById(id);
            if (guideProduct == null) {
                return Result.error(404, "讲解产品不存在");
            }

            boolean success = guideProductService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲解产品失败，id: {}", id, e);
            return Result.error("删除讲解产品失败");
        }
    }


}
