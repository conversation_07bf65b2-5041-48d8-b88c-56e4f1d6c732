#!/bin/bash

# 旅游讲解小程序文件上传API启动脚本

APP_NAME="tourism-file-api"
JAR_FILE="file-management-api-1.0.0.jar"
PID_FILE="$APP_NAME.pid"
LOG_FILE="logs/$APP_NAME.log"

# 创建日志目录
mkdir -p logs

# JVM参数
JVM_OPTS="-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08"

# Spring配置
SPRING_OPTS="--spring.profiles.active=prod"

# 环境变量（请根据实际情况修改）
export TENCENT_COS_SECRET_ID="AKIDTntlQs55c1C9sNgkJgqmTxEG3cx3uYyV"
export TENCENT_COS_SECRET_KEY="PZVIrCkBWiombGYgeS6av67zhGfuzviV"
export TENCENT_COS_REGION="ap-guangzhou"
export TENCENT_COS_BUCKET_NAME="travel-1315014578"
export TENCENT_COS_CDN_DOMAIN="https://cos.gobsweb.com"
export TENCENT_COS_URL_EXPIRE_TIME="1576800000"

# 停止现有进程
stop() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "停止应用 (PID: $PID)..."
            kill $PID
            sleep 5
            if ps -p $PID > /dev/null 2>&1; then
                echo "强制停止应用..."
                kill -9 $PID
            fi
            rm -f $PID_FILE
            echo "应用已停止"
        else
            rm -f $PID_FILE
        fi
    fi
}

# 启动应用
start() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "应用已在运行 (PID: $PID)"
            return 1
        else
            rm -f $PID_FILE
        fi
    fi
    
    echo "启动应用..."
    nohup java $JVM_OPTS -jar $JAR_FILE $SPRING_OPTS > $LOG_FILE 2>&1 &
    echo $! > $PID_FILE
    echo "应用已启动 (PID: $!)"
    
    # 等待启动
    sleep 10
    
    # 检查健康状态
    if curl -f http://localhost:8080/api/health/check > /dev/null 2>&1; then
        echo "✅ 应用启动成功"
        echo "API文档: http://your-server-ip:8080/api/doc.html"
    else
        echo "❌ 应用启动失败，请查看日志: $LOG_FILE"
    fi
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "应用正在运行 (PID: $PID)"
        else
            echo "应用未运行（PID文件存在但进程不存在）"
            rm -f $PID_FILE
        fi
    else
        echo "应用未运行"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f $LOG_FILE
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
