{"name": "tourism-admin-frontend", "version": "1.0.0", "description": "旅游讲解小程序管理后台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "element-plus": "^2.3.12", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "@vueuse/core": "^10.4.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3", "sass": "^1.66.1"}}