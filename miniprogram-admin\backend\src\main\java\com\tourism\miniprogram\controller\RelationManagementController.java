package com.tourism.miniprogram.controller;

import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.dto.*;
import com.tourism.miniprogram.entity.*;
import com.tourism.miniprogram.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 关系管理控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Slf4j
@RestController
@RequestMapping("/relations")
@Api(tags = "关系管理")
public class RelationManagementController {

    @Autowired
    private ProductAreaRelationService productAreaRelationService;

    @Autowired
    private AreaPointRelationService areaPointRelationService;

    @Autowired
    private PointAudioRelationService pointAudioRelationService;

    @Autowired
    private ExplanationAreaService explanationAreaService;

    @Autowired
    private ExplanationAudioService explanationAudioService;

    @Autowired
    private GuideProductService guideProductService;

    @Autowired
    private ExplanationPointService explanationPointService;

    // ==================== 产品-区域关系管理 ====================

    /**
     * 获取产品的区域关系列表
     */
    @GetMapping("/product-area/product/{productId}")
    @ApiOperation(value = "获取产品的区域关系列表", notes = "根据产品ID获取该产品关联的所有区域")
    public Result<List<ProductAreaRelationDTO>> getAreasByProductId(@ApiParam(value = "产品ID", required = true) @PathVariable Integer productId) {
        try {
            List<ProductAreaRelationDTO> relations = productAreaRelationService.getByProductIdWithDetails(productId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取产品区域关系失败，productId: {}", productId, e);
            return Result.error("获取产品区域关系失败");
        }
    }

    /**
     * 获取产品的区域关系详细列表
     */
    @GetMapping("/product-area/product/{productId}/details")
    @ApiOperation(value = "获取产品的区域关系详细列表", notes = "根据产品ID获取该产品关联的所有区域详细信息")
    public Result<List<ProductAreaRelationDTO>> getAreasByProductIdWithDetails(@ApiParam(value = "产品ID", required = true) @PathVariable Integer productId) {
        try {
            List<ProductAreaRelation> relations = productAreaRelationService.getByProductId(productId);
            List<ProductAreaRelationDTO> dtoList = new ArrayList<>();

            for (ProductAreaRelation relation : relations) {
                ProductAreaRelationDTO dto = new ProductAreaRelationDTO();
                dto.setRelationId(relation.getRelationId());
                dto.setProductId(relation.getProductId());
                dto.setAreaId(relation.getAreaId());
                dto.setSortOrder(relation.getSortOrder());
                dto.setCreatedAt(relation.getCreatedAt());

                // 获取产品名称
                GuideProduct product = guideProductService.getById(relation.getProductId());
                if (product != null) {
                    dto.setProductName(product.getTitle());
                }

                // 获取区域名称
                ExplanationArea area = explanationAreaService.getById(relation.getAreaId());
                if (area != null) {
                    dto.setAreaName(area.getAreaName());
                }

                dtoList.add(dto);
            }

            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("获取产品区域关系详细信息失败，productId: {}", productId, e);
            return Result.error("获取产品区域关系详细信息失败");
        }
    }

    /**
     * 获取区域的产品关系列表
     */
    @GetMapping("/product-area/area/{areaId}")
    @ApiOperation(value = "获取区域的产品关系列表", notes = "根据区域ID获取该区域关联的所有产品")
    public Result<List<ProductAreaRelation>> getProductsByAreaId(@ApiParam(value = "区域ID", required = true) @PathVariable Integer areaId) {
        try {
            List<ProductAreaRelation> relations = productAreaRelationService.getByAreaId(areaId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取区域产品关系失败，areaId: {}", areaId, e);
            return Result.error("获取区域产品关系失败");
        }
    }

    /**
     * 添加产品区域关系
     */
    @PostMapping("/product-area")
    @ApiOperation(value = "添加产品区域关系", notes = "创建产品与区域的关联关系")
    public Result<String> addProductAreaRelation(@RequestBody @Valid ProductAreaRelation relation) {
        try {
            boolean success = productAreaRelationService.addRelation(
                relation.getProductId(), 
                relation.getAreaId(), 
                relation.getSortOrder()
            );
            if (success) {
                return Result.success("添加关系成功");
            } else {
                return Result.error("添加关系失败");
            }
        } catch (Exception e) {
            log.error("添加产品区域关系失败", e);
            return Result.error("添加关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除产品区域关系
     */
    @DeleteMapping("/product-area")
    @ApiOperation(value = "删除产品区域关系", notes = "删除产品与区域的关联关系")
    public Result<String> removeProductAreaRelation(
            @ApiParam(value = "产品ID", required = true) @RequestParam Integer productId,
            @ApiParam(value = "区域ID", required = true) @RequestParam Integer areaId) {
        try {
            boolean success = productAreaRelationService.removeRelation(productId, areaId);
            if (success) {
                return Result.success("删除关系成功");
            } else {
                return Result.error("删除关系失败");
            }
        } catch (Exception e) {
            log.error("删除产品区域关系失败，productId: {}, areaId: {}", productId, areaId, e);
            return Result.error("删除关系失败");
        }
    }

    /**
     * 批量添加产品区域关系
     */
    @PostMapping("/product-area/batch")
    @ApiOperation(value = "批量添加产品区域关系", notes = "批量创建产品与区域的关联关系")
    public Result<String> batchAddProductAreaRelations(@RequestBody List<ProductAreaRelation> relations) {
        try {
            boolean success = productAreaRelationService.batchAddRelations(relations);
            if (success) {
                return Result.success("批量添加关系成功");
            } else {
                return Result.error("批量添加关系失败");
            }
        } catch (Exception e) {
            log.error("批量添加产品区域关系失败", e);
            return Result.error("批量添加关系失败");
        }
    }

    /**
     * 更新产品区域关系排序
     */
    @PutMapping("/product-area/{relationId}/sort")
    @ApiOperation(value = "更新产品区域关系排序", notes = "更新产品区域关系的排序值")
    public Result<String> updateProductAreaSortOrder(
            @ApiParam(value = "关系ID", required = true) @PathVariable Integer relationId,
            @ApiParam(value = "排序值", required = true) @RequestParam Integer sortOrder) {
        try {
            boolean success = productAreaRelationService.updateSortOrder(relationId, sortOrder);
            if (success) {
                return Result.success("更新排序成功");
            } else {
                return Result.error("更新排序失败");
            }
        } catch (Exception e) {
            log.error("更新产品区域关系排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            return Result.error("更新排序失败");
        }
    }

    /**
     * 批量更新产品区域关系排序
     */
    @PutMapping("/product-area/batch-sort")
    @ApiOperation(value = "批量更新产品区域关系排序", notes = "批量更新产品区域关系的排序值")
    public Result<String> batchUpdateProductAreaSortOrder(@RequestBody Map<String, List<Integer>> request) {
        try {
            List<Integer> relationIds = request.get("relationIds");
            List<Integer> sortOrders = request.get("sortOrders");

            boolean success = productAreaRelationService.batchUpdateSortOrder(relationIds, sortOrders);
            if (success) {
                return Result.success("批量更新排序成功");
            } else {
                return Result.error("批量更新排序失败");
            }
        } catch (Exception e) {
            log.error("批量更新产品区域关系排序失败", e);
            return Result.error("批量更新排序失败");
        }
    }

    // ==================== 区域-讲解点关系管理 ====================

    /**
     * 获取区域的讲解点关系列表
     */
    @GetMapping("/area-point/area/{areaId}")
    @ApiOperation(value = "获取区域的讲解点关系列表", notes = "根据区域ID获取该区域关联的所有讲解点")
    public Result<List<AreaPointRelationDTO>> getPointsByAreaId(@ApiParam(value = "区域ID", required = true) @PathVariable Integer areaId) {
        try {
            List<AreaPointRelationDTO> relations = areaPointRelationService.getByAreaIdWithDetails(areaId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取区域讲解点关系失败，areaId: {}", areaId, e);
            return Result.error("获取区域讲解点关系失败");
        }
    }

    /**
     * 获取区域的讲解点关系详细列表
     */
    @GetMapping("/area-point/area/{areaId}/details")
    @ApiOperation(value = "获取区域的讲解点关系详细列表", notes = "根据区域ID获取该区域关联的所有讲解点详细信息")
    public Result<List<AreaPointRelationDTO>> getPointsByAreaIdWithDetails(@ApiParam(value = "区域ID", required = true) @PathVariable Integer areaId) {
        try {
            List<AreaPointRelationDTO> relations = areaPointRelationService.getByAreaIdWithDetails(areaId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取区域讲解点关系详细信息失败，areaId: {}", areaId, e);
            return Result.error("获取区域讲解点关系详细信息失败");
        }
    }

    /**
     * 获取讲解点的区域关系列表
     */
    @GetMapping("/area-point/point/{pointId}")
    @ApiOperation(value = "获取讲解点的区域关系列表", notes = "根据讲解点ID获取该讲解点关联的所有区域")
    public Result<List<AreaPointRelation>> getAreasByPointId(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer pointId) {
        try {
            List<AreaPointRelation> relations = areaPointRelationService.getByPointId(pointId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取讲解点区域关系失败，pointId: {}", pointId, e);
            return Result.error("获取讲解点区域关系失败");
        }
    }

    /**
     * 添加区域讲解点关系
     */
    @PostMapping("/area-point")
    @ApiOperation(value = "添加区域讲解点关系", notes = "创建区域与讲解点的关联关系")
    public Result<String> addAreaPointRelation(@RequestBody @Valid AreaPointRelation relation) {
        try {
            boolean success = areaPointRelationService.addRelation(
                relation.getAreaId(),
                relation.getPointId(),
                relation.getSortOrder()
            );
            if (success) {
                return Result.success("添加关系成功");
            } else {
                return Result.error("添加关系失败");
            }
        } catch (Exception e) {
            log.error("添加区域讲解点关系失败", e);
            return Result.error("添加关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除区域讲解点关系
     */
    @DeleteMapping("/area-point")
    @ApiOperation(value = "删除区域讲解点关系", notes = "删除区域与讲解点的关联关系")
    public Result<String> removeAreaPointRelation(
            @ApiParam(value = "区域ID", required = true) @RequestParam Integer areaId,
            @ApiParam(value = "讲解点ID", required = true) @RequestParam Integer pointId) {
        try {
            boolean success = areaPointRelationService.removeRelation(areaId, pointId);
            if (success) {
                return Result.success("删除关系成功");
            } else {
                return Result.error("删除关系失败");
            }
        } catch (Exception e) {
            log.error("删除区域讲解点关系失败，areaId: {}, pointId: {}", areaId, pointId, e);
            return Result.error("删除关系失败");
        }
    }

    /**
     * 批量添加区域讲解点关系
     */
    @PostMapping("/area-point/batch")
    @ApiOperation(value = "批量添加区域讲解点关系", notes = "批量创建区域与讲解点的关联关系")
    public Result<String> batchAddAreaPointRelations(@RequestBody List<AreaPointRelation> relations) {
        try {
            boolean success = areaPointRelationService.batchAddRelations(relations);
            if (success) {
                return Result.success("批量添加关系成功");
            } else {
                return Result.error("批量添加关系失败");
            }
        } catch (Exception e) {
            log.error("批量添加区域讲解点关系失败", e);
            return Result.error("批量添加关系失败");
        }
    }

    /**
     * 更新区域讲解点关系排序
     */
    @PutMapping("/area-point/{relationId}/sort")
    @ApiOperation(value = "更新区域讲解点关系排序", notes = "更新区域讲解点关系的排序值")
    public Result<String> updateAreaPointSortOrder(
            @ApiParam(value = "关系ID", required = true) @PathVariable Integer relationId,
            @ApiParam(value = "排序值", required = true) @RequestParam Integer sortOrder) {
        try {
            boolean success = areaPointRelationService.updateSortOrder(relationId, sortOrder);
            if (success) {
                return Result.success("更新排序成功");
            } else {
                return Result.error("更新排序失败");
            }
        } catch (Exception e) {
            log.error("更新区域讲解点关系排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            return Result.error("更新排序失败");
        }
    }

    // ==================== 讲解点-音频关系管理 ====================

    /**
     * 获取讲解点的音频关系列表
     */
    @GetMapping("/point-audio/point/{pointId}")
    @ApiOperation(value = "获取讲解点的音频关系列表", notes = "根据讲解点ID获取该讲解点关联的所有音频")
    public Result<List<PointAudioRelationDTO>> getAudiosByPointId(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer pointId) {
        try {
            List<PointAudioRelationDTO> relations = pointAudioRelationService.getByPointIdWithDetails(pointId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取讲解点音频关系失败，pointId: {}", pointId, e);
            return Result.error("获取讲解点音频关系失败");
        }
    }

    /**
     * 获取讲解点的音频关系详细列表
     */
    @GetMapping("/point-audio/point/{pointId}/details")
    @ApiOperation(value = "获取讲解点的音频关系详细列表", notes = "根据讲解点ID获取该讲解点关联的所有音频详细信息")
    public Result<List<PointAudioRelationDTO>> getAudiosByPointIdWithDetails(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer pointId) {
        try {
            List<PointAudioRelationDTO> relations = pointAudioRelationService.getByPointIdWithDetails(pointId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取讲解点音频关系详细信息失败，pointId: {}", pointId, e);
            return Result.error("获取讲解点音频关系详细信息失败");
        }
    }

    /**
     * 获取音频的讲解点关系列表
     */
    @GetMapping("/point-audio/audio/{audioId}")
    @ApiOperation(value = "获取音频的讲解点关系列表", notes = "根据音频ID获取该音频关联的所有讲解点")
    public Result<List<PointAudioRelation>> getPointsByAudioId(@ApiParam(value = "音频ID", required = true) @PathVariable Integer audioId) {
        try {
            List<PointAudioRelation> relations = pointAudioRelationService.getByAudioId(audioId);
            return Result.success(relations);
        } catch (Exception e) {
            log.error("获取音频讲解点关系失败，audioId: {}", audioId, e);
            return Result.error("获取音频讲解点关系失败");
        }
    }

    /**
     * 添加讲解点音频关系
     */
    @PostMapping("/point-audio")
    @ApiOperation(value = "添加讲解点音频关系", notes = "创建讲解点与音频的关联关系")
    public Result<String> addPointAudioRelation(@RequestBody @Valid PointAudioRelation relation) {
        try {
            boolean success = pointAudioRelationService.addRelation(
                relation.getPointId(),
                relation.getAudioId(),
                relation.getSortOrder()
            );
            if (success) {
                return Result.success("添加关系成功");
            } else {
                return Result.error("添加关系失败");
            }
        } catch (Exception e) {
            log.error("添加讲解点音频关系失败", e);
            return Result.error("添加关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除讲解点音频关系
     */
    @DeleteMapping("/point-audio")
    @ApiOperation(value = "删除讲解点音频关系", notes = "删除讲解点与音频的关联关系")
    public Result<String> removePointAudioRelation(
            @ApiParam(value = "讲解点ID", required = true) @RequestParam Integer pointId,
            @ApiParam(value = "音频ID", required = true) @RequestParam Integer audioId) {
        try {
            boolean success = pointAudioRelationService.removeRelation(pointId, audioId);
            if (success) {
                return Result.success("删除关系成功");
            } else {
                return Result.error("删除关系失败");
            }
        } catch (Exception e) {
            log.error("删除讲解点音频关系失败，pointId: {}, audioId: {}", pointId, audioId, e);
            return Result.error("删除关系失败");
        }
    }

    /**
     * 批量添加讲解点音频关系
     */
    @PostMapping("/point-audio/batch")
    @ApiOperation(value = "批量添加讲解点音频关系", notes = "批量创建讲解点与音频的关联关系")
    public Result<String> batchAddPointAudioRelations(@RequestBody List<PointAudioRelation> relations) {
        try {
            boolean success = pointAudioRelationService.batchAddRelations(relations);
            if (success) {
                return Result.success("批量添加关系成功");
            } else {
                return Result.error("批量添加关系失败");
            }
        } catch (Exception e) {
            log.error("批量添加讲解点音频关系失败", e);
            return Result.error("批量添加关系失败");
        }
    }

    /**
     * 更新讲解点音频关系排序
     */
    @PutMapping("/point-audio/{relationId}/sort")
    @ApiOperation(value = "更新讲解点音频关系排序", notes = "更新讲解点音频关系的排序值")
    public Result<String> updatePointAudioSortOrder(
            @ApiParam(value = "关系ID", required = true) @PathVariable Integer relationId,
            @ApiParam(value = "排序值", required = true) @RequestParam Integer sortOrder) {
        try {
            boolean success = pointAudioRelationService.updateSortOrder(relationId, sortOrder);
            if (success) {
                return Result.success("更新排序成功");
            } else {
                return Result.error("更新排序失败");
            }
        } catch (Exception e) {
            log.error("更新讲解点音频关系排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            return Result.error("更新排序失败");
        }
    }

    // ==================== 辅助查询接口 ====================

    /**
     * 获取所有区域列表
     */
    @GetMapping("/areas")
    @ApiOperation(value = "获取所有区域列表", notes = "获取所有启用的区域列表，用于下拉选择")
    public Result<List<ExplanationArea>> getAllAreas() {
        try {
            List<ExplanationArea> areas = explanationAreaService.list();
            return Result.success(areas);
        } catch (Exception e) {
            log.error("获取区域列表失败", e);
            return Result.error("获取区域列表失败");
        }
    }

    /**
     * 获取所有音频列表
     */
    @GetMapping("/audios")
    @ApiOperation(value = "获取所有音频列表", notes = "获取所有启用的音频列表，用于下拉选择")
    public Result<List<ExplanationAudio>> getAllAudios() {
        try {
            List<ExplanationAudio> audios = explanationAudioService.list();
            return Result.success(audios);
        } catch (Exception e) {
            log.error("获取音频列表失败", e);
            return Result.error("获取音频列表失败");
        }
    }
}
