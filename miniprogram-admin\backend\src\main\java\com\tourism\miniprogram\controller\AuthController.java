package com.tourism.miniprogram.controller;

import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.dto.LoginResponse;
import com.tourism.miniprogram.dto.PhoneDecryptRequest;
import com.tourism.miniprogram.dto.PhoneDecryptResponse;
import com.tourism.miniprogram.dto.WechatLoginRequest;
import com.tourism.miniprogram.service.WechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Api(tags = "认证管理")
public class AuthController {

    @Autowired
    private WechatService wechatService;

    /**
     * 微信小程序登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @PostMapping("/wechat/login")
    @ApiOperation(value = "微信小程序登录", notes = "通过微信授权码进行登录")
    public Result<LoginResponse> wechatLogin(@RequestBody @Validated WechatLoginRequest request) {
        try {
            log.info("微信登录请求，code: {}", request.getCode());
            LoginResponse response = wechatService.login(request);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("微信登录失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 微信小程序手机号解密
     *
     * @param request 解密请求
     * @return 解密响应
     */
    @PostMapping("/wechat/decrypt-phone")
    @ApiOperation(value = "微信小程序手机号解密", notes = "解密微信小程序获取的手机号信息")
    public Result<PhoneDecryptResponse> decryptPhone(@RequestBody @Validated PhoneDecryptRequest request) {
        try {
            log.info("微信手机号解密请求，code: {}", request.getCode());
            PhoneDecryptResponse response = wechatService.decryptPhone(request);
            return Result.success("手机号解密成功", response);
        } catch (Exception e) {
            log.error("微信手机号解密失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 测试接口
     *
     * @return 测试结果
     */
    @GetMapping("/test")
    @ApiOperation(value = "测试接口", notes = "用于测试服务是否正常")
    public Result<String> test() {
        return Result.success("服务正常运行");
    }
}
