package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.ExplanationAudio;
import com.tourism.miniprogram.mapper.ExplanationAudioMapper;
import com.tourism.miniprogram.service.ExplanationAudioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲解音频服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class ExplanationAudioServiceImpl extends ServiceImpl<ExplanationAudioMapper, ExplanationAudio> implements ExplanationAudioService {

    @Override
    public List<ExplanationAudio> getEnabledAudios() {
        try {
            return baseMapper.selectList(null);
        } catch (Exception e) {
            log.error("获取启用音频列表失败", e);
            throw new RuntimeException("获取音频列表失败");
        }
    }
}
