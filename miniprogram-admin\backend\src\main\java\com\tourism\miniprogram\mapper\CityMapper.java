package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.City;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 城市Mapper接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Mapper
public interface CityMapper extends BaseMapper<City> {

    /**
     * 根据省份ID获取启用的城市列表
     *
     * @param provinceId 省份ID
     * @return 城市列表
     */
    List<City> selectEnabledCitiesByProvinceId(@Param("provinceId") Integer provinceId);
}
