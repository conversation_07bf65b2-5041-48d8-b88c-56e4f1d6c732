package com.tourism.miniprogram.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson配置类
 * 用于配置日期时间格式化
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Configuration
public class JacksonConfig {

    /**
     * 日期时间格式
     */
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 配置ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = Jackson2ObjectMapperBuilder.json()
                .modules(javaTimeModule(), customModule())
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .build();
        return mapper;
    }

    /**
     * Java时间模块配置
     */
    @Bean
    public JavaTimeModule javaTimeModule() {
        JavaTimeModule module = new JavaTimeModule();

        // 配置LocalDateTime的序列化格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));

        return module;
    }

    /**
     * 自定义模块，处理LocalDateTime反序列化
     */
    @Bean
    public SimpleModule customModule() {
        SimpleModule module = new SimpleModule();
        module.addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer());
        return module;
    }

    /**
     * 自定义LocalDateTime反序列化器
     * 支持多种日期时间格式，包括ISO 8601
     */
    public static class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateString = p.getText();

            try {
                // 尝试解析ISO 8601格式（带时区）
                if (dateString.contains("Z") || dateString.contains("+") || dateString.matches(".*[+-]\\d{2}:\\d{2}$")) {
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateString);
                    return offsetDateTime.toLocalDateTime();
                }

                // 尝试解析ISO本地日期时间格式
                if (dateString.contains("T")) {
                    return LocalDateTime.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                }

                // 尝试解析标准格式
                return LocalDateTime.parse(dateString, DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));

            } catch (Exception e) {
                throw new IOException("无法解析日期时间: " + dateString, e);
            }
        }
    }
}
