package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ProductBundle;

import java.util.List;

/**
 * 产品组合包服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface ProductBundleService extends IService<ProductBundle> {

    /**
     * 获取启用的组合包列表
     *
     * @return 组合包列表
     */
    List<ProductBundle> getEnabledBundles();
}
