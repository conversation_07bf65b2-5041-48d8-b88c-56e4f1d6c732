package com.tourism.miniprogram.service;

import com.tourism.miniprogram.dto.FileUploadResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 文件服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface FileService {

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 上传结果
     */
    FileUploadResult uploadFile(MultipartFile file);

    /**
     * 上传文件流
     *
     * @param inputStream 文件流
     * @param fileName 文件名
     * @param contentType 文件类型
     * @param fileSize 文件大小
     * @return 上传结果
     */
    FileUploadResult uploadFile(InputStream inputStream, String fileName, String contentType, long fileSize);

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    boolean deleteFile(String filePath);

    /**
     * 生成预签名下载URL
     *
     * @param filePath 文件路径
     * @param expireTime 过期时间（秒）
     * @return 预签名URL
     */
    String generatePresignedUrl(String filePath, long expireTime);

    /**
     * 生成预签名下载URL（使用默认过期时间）
     *
     * @param filePath 文件路径
     * @return 预签名URL
     */
    String generatePresignedUrl(String filePath);

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    FileUploadResult getFileInfo(String filePath);
}
