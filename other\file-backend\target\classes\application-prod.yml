server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: tourism-file-management-api
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 腾讯云COS配置（生产环境）
tencent:
  cos:
    secret-id: ${TENCENT_COS_SECRET_ID:YOUR_SECRET_ID}
    secret-key: ${TENCENT_COS_SECRET_KEY:YOUR_SECRET_KEY}
    region: ${TENCENT_COS_REGION:ap-beijing}
    bucket-name: ${TENCENT_COS_BUCKET_NAME:your-bucket-name}
    cdn-domain: ${TENCENT_COS_CDN_DOMAIN:}
    url-expire-time: ${TENCENT_COS_URL_EXPIRE_TIME:3600}

# Swagger配置（生产环境建议关闭）
springfox:
  documentation:
    swagger-ui:
      enabled: false

# Knife4j配置（生产环境建议关闭）
knife4j:
  enable: false
  production: true

# 日志配置（生产环境）
logging:
  level:
    root: INFO
    com.tourism: INFO
    org.springframework.web: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/tourism-file-api.log
    max-size: 100MB
    max-history: 30

# 文件上传配置
file:
  upload:
    image-types: ${FILE_UPLOAD_IMAGE_TYPES:jpg,jpeg,png,gif,bmp,webp}
    video-types: ${FILE_UPLOAD_VIDEO_TYPES:mp4,avi,mov,wmv,flv,3gp,mkv}
    audio-types: ${FILE_UPLOAD_AUDIO_TYPES:mp3,wav,flac,aac,ogg,wma,m4a}
    max-size: ${FILE_UPLOAD_MAX_SIZE:104857600}
    path-prefix: ${FILE_UPLOAD_PATH_PREFIX:tourism/files/}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
