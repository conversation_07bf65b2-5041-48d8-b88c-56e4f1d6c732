package com.tourism.service;

import com.tourism.config.FileUploadConfig;
import com.tourism.dto.FileUploadResponseDTO;
import com.tourism.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件管理服务类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
public class FileManagementService {

    @Autowired
    private CosService cosService;

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件上传响应
     * @throws IOException IO异常
     */
    public FileUploadResponseDTO uploadFile(MultipartFile file) throws IOException {
        // 1. 验证文件
        validateFile(file);

        // 2. 生成存储文件名
        String storedFileName = FileUtils.generateStoredFileName(file.getOriginalFilename());

        // 3. 获取文件类型
        String fileType = FileUtils.getFileType(file.getOriginalFilename());

        // 4. 生成文件存储路径
        String filePath = FileUtils.generateFilePath(
                fileUploadConfig.getPathPrefix(),
                fileType,
                storedFileName
        );

        log.info("开始上传文件: {} -> {}", file.getOriginalFilename(), filePath);

        // 5. 上传到COS
        FileUploadResponseDTO response = cosService.uploadFile(file, filePath);

        log.info("文件上传成功: {}", response.getFileId());

        return response;
    }



    /**
     * 验证文件
     *
     * @param file 文件
     */
    private void validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (FileUtils.isEmpty(file)) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 检查文件大小
        if (FileUtils.isFileSizeExceeded(file, fileUploadConfig.getMaxSize())) {
            throw new IllegalArgumentException(
                    String.format("文件大小超出限制，最大允许 %s，当前文件 %s",
                            FileUtils.formatFileSize(fileUploadConfig.getMaxSize()),
                            FileUtils.formatFileSize(file.getSize())
                    )
            );
        }

        // 检查文件格式
        String fileExtension = FileUtils.getFileExtension(file.getOriginalFilename());
        if (!fileUploadConfig.isAllowedFileType(fileExtension)) {
            throw new IllegalArgumentException(
                    String.format("不支持的文件格式: %s，支持的格式: 图片(%s), 视频(%s), 音频(%s)",
                            fileExtension,
                            fileUploadConfig.getImageTypes(),
                            fileUploadConfig.getVideoTypes(),
                            fileUploadConfig.getAudioTypes()
                    )
            );
        }

        log.info("文件验证通过: {} ({})",
                file.getOriginalFilename(),
                FileUtils.formatFileSize(file.getSize())
        );
    }
}
