package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信小程序手机号解密请求
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@ApiModel(description = "微信小程序手机号解密请求")
public class PhoneDecryptRequest {

    @ApiModelProperty(value = "微信授权码", required = true, example = "081234567890abcdef")
    @NotBlank(message = "授权码不能为空")
    private String code;

    @ApiModelProperty(value = "加密数据", required = true)
    @NotBlank(message = "加密数据不能为空")
    private String encryptedData;

    @ApiModelProperty(value = "初始向量", required = true)
    @NotBlank(message = "初始向量不能为空")
    private String iv;
}
