.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.scenic-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.scenic-item {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.scenic-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.scenic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.scenic-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.scenic-desc {
  font-size: 28rpx;
  color: #666;
}
