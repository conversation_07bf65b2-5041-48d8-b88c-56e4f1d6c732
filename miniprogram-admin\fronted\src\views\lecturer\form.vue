<template>
  <div class="app-container">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 800px"
      >
        <el-form-item label="讲师姓名" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入讲师姓名"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="头像" prop="avatarUrl">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="handleAvatarUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.avatarUrl" :src="form.avatarUrl" class="avatar" />
            <div v-else-if="uploadLoading" class="avatar-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="头衔" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入头衔"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="专长领域" prop="expertise">
          <el-input
            v-model="form.expertise"
            placeholder="请输入专长领域，多个用逗号分隔"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲师简介" prop="intro">
          <el-input
            v-model="form.intro"
            type="textarea"
            :rows="4"
            placeholder="请输入讲师简介"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { getLecturerById, createLecturer, updateLecturer } from '@/api/lecturer'
import { uploadImage } from '@/api/upload'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const uploadLoading = ref(false)

const form = reactive({
  name: '',
  avatarUrl: '',
  title: '',
  intro: '',
  expertise: '',
  status: 1,
  sort: 0
})

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入讲师姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  avatarUrl: [
    { required: true, message: '请上传讲师头像', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入头衔', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  intro: [
    { required: true, message: '请输入讲师简介', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传
const handleAvatarUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '头像上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const imageUrl = await uploadImage(file, (progress) => {
      loadingInstance.setText(`头像上传中... ${progress}%`)
    })

    loadingInstance.close()

    // uploadImage 方法直接返回 URL 字符串
    form.avatarUrl = imageUrl
    ElMessage.success('头像上传成功')
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error(error.message || '头像上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 获取详情
const fetchDetail = async () => {
  try {
    const { data } = await getLecturerById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    console.error('获取讲师详情失败:', error)
    ElMessage.error('获取讲师详情失败')
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    if (isEdit.value) {
      await updateLecturer(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createLecturer(form)
      ElMessage.success('创建成功')
    }
    
    goBack()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 返回
const goBack = () => {
  router.push('/lecturer')
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.form-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar-uploader-loading {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.avatar-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
