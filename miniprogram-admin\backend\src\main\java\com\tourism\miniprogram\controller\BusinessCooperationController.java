package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.BusinessCooperation;
import com.tourism.miniprogram.service.BusinessCooperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 商务合作控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/business-cooperation")
@Api(tags = "商务合作管理")
public class BusinessCooperationController {

    @Autowired
    private BusinessCooperationService businessCooperationService;

    /**
     * 分页获取合作申请列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取合作申请列表", notes = "分页获取合作申请列表，支持按姓名筛选")
    public Result<IPage<BusinessCooperation>> getCooperationPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "姓名") @RequestParam(required = false) String name,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId) {
        try {
            Page<BusinessCooperation> page = new Page<>(current, size);
            QueryWrapper<BusinessCooperation> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            queryWrapper.orderByDesc("id");

            IPage<BusinessCooperation> cooperationPage = businessCooperationService.page(page, queryWrapper);
            return Result.success(cooperationPage);
        } catch (Exception e) {
            log.error("分页获取合作申请列表失败", e);
            return Result.error("获取合作申请列表失败");
        }
    }

    /**
     * 获取合作申请列表
     */
    @GetMapping
    @ApiOperation(value = "获取合作申请列表", notes = "获取所有合作申请列表")
    public Result<List<BusinessCooperation>> getCooperations() {
        try {
            QueryWrapper<BusinessCooperation> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<BusinessCooperation> cooperations = businessCooperationService.list(queryWrapper);
            return Result.success(cooperations);
        } catch (Exception e) {
            log.error("获取合作申请列表失败", e);
            return Result.error("获取合作申请列表失败");
        }
    }

    /**
     * 获取合作申请详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取合作申请详情", notes = "根据ID获取合作申请详情")
    public Result<BusinessCooperation> getCooperationById(@ApiParam(value = "合作申请ID", required = true) @PathVariable Integer id) {
        try {
            BusinessCooperation cooperation = businessCooperationService.getById(id);
            if (cooperation == null) {
                return Result.error(404, "合作申请不存在");
            }
            return Result.success(cooperation);
        } catch (Exception e) {
            log.error("获取合作申请详情失败，id: {}", id, e);
            return Result.error("获取合作申请详情失败");
        }
    }

    /**
     * 创建合作申请
     */
    @PostMapping
    @ApiOperation(value = "创建合作申请", notes = "创建新的合作申请")
    public Result<BusinessCooperation> createCooperation(@RequestBody @Valid BusinessCooperation cooperation) {
        try {
            boolean success = businessCooperationService.save(cooperation);
            if (success) {
                BusinessCooperation createdCooperation = businessCooperationService.getById(cooperation.getId());
                return Result.success(createdCooperation);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建合作申请失败", e);
            return Result.error("创建合作申请失败");
        }
    }

    /**
     * 更新合作申请
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新合作申请", notes = "根据ID更新合作申请信息")
    public Result<String> updateCooperation(
            @ApiParam(value = "合作申请ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid BusinessCooperation cooperation) {
        try {
            cooperation.setId(id);
            boolean success = businessCooperationService.updateById(cooperation);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新合作申请失败，id: {}", id, e);
            return Result.error("更新合作申请失败");
        }
    }

    /**
     * 删除合作申请
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除合作申请", notes = "根据ID删除合作申请")
    public Result<String> deleteCooperation(@ApiParam(value = "合作申请ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = businessCooperationService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除合作申请失败，id: {}", id, e);
            return Result.error("删除合作申请失败");
        }
    }



    /**
     * 搜索合作申请
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索合作申请", notes = "根据关键词搜索合作申请")
    public Result<List<BusinessCooperation>> searchCooperations(
            @ApiParam(value = "搜索关键词", required = true) @RequestParam String keyword) {
        try {
            List<BusinessCooperation> cooperations = businessCooperationService.searchCooperations(keyword);
            return Result.success(cooperations);
        } catch (Exception e) {
            log.error("搜索合作申请失败，keyword: {}", keyword, e);
            return Result.error("搜索合作申请失败");
        }
    }


}
