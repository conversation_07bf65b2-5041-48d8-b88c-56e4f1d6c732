package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.DistributionProductCommissionRate;
import com.tourism.miniprogram.service.DistributionProductCommissionRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销产品佣金比例控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/distribution/commission-rate")
@Api(tags = "分销产品佣金比例管理")
public class DistributionProductCommissionRateController {

    @Autowired
    private DistributionProductCommissionRateService commissionRateService;

    /**
     * 获取佣金比例列表（分页查询）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @param bundleId 组合包ID
     * @return 佣金比例列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取佣金比例列表", notes = "分页查询佣金比例，支持按分销员、产品、组合包筛选")
    public Result<IPage<DistributionProductCommissionRate>> getCommissionRateList(
            @ApiParam(value = "当前页码", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "分销员ID") @RequestParam(required = false) Integer distributorId,
            @ApiParam(value = "产品ID") @RequestParam(required = false) Integer productId,
            @ApiParam(value = "组合包ID") @RequestParam(required = false) Integer bundleId) {
        try {
            Page<DistributionProductCommissionRate> page = new Page<>(current, size);
            QueryWrapper<DistributionProductCommissionRate> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (distributorId != null) {
                queryWrapper.eq("distributor_id", distributorId);
            }
            if (productId != null) {
                queryWrapper.eq("product_id", productId);
            }
            if (bundleId != null) {
                queryWrapper.eq("bundle_id", bundleId);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_at");

            IPage<DistributionProductCommissionRate> ratePage = commissionRateService.page(page, queryWrapper);
            return Result.success(ratePage);
        } catch (Exception e) {
            log.error("获取佣金比例列表失败", e);
            return Result.error("获取佣金比例列表失败");
        }
    }

    /**
     * 获取佣金比例详情
     *
     * @param rateId 佣金比例ID
     * @return 佣金比例信息
     */
    @GetMapping("/{rateId}")
    @ApiOperation(value = "获取佣金比例详情", notes = "根据ID获取佣金比例详细信息")
    public Result<DistributionProductCommissionRate> getCommissionRateInfo(
            @ApiParam(value = "佣金比例ID", required = true) @PathVariable Integer rateId) {
        try {
            DistributionProductCommissionRate rate = commissionRateService.getById(rateId);
            if (rate == null) {
                return Result.error(404, "佣金比例记录不存在");
            }
            return Result.success(rate);
        } catch (Exception e) {
            log.error("获取佣金比例详情失败，rateId: {}", rateId, e);
            return Result.error("获取佣金比例详情失败");
        }
    }

    /**
     * 根据分销员ID获取佣金比例列表
     *
     * @param distributorId 分销员ID
     * @return 佣金比例列表
     */
    @GetMapping("/distributor/{distributorId}")
    @ApiOperation(value = "获取分销员佣金比例", notes = "获取指定分销员的所有佣金比例设置")
    public Result<List<DistributionProductCommissionRate>> getCommissionRatesByDistributor(
            @ApiParam(value = "分销员ID", required = true) @PathVariable Integer distributorId) {
        try {
            List<DistributionProductCommissionRate> rates = commissionRateService.getByDistributorId(distributorId);
            return Result.success(rates);
        } catch (Exception e) {
            log.error("获取分销员佣金比例失败，distributorId: {}", distributorId, e);
            return Result.error("获取佣金比例失败");
        }
    }

    /**
     * 根据产品ID获取佣金比例列表
     *
     * @param productId 产品ID
     * @return 佣金比例列表
     */
    @GetMapping("/product/{productId}")
    @ApiOperation(value = "获取产品佣金比例", notes = "获取指定产品的所有分销员佣金比例")
    public Result<List<DistributionProductCommissionRate>> getCommissionRatesByProduct(
            @ApiParam(value = "产品ID", required = true) @PathVariable Integer productId) {
        try {
            List<DistributionProductCommissionRate> rates = commissionRateService.getByProductId(productId);
            return Result.success(rates);
        } catch (Exception e) {
            log.error("获取产品佣金比例失败，productId: {}", productId, e);
            return Result.error("获取佣金比例失败");
        }
    }

    /**
     * 根据组合包ID获取佣金比例列表
     *
     * @param bundleId 组合包ID
     * @return 佣金比例列表
     */
    @GetMapping("/bundle/{bundleId}")
    @ApiOperation(value = "获取组合包佣金比例", notes = "获取指定组合包的所有分销员佣金比例")
    public Result<List<DistributionProductCommissionRate>> getCommissionRatesByBundle(
            @ApiParam(value = "组合包ID", required = true) @PathVariable Integer bundleId) {
        try {
            List<DistributionProductCommissionRate> rates = commissionRateService.getByBundleId(bundleId);
            return Result.success(rates);
        } catch (Exception e) {
            log.error("获取组合包佣金比例失败，bundleId: {}", bundleId, e);
            return Result.error("获取佣金比例失败");
        }
    }

    /**
     * 设置分销员产品佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @param commissionRate 佣金比例
     * @return 设置结果
     */
    @PostMapping("/product")
    @ApiOperation(value = "设置产品佣金比例", notes = "为分销员设置特定产品的佣金比例")
    public Result<DistributionProductCommissionRate> setProductCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "产品ID", required = true) @RequestParam Integer productId,
            @ApiParam(value = "佣金比例", required = true) @RequestParam BigDecimal commissionRate) {
        try {
            DistributionProductCommissionRate rate = commissionRateService.setProductCommissionRate(
                    distributorId, productId, commissionRate);
            return Result.success(rate);
        } catch (Exception e) {
            log.error("设置产品佣金比例失败，distributorId: {}, productId: {}, commissionRate: {}", 
                    distributorId, productId, commissionRate, e);
            return Result.error("设置产品佣金比例失败: " + e.getMessage());
        }
    }

    /**
     * 设置分销员组合包佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @param commissionRate 佣金比例
     * @return 设置结果
     */
    @PostMapping("/bundle")
    @ApiOperation(value = "设置组合包佣金比例", notes = "为分销员设置特定组合包的佣金比例")
    public Result<DistributionProductCommissionRate> setBundleCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "组合包ID", required = true) @RequestParam Integer bundleId,
            @ApiParam(value = "佣金比例", required = true) @RequestParam BigDecimal commissionRate) {
        try {
            DistributionProductCommissionRate rate = commissionRateService.setBundleCommissionRate(
                    distributorId, bundleId, commissionRate);
            return Result.success(rate);
        } catch (Exception e) {
            log.error("设置组合包佣金比例失败，distributorId: {}, bundleId: {}, commissionRate: {}", 
                    distributorId, bundleId, commissionRate, e);
            return Result.error("设置组合包佣金比例失败: " + e.getMessage());
        }
    }

    /**
     * 获取分销员产品的有效佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 有效佣金比例
     */
    @GetMapping("/effective/product")
    @ApiOperation(value = "获取产品有效佣金比例", notes = "获取分销员对指定产品的有效佣金比例")
    public Result<BigDecimal> getEffectiveProductCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "产品ID", required = true) @RequestParam Integer productId) {
        try {
            BigDecimal rate = commissionRateService.getEffectiveProductCommissionRate(distributorId, productId);
            return Result.success(rate);
        } catch (Exception e) {
            log.error("获取产品有效佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            return Result.error("获取有效佣金比例失败");
        }
    }

    /**
     * 获取分销员组合包的有效佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 有效佣金比例
     */
    @GetMapping("/effective/bundle")
    @ApiOperation(value = "获取组合包有效佣金比例", notes = "获取分销员对指定组合包的有效佣金比例")
    public Result<BigDecimal> getEffectiveBundleCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "组合包ID", required = true) @RequestParam Integer bundleId) {
        try {
            BigDecimal rate = commissionRateService.getEffectiveBundleCommissionRate(distributorId, bundleId);
            return Result.success(rate);
        } catch (Exception e) {
            log.error("获取组合包有效佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            return Result.error("获取有效佣金比例失败");
        }
    }

    /**
     * 删除分销员产品佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 删除结果
     */
    @DeleteMapping("/product")
    @ApiOperation(value = "删除产品佣金比例", notes = "删除分销员的特定产品佣金比例设置")
    public Result<String> removeProductCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "产品ID", required = true) @RequestParam Integer productId) {
        try {
            boolean success = commissionRateService.removeProductCommissionRate(distributorId, productId);
            if (success) {
                return Result.success("产品佣金比例删除成功");
            } else {
                return Result.error("产品佣金比例删除失败或不存在");
            }
        } catch (Exception e) {
            log.error("删除产品佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            return Result.error("删除产品佣金比例失败");
        }
    }

    /**
     * 删除分销员组合包佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 删除结果
     */
    @DeleteMapping("/bundle")
    @ApiOperation(value = "删除组合包佣金比例", notes = "删除分销员的特定组合包佣金比例设置")
    public Result<String> removeBundleCommissionRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "组合包ID", required = true) @RequestParam Integer bundleId) {
        try {
            boolean success = commissionRateService.removeBundleCommissionRate(distributorId, bundleId);
            if (success) {
                return Result.success("组合包佣金比例删除成功");
            } else {
                return Result.error("组合包佣金比例删除失败或不存在");
            }
        } catch (Exception e) {
            log.error("删除组合包佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            return Result.error("删除组合包佣金比例失败");
        }
    }

    /**
     * 检查分销员是否有产品特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param productId 产品ID
     * @return 检查结果
     */
    @GetMapping("/check/product")
    @ApiOperation(value = "检查产品特定佣金比例", notes = "检查分销员是否设置了产品特定佣金比例")
    public Result<Boolean> hasProductSpecificRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "产品ID", required = true) @RequestParam Integer productId) {
        try {
            boolean hasSpecificRate = commissionRateService.hasProductSpecificRate(distributorId, productId);
            return Result.success(hasSpecificRate);
        } catch (Exception e) {
            log.error("检查产品特定佣金比例失败，distributorId: {}, productId: {}", distributorId, productId, e);
            return Result.error("检查失败");
        }
    }

    /**
     * 检查分销员是否有组合包特定佣金比例
     *
     * @param distributorId 分销员ID
     * @param bundleId 组合包ID
     * @return 检查结果
     */
    @GetMapping("/check/bundle")
    @ApiOperation(value = "检查组合包特定佣金比例", notes = "检查分销员是否设置了组合包特定佣金比例")
    public Result<Boolean> hasBundleSpecificRate(
            @ApiParam(value = "分销员ID", required = true) @RequestParam Integer distributorId,
            @ApiParam(value = "组合包ID", required = true) @RequestParam Integer bundleId) {
        try {
            boolean hasSpecificRate = commissionRateService.hasBundleSpecificRate(distributorId, bundleId);
            return Result.success(hasSpecificRate);
        } catch (Exception e) {
            log.error("检查组合包特定佣金比例失败，distributorId: {}, bundleId: {}", distributorId, bundleId, e);
            return Result.error("检查失败");
        }
    }
}
