package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.dto.ProductAreaRelationDTO;
import com.tourism.miniprogram.entity.ProductAreaRelation;
import com.tourism.miniprogram.mapper.ProductAreaRelationMapper;
import com.tourism.miniprogram.service.ProductAreaRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 产品区域关系服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Slf4j
@Service
public class ProductAreaRelationServiceImpl extends ServiceImpl<ProductAreaRelationMapper, ProductAreaRelation> implements ProductAreaRelationService {

    @Override
    public List<ProductAreaRelation> getByProductId(Integer productId) {
        try {
            if (productId == null) {
                throw new IllegalArgumentException("产品ID不能为空");
            }
            return baseMapper.selectByProductId(productId);
        } catch (Exception e) {
            log.error("根据产品ID获取区域关系列表失败，productId: {}", productId, e);
            throw new RuntimeException("获取区域关系列表失败");
        }
    }

    @Override
    public List<ProductAreaRelationDTO> getByProductIdWithDetails(Integer productId) {
        try {
            if (productId == null) {
                throw new IllegalArgumentException("产品ID不能为空");
            }
            return baseMapper.selectByProductIdWithDetails(productId);
        } catch (Exception e) {
            log.error("根据产品ID获取区域关系详细列表失败，productId: {}", productId, e);
            throw new RuntimeException("获取区域关系详细列表失败");
        }
    }

    @Override
    public List<ProductAreaRelation> getByAreaId(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.selectByAreaId(areaId);
        } catch (Exception e) {
            log.error("根据区域ID获取产品关系列表失败，areaId: {}", areaId, e);
            throw new RuntimeException("获取产品关系列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRelation(Integer productId, Integer areaId, Integer sortOrder) {
        try {
            if (productId == null || areaId == null) {
                throw new IllegalArgumentException("产品ID和区域ID不能为空");
            }

            // 检查关系是否已存在
            QueryWrapper<ProductAreaRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", productId).eq("area_id", areaId);
            if (baseMapper.selectCount(queryWrapper) > 0) {
                throw new RuntimeException("该关系已存在");
            }

            ProductAreaRelation relation = new ProductAreaRelation();
            relation.setProductId(productId);
            relation.setAreaId(areaId);
            relation.setSortOrder(sortOrder != null ? sortOrder : 0);

            return baseMapper.insert(relation) > 0;
        } catch (Exception e) {
            log.error("添加产品区域关系失败，productId: {}, areaId: {}", productId, areaId, e);
            throw new RuntimeException("添加关系失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRelation(Integer productId, Integer areaId) {
        try {
            if (productId == null || areaId == null) {
                throw new IllegalArgumentException("产品ID和区域ID不能为空");
            }

            QueryWrapper<ProductAreaRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", productId).eq("area_id", areaId);
            return baseMapper.delete(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("删除产品区域关系失败，productId: {}, areaId: {}", productId, areaId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddRelations(List<ProductAreaRelation> relations) {
        try {
            if (relations == null || relations.isEmpty()) {
                throw new IllegalArgumentException("关系列表不能为空");
            }
            return baseMapper.batchInsert(relations) > 0;
        } catch (Exception e) {
            log.error("批量添加产品区域关系失败", e);
            throw new RuntimeException("批量添加关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByProductId(Integer productId) {
        try {
            if (productId == null) {
                throw new IllegalArgumentException("产品ID不能为空");
            }
            return baseMapper.deleteByProductId(productId) >= 0;
        } catch (Exception e) {
            log.error("根据产品ID删除关系失败，productId: {}", productId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByAreaId(Integer areaId) {
        try {
            if (areaId == null) {
                throw new IllegalArgumentException("区域ID不能为空");
            }
            return baseMapper.deleteByAreaId(areaId) >= 0;
        } catch (Exception e) {
            log.error("根据区域ID删除关系失败，areaId: {}", areaId, e);
            throw new RuntimeException("删除关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Integer relationId, Integer sortOrder) {
        try {
            if (relationId == null || sortOrder == null) {
                throw new IllegalArgumentException("关系ID和排序值不能为空");
            }
            return baseMapper.updateSortOrder(relationId, sortOrder) > 0;
        } catch (Exception e) {
            log.error("更新排序失败，relationId: {}, sortOrder: {}", relationId, sortOrder, e);
            throw new RuntimeException("更新排序失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders) {
        try {
            if (relationIds == null || sortOrders == null || relationIds.size() != sortOrders.size()) {
                throw new IllegalArgumentException("关系ID列表和排序值列表不能为空且长度必须一致");
            }

            for (int i = 0; i < relationIds.size(); i++) {
                baseMapper.updateSortOrder(relationIds.get(i), sortOrders.get(i));
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新排序失败", e);
            throw new RuntimeException("批量更新排序失败");
        }
    }
}
