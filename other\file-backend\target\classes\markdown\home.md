# 旅游讲解小程序文件上传API

欢迎使用旅游讲解小程序文件上传API系统！

## 🎯 系统简介

本系统基于Java 1.8和Spring Boot开发，集成腾讯云COS存储服务，为旅游讲解小程序提供简洁高效的文件上传解决方案。

## 📋 主要功能

- ✅ **文件上传**: 支持图片和视频文件上传
- ✅ **预览URL**: 返回文件预览URL，支持直接访问
- ✅ **CDN加速**: 支持腾讯云CDN加速访问
- ✅ **格式验证**: 严格的文件格式和大小验证

## 🔧 支持的文件格式

### 图片格式
- jpg, jpeg, png, gif, bmp, webp

### 视频格式
- mp4, avi, mov, wmv, flv, 3gp, mkv

### 文件大小限制
- 最大支持: **1000MB**

## 🚀 快速开始

### 文件上传测试

使用 **文件上传测试接口** 进行API测试：

1. 点击左侧菜单 `文件上传测试接口`
2. 选择 `文件上传测试` 接口
3. 点击 `试一试` 按钮
4. 选择要上传的文件
5. 点击 `执行` 按钮

### 接口说明

系统提供以下接口：

- `POST /test/upload` - 文件上传测试接口
- `GET /test/supported-formats` - 获取支持的文件格式

## 📖 响应格式

### 文件上传响应

```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "a1b2c3d4e5f6",
    "originalFileName": "景区图片.jpg",
    "fileType": "image",
    "fileSize": 1024000,
    "downloadUrl": "https://example.cos.ap-beijing.myqcloud.com/tourism/files/image/20231201_123456_abc123.jpg",
    "previewUrl": "https://cdn.example.com/tourism/files/image/20231201_123456_abc123.jpg?response-content-disposition=inline",
    "cdnUrl": "https://cdn.example.com/tourism/files/image/20231201_123456_abc123.jpg",
    "uploadTime": "2023-12-01 12:34:56",
    "urlExpireTime": 3600
  }
}
```

**字段说明：**
- `downloadUrl`: 使用 `Content-Disposition: attachment` 强制触发文件下载
- `previewUrl`: 使用 `Content-Disposition: inline` 在浏览器中直接显示文件内容
- `cdnUrl`: CDN加速访问地址，提供最佳性能

### 错误响应格式

```json
{
  "code": 400,
  "message": "不支持的文件格式: txt，支持的格式: jpg,jpeg,png,gif,bmp,webp, mp4,avi,mov,wmv,flv,3gp,mkv",
  "data": null,
  "timestamp": 1640995200000
}
```

## ⚠️ 注意事项

1. **文件格式**: 请确保上传的文件格式在支持列表中
2. **文件大小**: 单个文件不能超过1000MB
3. **预览URL**: 优先使用CDN域名，提供更好的访问体验
4. **URL有效期**: 下载URL默认有效期为1小时
5. **网络环境**: 建议在稳定的网络环境下进行文件上传

## 🔗 相关链接

- **项目仓库**: [GitHub](https://github.com/tourism/file-management-api)
- **技术支持**: <EMAIL>
- **系统监控**: [健康检查](/api/health/check)

## 📞 联系我们

如有问题或建议，请联系开发团队：

- **邮箱**: <EMAIL>
- **电话**: ************

---

*最后更新时间: 2023-12-01*
