// 轮播图服务模块
const httpService = require('./httpService');

class CarouselService {
  constructor() {
    this.cacheKeyPrefix = 'carousels_cache_';
    this.cacheTime = 3 * 60 * 1000; // 3分钟缓存
  }

  // 获取轮播图数据
  async getCarousels(provinceId = null, type = 'home') {
    try {
      // 构建缓存key
      const cacheKey = `${this.cacheKeyPrefix}${type}_${provinceId || 'all'}`;

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的轮播图数据');
        return cachedData;
      }

      console.log(`从服务器获取轮播图数据 (省份ID: ${provinceId}, 类型: ${type})`);
      const params = { type };
      if (provinceId) {
        params.provinceId = provinceId; // 修改参数名为 provinceId
      }

      const carousels = await httpService.get('/api/carousels', params, {
        showLoading: false // 轮播图加载不显示loading
      });

      // 处理返回的数据结构
      let carouselData = [];
      if (carousels && carousels.data && Array.isArray(carousels.data)) {
        carouselData = carousels.data;
      } else if (Array.isArray(carousels)) {
        carouselData = carousels;
      }

      // 缓存数据
      this.setCache(cacheKey, carouselData);

      console.log('轮播图数据获取成功:', carouselData.length, '张图片');
      return carouselData;
    } catch (error) {
      console.error('获取轮播图失败:', error);
      // 轮播图失败时不显示错误提示，返回空数组
      return [];
    }
  }

  // 获取首页轮播图
  async getHomeCarousels(provinceId = null) {
    return this.getCarousels(provinceId, 'home');
  }

  // 根据省份获取轮播图
  async getCarouselsByProvince(provinceId, type = 'home') {
    try {
      if (!provinceId) {
        console.warn('省份ID为空，获取全部轮播图');
        return this.getCarousels(null, type);
      }

      console.log(`获取省份轮播图 (省份ID: ${provinceId}, 类型: ${type})`);
      const carousels = await this.getCarousels(provinceId, type);

      // 格式化数据用于显示
      const formattedCarousels = this.formatCarouselsForDisplay(carousels);

      return formattedCarousels;
    } catch (error) {
      console.error('获取省份轮播图失败:', error);
      return [];
    }
  }

  // 获取首页背景轮播图
  async getShouyeCarousels() {
    try {
      // 构建缓存key
      const cacheKey = `${this.cacheKeyPrefix}shouye_background`;

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的首页轮播图数据');
        return cachedData;
      }

      console.log('从服务器获取首页轮播图数据');
      const response = await httpService.get('/api/shouye-carousels', {}, {
        showLoading: false // 背景图加载不显示loading
      });

      // 处理返回的数据结构
      let carouselData = [];
      if (response && response.data && Array.isArray(response.data)) {
        carouselData = response.data.map(item => ({
          id: item.carouselId,
          imageUrl: item.imageUrl,
          image: item.imageUrl // 兼容现有字段名
        }));
      } else if (Array.isArray(response)) {
        carouselData = response.map(item => ({
          id: item.carouselId,
          imageUrl: item.imageUrl,
          image: item.imageUrl
        }));
      }

      // 缓存数据
      this.setCache(cacheKey, carouselData);

      console.log('首页轮播图数据获取成功:', carouselData.length, '张图片');
      return carouselData;
    } catch (error) {
      console.error('获取首页轮播图失败:', error);
      // 返回默认背景图片
      return [{
        id: 0,
        imageUrl: '/images/lanhu_shouye/FigmaDDSSlicePNG6878c0806cc44a4dc399352e0e756437.png',
        image: '/images/lanhu_shouye/FigmaDDSSlicePNG6878c0806cc44a4dc399352e0e756437.png'
      }];
    }
  }

  // 获取景区详情页轮播图
  async getScenicCarousels(scenicId) {
    try {
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      const carousels = await httpService.get('/api/carousels/scenic', {
        scenic_id: scenicId
      }, {
        showLoading: false
      });

      return carousels;
    } catch (error) {
      console.error('获取景区轮播图失败:', error);
      return [];
    }
  }

  // 获取轮播图详情
  async getCarouselDetail(carouselId) {
    try {
      if (!carouselId) {
        throw new Error('轮播图ID不能为空');
      }

      const detail = await httpService.get(`/api/carousels/${carouselId}`, {}, {
        loadingText: '加载详情中...'
      });

      return detail;
    } catch (error) {
      console.error('获取轮播图详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 记录轮播图点击
  async recordCarouselClick(carouselId) {
    try {
      if (!carouselId) {
        return;
      }

      await httpService.post('/api/carousels/click', {
        carousel_id: carouselId,
        timestamp: Date.now()
      }, {
        showLoading: false
      });

      console.log('轮播图点击记录成功');
    } catch (error) {
      console.error('记录轮播图点击失败:', error);
      // 点击记录失败不影响用户体验，不显示错误
    }
  }

  // 缓存相关方法
  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取轮播图缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置轮播图缓存失败:', error);
    }
  }

  clearCache(pattern = null) {
    try {
      const storage = wx.getStorageInfoSync();
      storage.keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          if (!pattern || key.includes(pattern)) {
            wx.removeStorageSync(key);
          }
        }
      });
      console.log('轮播图缓存已清除');
    } catch (error) {
      console.error('清除轮播图缓存失败:', error);
    }
  }

  // 验证轮播图数据
  validateCarousel(carousel) {
    if (!carousel) {
      return false;
    }

    const requiredFields = ['id', 'title', 'image'];
    return requiredFields.every(field => carousel.hasOwnProperty(field) && carousel[field]);
  }

  // 格式化轮播图数据用于显示
  formatCarouselForDisplay(carousel) {
    if (!this.validateCarousel(carousel)) {
      return null;
    }

    return {
      id: carousel.id,
      title: carousel.title,
      subtitle: carousel.subtitle || '',
      image: carousel.image,
      scenicId: carousel.scenicId || carousel.scenic_id || null, // 兼容新旧字段名
      provinceId: carousel.provinceId || carousel.province_id || null, // 兼容新旧字段名
      type: carousel.type || 'home',
      sort: carousel.sort || 0,
      status: carousel.status || 1,
      createdAt: carousel.createdAt || null,
      updatedAt: carousel.updatedAt || null
    };
  }

  // 批量格式化轮播图数据
  formatCarouselsForDisplay(carousels) {
    if (!Array.isArray(carousels)) {
      return [];
    }

    return carousels
      .map(carousel => this.formatCarouselForDisplay(carousel))
      .filter(carousel => carousel !== null)
      .sort((a, b) => (a.sort || 0) - (b.sort || 0));
  }

  // 处理轮播图点击事件
  async handleCarouselClick(carousel, navigationCallback) {
    try {
      if (!carousel) {
        return;
      }

      // 记录点击
      await this.recordCarouselClick(carousel.id);

      // 如果有关联的景区ID，跳转到景区详情页
      if (carousel.scenicId) {
        if (typeof navigationCallback === 'function') {
          navigationCallback(carousel);
        } else {
          // 默认跳转逻辑
          wx.navigateTo({
            url: `/pages/lanhu_jingquxiangqing/component?scenicId=${carousel.scenicId}&title=${encodeURIComponent(carousel.title)}`,
            success: () => {
              console.log('跳转到景区详情页面:', carousel);
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      } else {
        // 如果没有关联景区，显示轮播图信息
        wx.showToast({
          title: carousel.title || '查看详情',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('处理轮播图点击失败:', error);
    }
  }

  // 预加载轮播图图片
  preloadCarouselImages(carousels) {
    if (!Array.isArray(carousels)) {
      return;
    }

    carousels.forEach(carousel => {
      if (carousel.image) {
        wx.getImageInfo({
          src: carousel.image,
          success: () => {
            console.log('轮播图预加载成功:', carousel.image);
          },
          fail: (err) => {
            console.warn('轮播图预加载失败:', carousel.image, err);
          }
        });
      }
    });
  }

  // 获取轮播图的默认配置
  getDefaultCarouselConfig() {
    return {
      autoplay: true,
      interval: 3000,
      duration: 500,
      circular: true,
      indicatorDots: true,
      indicatorColor: 'rgba(255, 255, 255, 0.5)',
      indicatorActiveColor: '#ffffff'
    };
  }
}

// 创建单例实例
const carouselService = new CarouselService();

module.exports = carouselService;
