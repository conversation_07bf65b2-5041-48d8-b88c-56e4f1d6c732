package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传结果DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "文件上传结果")
public class FileUploadResult {

    @ApiModelProperty(value = "文件名", example = "image_20250106_123456.jpg")
    private String fileName;

    @ApiModelProperty(value = "原始文件名", example = "photo.jpg")
    private String originalFileName;

    @ApiModelProperty(value = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型", example = "image/jpeg")
    private String contentType;

    @ApiModelProperty(value = "文件扩展名", example = "jpg")
    private String fileExtension;

    @ApiModelProperty(value = "文件访问URL", example = "https://your-bucket.cos.ap-guangzhou.myqcloud.com/tourism/files/image_20250106_123456.jpg")
    private String fileUrl;

    @ApiModelProperty(value = "CDN访问URL", example = "https://your-cdn-domain.com/tourism/files/image_20250106_123456.jpg")
    private String cdnUrl;

    @ApiModelProperty(value = "文件存储路径", example = "tourism/files/image_20250106_123456.jpg")
    private String filePath;

    @ApiModelProperty(value = "上传时间戳", example = "1641456789000")
    private Long uploadTime;
}
