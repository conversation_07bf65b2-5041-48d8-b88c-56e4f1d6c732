import request from '@/utils/request'

/**
 * 根据省份获取城市列表
 * @param {number} provinceId 省份ID
 */
export function getCityList(provinceId) {
  return request({
    url: '/cities',
    method: 'get',
    params: { provinceId }
  })
}

/**
 * 分页获取城市列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.name 城市名称
 * @param {number} params.provinceId 省份ID
 */
export function getCityPage(params) {
  return request({
    url: '/cities/page',
    method: 'get',
    params
  })
}

/**
 * 获取城市详情
 * @param {number} id 城市ID
 */
export function getCityInfo(id) {
  return request({
    url: `/cities/${id}`,
    method: 'get'
  })
}

/**
 * 创建城市
 * @param {Object} data 城市数据
 */
export function createCity(data) {
  return request({
    url: '/cities',
    method: 'post',
    data
  })
}

/**
 * 更新城市
 * @param {number} id 城市ID
 * @param {Object} data 城市数据
 */
export function updateCity(id, data) {
  return request({
    url: `/cities/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除城市
 * @param {number} id 城市ID
 */
export function deleteCity(id) {
  return request({
    url: `/cities/${id}`,
    method: 'delete'
  })
}
