package com.tourism.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.github.xiaoymin.knife4j.spring.extension.OpenApiExtensionResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Knife4j专门配置类，优化文件上传显示
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@EnableOpenApi
@EnableKnife4j
public class Knife4jConfig {

    @Autowired(required = false)
    private OpenApiExtensionResolver openApiExtensionResolver;

    /**
     * 创建文件上传测试API文档
     *
     * @return Docket实例
     */
    @Bean
    public Docket fileUploadTestApi() {
        String groupName = "文件上传测试接口";
        Docket docket = new Docket(DocumentationType.OAS_30)
                .apiInfo(fileUploadTestApiInfo())
                .groupName(groupName)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tourism.controller"))
                .paths(PathSelectors.regex("/test/.*"))
                .build()
                .enable(true);

        if (openApiExtensionResolver != null) {
            docket.extensions(openApiExtensionResolver.buildExtensions(groupName));
        }

        return docket;
    }

    /**
     * 创建健康检查API文档
     *
     * @return Docket实例
     */
    @Bean
    public Docket healthApi() {
        String groupName = "系统监控接口";
        Docket docket = new Docket(DocumentationType.OAS_30)
                .apiInfo(healthApiInfo())
                .groupName(groupName)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tourism.controller"))
                .paths(PathSelectors.regex("/health/.*"))
                .build()
                .enable(true);

        if (openApiExtensionResolver != null) {
            docket.extensions(openApiExtensionResolver.buildExtensions(groupName));
        }

        return docket;
    }

    /**
     * 文件上传测试API信息
     *
     * @return ApiInfo实例
     */
    private ApiInfo fileUploadTestApiInfo() {
        return new ApiInfoBuilder()
                .title("文件上传测试API")
                .description("专门用于API文档测试的文件上传接口，支持图片和视频文件上传，返回预览URL")
                .version("1.0.0")
                .contact(new Contact("Tourism Team", "https://tourism.com", "<EMAIL>"))
                .build();
    }

    /**
     * 健康检查API信息
     *
     * @return ApiInfo实例
     */
    private ApiInfo healthApiInfo() {
        return new ApiInfoBuilder()
                .title("系统监控API")
                .description("提供系统健康检查和状态监控功能")
                .version("1.0.0")
                .contact(new Contact("Tourism Team", "https://tourism.com", "<EMAIL>"))
                .build();
    }
}
