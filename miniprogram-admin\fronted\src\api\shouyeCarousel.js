import request from '@/utils/request'

/**
 * 获取首页轮播图列表
 */
export function getShouyeCarouselList() {
  return request({
    url: '/shouye-carousels',
    method: 'get'
  })
}

/**
 * 分页获取首页轮播图列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 */
export function getShouyeCarouselPage(params) {
  return request({
    url: '/shouye-carousels/page',
    method: 'get',
    params
  })
}

/**
 * 获取首页轮播图详情
 * @param {number} id 首页轮播图ID
 */
export function getShouyeCarouselInfo(id) {
  return request({
    url: `/shouye-carousels/${id}`,
    method: 'get'
  })
}

/**
 * 创建首页轮播图
 * @param {Object} data 首页轮播图数据
 */
export function createShouyeCarousel(data) {
  return request({
    url: '/shouye-carousels',
    method: 'post',
    data
  })
}

/**
 * 更新首页轮播图
 * @param {number} id 首页轮播图ID
 * @param {Object} data 首页轮播图数据
 */
export function updateShouyeCarousel(id, data) {
  return request({
    url: `/shouye-carousels/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除首页轮播图
 * @param {number} id 首页轮播图ID
 */
export function deleteShouyeCarousel(id) {
  return request({
    url: `/shouye-carousels/${id}`,
    method: 'delete'
  })
}
