import request from '@/utils/request'

// 获取产品列表
export function getProductList() {
  return request({
    url: '/products',
    method: 'get'
  })
}

// 分页获取产品列表
export function getProductPage(params) {
  return request({
    url: '/products/page',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductById(id) {
  return request({
    url: `/products/${id}`,
    method: 'get'
  })
}

// 根据景区ID获取产品列表
export function getProductsByScenicId(scenicId) {
  return request({
    url: `/products/scenic/${scenicId}`,
    method: 'get'
  })
}

// 根据产品类型获取产品列表
export function getProductsByType(productType) {
  return request({
    url: `/products/type/${productType}`,
    method: 'get'
  })
}

// 创建产品
export function createProduct(data) {
  return request({
    url: '/products',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProduct(id, data) {
  return request({
    url: `/products/${id}`,
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}`,
    method: 'delete'
  })
}
