package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 区域讲解点关系DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "AreaPointRelationDTO对象", description = "区域讲解点关系详细信息")
public class AreaPointRelationDTO {

    @ApiModelProperty(value = "关系ID")
    private Integer relationId;

    @ApiModelProperty(value = "区域ID")
    private Integer areaId;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "讲解点ID")
    private Integer pointId;

    @ApiModelProperty(value = "讲解点名称")
    private String pointName;

    @ApiModelProperty(value = "讲解点图片URL")
    private String pointImage;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
}
