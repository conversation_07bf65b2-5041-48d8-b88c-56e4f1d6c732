package com.tourism.miniprogram.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tourism.miniprogram.service.WechatMiniProgramService;
import com.tourism.miniprogram.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 微信小程序服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class WechatMiniProgramServiceImpl implements WechatMiniProgramService {

    @Value("${wechat.miniapp.app-id:}")
    private String appId;

    @Value("${wechat.miniapp.secret:}")
    private String appSecret;

    @Autowired
    private FileService fileService;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 缓存access_token，避免频繁请求
    private final Map<String, Object> tokenCache = new ConcurrentHashMap<>();
    private static final String TOKEN_CACHE_KEY = "access_token";
    private static final String TOKEN_EXPIRE_KEY = "expire_time";

    @Override
    public String getAccessToken() {
        // 检查缓存中的token是否有效
        String cachedToken = (String) tokenCache.get(TOKEN_CACHE_KEY);
        Long expireTime = (Long) tokenCache.get(TOKEN_EXPIRE_KEY);
        
        if (cachedToken != null && expireTime != null && System.currentTimeMillis() < expireTime) {
            return cachedToken;
        }

        try {
            String url = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appId, appSecret
            );

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                
                if (jsonNode.has("access_token")) {
                    String accessToken = jsonNode.get("access_token").asText();
                    int expiresIn = jsonNode.get("expires_in").asInt();
                    
                    // 缓存token，提前5分钟过期
                    long expireTimeMillis = System.currentTimeMillis() + (expiresIn - 300) * 1000L;
                    tokenCache.put(TOKEN_CACHE_KEY, accessToken);
                    tokenCache.put(TOKEN_EXPIRE_KEY, expireTimeMillis);
                    
                    log.info("获取微信access_token成功，有效期: {}秒", expiresIn);
                    return accessToken;
                } else {
                    log.error("获取微信access_token失败: {}", response.getBody());
                    throw new RuntimeException("获取微信access_token失败: " + jsonNode.get("errmsg").asText());
                }
            }
        } catch (Exception e) {
            log.error("获取微信access_token异常", e);
            throw new RuntimeException("获取微信access_token异常", e);
        }
        
        throw new RuntimeException("获取微信access_token失败");
    }

    @Override
    public byte[] generateMiniProgramCode(String scene, String page, Integer width) {
        String accessToken = getAccessToken();
        
        try {
            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("scene", scene);
            params.put("page", page != null ? page : "pages/lanhu_shouye/component");
            params.put("width", width != null ? width : 430);
            params.put("auto_color", false);
            params.put("line_color", Map.of("r", 0, "g", 0, "b", 0));
            params.put("is_hyaline", false);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            
            ResponseEntity<byte[]> response = restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, byte[].class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                byte[] imageBytes = response.getBody();
                
                // 检查返回的是否是图片数据（微信API错误时会返回JSON）
                if (imageBytes != null && imageBytes.length > 0) {
                    // 简单检查是否是图片格式（PNG/JPEG的文件头）
                    if (isImageData(imageBytes)) {
                        log.info("生成小程序码成功，场景值: {}, 页面: {}", scene, page);
                        return imageBytes;
                    } else {
                        // 可能是错误信息，尝试解析
                        String errorMsg = new String(imageBytes);
                        log.error("生成小程序码失败: {}", errorMsg);
                        throw new RuntimeException("生成小程序码失败: " + errorMsg);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成小程序码异常，场景值: {}, 页面: {}", scene, page, e);
            throw new RuntimeException("生成小程序码异常", e);
        }
        
        throw new RuntimeException("生成小程序码失败");
    }

    @Override
    public String uploadMiniProgramCodeToCloud(byte[] imageBytes, String fileName) {
        try {
            // 将字节数组转换为MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(
                fileName,
                fileName,
                "image/png",
                imageBytes
            );

            // 使用现有的文件上传服务
            String imageUrl = fileService.uploadImage(multipartFile);

            log.info("小程序码上传成功: {}", imageUrl);
            return imageUrl;

        } catch (Exception e) {
            log.error("上传小程序码失败", e);
            throw new RuntimeException("上传小程序码失败", e);
        }
    }

    /**
     * 检查字节数组是否是图片数据
     */
    private boolean isImageData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        // 检查PNG文件头
        if (data[0] == (byte) 0x89 && data[1] == 0x50 && data[2] == 0x4E && data[3] == 0x47) {
            return true;
        }
        
        // 检查JPEG文件头
        if (data[0] == (byte) 0xFF && data[1] == (byte) 0xD8) {
            return true;
        }
        
        return false;
    }
}
