package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Province;
import com.tourism.miniprogram.service.ProvinceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 省份控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/provinces")
@Api(tags = "省份管理")
public class ProvinceController {

    @Autowired
    private ProvinceService provinceService;

    /**
     * 获取省份列表
     *
     * @return 省份列表
     */
    @GetMapping
    @ApiOperation(value = "获取省份列表", notes = "获取所有启用的省份列表")
    public Result<List<Province>> getProvinces() {
        try {
            List<Province> provinces = provinceService.getEnabledProvinces();
            return Result.success(provinces);
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return Result.error("获取省份列表失败");
        }
    }

    /**
     * 分页获取省份列表
     *
     * @param current 当前页
     * @param size    每页大小
     * @param name    省份名称（模糊查询）
     * @return 省份分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取省份列表", notes = "分页获取省份列表，支持按名称模糊搜索")
    public Result<IPage<Province>> getProvincePage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "省份名称") @RequestParam(required = false) String name) {
        try {
            Page<Province> page = new Page<>(current, size);
            QueryWrapper<Province> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            queryWrapper.orderByAsc("sort").orderByAsc("id");

            IPage<Province> provincePage = provinceService.page(page, queryWrapper);
            return Result.success(provincePage);
        } catch (Exception e) {
            log.error("分页获取省份列表失败", e);
            return Result.error("获取省份列表失败");
        }
    }

    /**
     * 获取省份详情
     *
     * @param id 省份ID
     * @return 省份详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取省份详情", notes = "根据ID获取省份详细信息")
    public Result<Province> getProvinceById(@ApiParam(value = "省份ID", required = true) @PathVariable Integer id) {
        try {
            Province province = provinceService.getById(id);
            if (province == null) {
                return Result.error(404, "省份不存在");
            }
            return Result.success(province);
        } catch (Exception e) {
            log.error("获取省份详情失败，id: {}", id, e);
            return Result.error("获取省份详情失败");
        }
    }

    /**
     * 创建省份
     *
     * @param province 省份信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建省份", notes = "创建新的省份")
    public Result<Province> createProvince(@RequestBody @Valid Province province) {
        try {
            // 检查省份代码是否已存在
            QueryWrapper<Province> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", province.getCode());
            Province existProvince = provinceService.getOne(queryWrapper);
            if (existProvince != null) {
                return Result.error("省份代码已存在");
            }

            boolean success = provinceService.save(province);
            if (success) {
                // 重新查询获取完整的省份信息（包含自动生成的ID和时间戳）
                Province createdProvince = provinceService.getById(province.getId());
                return Result.success(createdProvince);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建省份失败", e);
            return Result.error("创建省份失败");
        }
    }

    /**
     * 更新省份
     *
     * @param id       省份ID
     * @param province 省份信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新省份", notes = "更新省份信息")
    public Result<String> updateProvince(
            @ApiParam(value = "省份ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Province province) {
        try {
            Province existProvince = provinceService.getById(id);
            if (existProvince == null) {
                return Result.error(404, "省份不存在");
            }

            // 检查省份代码是否已被其他省份使用
            QueryWrapper<Province> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", province.getCode()).ne("id", id);
            Province duplicateProvince = provinceService.getOne(queryWrapper);
            if (duplicateProvince != null) {
                return Result.error("省份代码已被其他省份使用");
            }

            province.setId(id);
            boolean success = provinceService.updateById(province);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新省份失败，id: {}", id, e);
            return Result.error("更新省份失败");
        }
    }

    /**
     * 删除省份
     *
     * @param id 省份ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除省份", notes = "删除省份（软删除）")
    public Result<String> deleteProvince(@ApiParam(value = "省份ID", required = true) @PathVariable Integer id) {
        try {
            Province province = provinceService.getById(id);
            if (province == null) {
                return Result.error(404, "省份不存在");
            }

            // 这里可以添加检查是否有关联的城市
            // 如果有关联数据，可以选择禁止删除或级联删除

            boolean success = provinceService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除省份失败，id: {}", id, e);
            return Result.error("删除省份失败");
        }
    }
}
