package com.tourism.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.schema.ScalarType;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.OperationBuilderPlugin;
import springfox.documentation.spi.service.contexts.OperationContext;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Swagger文件上传配置类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
public class SwaggerFileUploadConfig implements OperationBuilderPlugin {

    /**
     * 配置文件上传解析器
     *
     * @return MultipartResolver
     */
    @Bean
    public MultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setDefaultEncoding("UTF-8");
        resolver.setMaxUploadSize(1048576000L); // 1000MB
        resolver.setMaxInMemorySize(40960); // 40KB
        return resolver;
    }

    @Override
    public void apply(OperationContext context) {
        // 检查是否为文件上传接口
        if (isFileUploadOperation(context)) {
            // 移除原有的file参数，添加自定义的文件参数
            List<RequestParameter> parameters = context.operationBuilder().build().getRequestParameters()
                    .stream()
                    .filter(param -> !param.getName().equals("file") && !param.getName().equals("files"))
                    .collect(Collectors.toList());

            // 添加文件上传参数（仅支持单文件上传）
            if (context.requestMappingPattern().contains("/upload")) {
                RequestParameter fileParam = new RequestParameterBuilder()
                        .name("file")
                        .description("要上传的文件")
                        .required(true)
                        .in(ParameterType.FORM)
                        .query(q -> q.model(m -> m.scalarModel(ScalarType.STRING)))
                        .build();
                parameters.add(fileParam);
            }

            context.operationBuilder().requestParameters(parameters);
        }
    }

    @Override
    public boolean supports(DocumentationType delimiter) {
        return DocumentationType.OAS_30.equals(delimiter) || DocumentationType.SWAGGER_2.equals(delimiter);
    }

    /**
     * 判断是否为文件上传操作
     *
     * @param context 操作上下文
     * @return 是否为文件上传操作
     */
    private boolean isFileUploadOperation(OperationContext context) {
        return context.requestMappingPattern().contains("/upload") &&
               context.httpMethod().toString().equals("POST");
    }
}
