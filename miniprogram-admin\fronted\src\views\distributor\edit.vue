<template>
  <div class="distributor-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" type="info" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回详情
      </el-button>
      <h2>编辑分销员</h2>
    </div>

    <div v-loading="loading" class="edit-content">
      <el-card>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="edit-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分销员ID">
                <el-input v-model="form.id" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分销员编号">
                <el-input v-model="form.distributorCode" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="绑定用户ID">
                <el-input v-model="form.userId" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" style="width: 100%">
                  <el-option label="活跃" value="active" />
                  <el-option label="非活跃" value="inactive" />
                  <el-option label="冻结" value="frozen" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="佣金比例" prop="commissionRate">
                <el-input
                  v-model.number="form.commissionRate"
                  type="number"
                  :min="0"
                  :max="100"
                  :step="0.01"
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用自定义比例">
                <el-switch
                  v-model="form.customRate"
                  active-text="是"
                  inactive-text="否"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="今日激活人数">
                <el-input-number
                  v-model="form.todayActivated"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专属二维码URL">
                <el-input v-model="form.qrcodeUrl" placeholder="系统自动生成" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">佣金信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="累积佣金总额">
                <el-input
                  v-model.number="form.totalCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="可提现金额">
                <el-input
                  v-model.number="form.availableCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="已提现金额">
                <el-input
                  v-model.number="form.withdrawnCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="未结算金额">
                <el-input
                  v-model.number="form.unsettledCommission"
                  type="number"
                  :step="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">时间信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建时间">
                <el-input :value="formatDate(form.createdAt)" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间">
                <el-input :value="formatDate(form.updatedAt)" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              <el-icon><Check /></el-icon>
              保存修改
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="goBack">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDistributorDetail, updateDistributor } from '@/api/distributor'
import { formatDate } from '@/utils'

const router = useRouter()
const route = useRoute()

const distributorId = route.params.id
const loading = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  id: '',
  distributorCode: '',
  userId: '',
  status: 'active',
  commissionRate: 10.00,
  customRate: false,
  todayActivated: 0,
  totalCommission: 0.00,
  availableCommission: 0.00,
  withdrawnCommission: 0.00,
  unsettledCommission: 0.00,
  qrcodeUrl: '',
  createdAt: '',
  updatedAt: ''
})

// 原始数据备份
const originalForm = reactive({})

// 表单验证规则
const rules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  commissionRate: [
    { required: true, message: '请输入佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间', trigger: 'blur' }
  ]
}

// 获取分销员详情
const fetchDistributorDetail = async () => {
  loading.value = true
  try {
    const response = await getDistributorDetail(distributorId)
    if (response.code === 200) {
      const data = response.data
      
      // 填充表单数据
      Object.keys(form).forEach(key => {
        if (data.hasOwnProperty(key)) {
          form[key] = data[key]
        }
      })
      
      // 备份原始数据
      Object.assign(originalForm, form)
    }
  } catch (error) {
    console.error('获取分销员详情失败:', error)
    ElMessage.error('获取分销员详情失败')
  } finally {
    loading.value = false
  }
}

// 返回详情页
const goBack = () => {
  router.push(`/distributor/detail/${distributorId}`)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitLoading.value = true
    
    // 准备提交数据，只提交有变化的字段
    const updateData = {}
    Object.keys(form).forEach(key => {
      if (form[key] !== originalForm[key] && !['id', 'distributorCode', 'userId', 'createdAt', 'updatedAt'].includes(key)) {
        updateData[key] = form[key]
      }
    })
    
    if (Object.keys(updateData).length === 0) {
      ElMessage.info('没有数据需要更新')
      return
    }
    
    const response = await updateDistributor(distributorId, updateData)
    if (response.code === 200) {
      ElMessage.success('分销员信息更新成功')
      // 更新原始数据
      Object.assign(originalForm, form)
      // 返回详情页
      setTimeout(() => {
        goBack()
      }, 1000)
    }
  } catch (error) {
    console.error('更新分销员信息失败:', error)
    ElMessage.error(error.response?.data?.message || '更新分销员信息失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.assign(form, originalForm)
  formRef.value?.clearValidate()
}

onMounted(() => {
  fetchDistributorDetail()
})
</script>

<style lang="scss" scoped>
.distributor-edit {
  .page-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .edit-content {
    .edit-form {
      max-width: 800px;
      
      .el-divider {
        margin: 30px 0 20px 0;
        
        :deep(.el-divider__text) {
          font-weight: 500;
          color: #409EFF;
        }
      }
      
      .el-form-item {
        margin-bottom: 20px;
      }
      
      .el-input-number {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .distributor-edit {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .edit-form {
      .el-row {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
