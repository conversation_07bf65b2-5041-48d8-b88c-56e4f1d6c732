<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.CityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.City">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="province_id" property="provinceId" />
        <result column="code" property="code" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, province_id, code, sort, status, created_at, updated_at
    </sql>

    <!-- 根据省份ID获取启用的城市列表 -->
    <select id="selectEnabledCitiesByProvinceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM cities
        WHERE province_id = #{provinceId} AND status = 1
        ORDER BY sort ASC, id ASC
    </select>

</mapper>
