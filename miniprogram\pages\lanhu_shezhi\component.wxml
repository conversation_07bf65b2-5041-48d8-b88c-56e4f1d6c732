<view class="page">
  <view class="box_3">
    <view class="list_1">
      <!-- 头像设置 -->
      <view class="list-items_1-0">
        <text lines="1" class="text_2-0">头像</text>
        <button class="avatar-button" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar" style="width: 287rpx; display: block; box-sizing: border-box; left: 396rpx; top: 14rpx">
          <image
            src="{{tempAvatarUrl || userInfo.avatarUrl || '../../images/lanhu_shezhi/FigmaDDSSlicePNGe64ee9f315ff9cb6e31cee6d3bca20c8.png'}}"
            class="label_1-0-1" style="width: 72rpx; display: inline-block; box-sizing: border-box; height: 72rpx; position: relative; left: 103rpx; top: 0rpx">
          </image>
        </button>
      </view>

      <!-- 昵称设置 -->
      <view class="list-items_1-1">
        <text lines="1" class="text_2-1">昵称</text>
        <input
          type="nickname"
          class="nickname-input"
          value="{{tempNickname}}"
          placeholder="{{userInfo.nickname || '请输入昵称'}}"
          bindinput="onNicknameInput"
          bindblur="onNicknameBlur"
          maxlength="20">
        </input>
      </view>

      <!-- 关于我们 -->
      <view class="list-items_1-2" bindtap="onAboutUsClick">
        <text lines="1" class="text_2-2">关于我们</text>
        <image src="../../images/lanhu_shezhi/FigmaDDSSlicePNGb12b195b6e9ab1e73258b454458113c1.png" class="thumbnail_4-2"></image>
      </view>

      <!-- 用户协议 -->
      <view class="list-items_1-3" bindtap="onUserAgreementClick">
        <text lines="1" class="text_2-3">用户协议</text>
        <image src="../../images/lanhu_shezhi/FigmaDDSSlicePNGb12b195b6e9ab1e73258b454458113c1.png" class="thumbnail_4-3"></image>
      </view>
    </view>
    <!-- 退出登录按钮 -->
    <view class="text-wrapper_1" bindtap="onLogout">
      <text lines="1" class="text_4">退出登录</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>