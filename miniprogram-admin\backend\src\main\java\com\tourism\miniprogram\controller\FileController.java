package com.tourism.miniprogram.controller;

import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.dto.FileUploadResult;
import com.tourism.miniprogram.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Api(tags = "文件管理")
public class FileController {

    @Autowired
    private FileService fileService;

    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    @ApiOperation(value = "上传文件", notes = "支持图片、视频、音频文件上传，最大1000MB。请在下方选择文件进行上传。")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form")
    })
    public Result<FileUploadResult> uploadFile(
            @ApiParam(value = "选择要上传的文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
        
        FileUploadResult result = fileService.uploadFile(file);
        
        log.info("文件上传成功: {}", result.getFileName());
        return Result.success(result);
    }

    @PostMapping(value = "/upload/image", consumes = "multipart/form-data")
    @ApiOperation(value = "上传图片", notes = "仅支持图片格式：jpg, jpeg, png, gif, bmp, webp。请在下方选择图片文件进行上传。")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form")
    })
    public Result<FileUploadResult> uploadImage(
            @ApiParam(value = "选择要上传的图片文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("开始上传图片: {}", file.getOriginalFilename());
        
        FileUploadResult result = fileService.uploadFile(file);
        
        log.info("图片上传成功: {}", result.getFileName());
        return Result.success(result);
    }

    @PostMapping(value = "/upload/video", consumes = "multipart/form-data")
    @ApiOperation(value = "上传视频", notes = "仅支持视频格式：mp4, avi, mov, wmv, flv, 3gp, mkv。请在下方选择视频文件进行上传。")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form")
    })
    public Result<FileUploadResult> uploadVideo(
            @ApiParam(value = "选择要上传的视频文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("开始上传视频: {}", file.getOriginalFilename());
        
        FileUploadResult result = fileService.uploadFile(file);
        
        log.info("视频上传成功: {}", result.getFileName());
        return Result.success(result);
    }

    @PostMapping(value = "/upload/audio", consumes = "multipart/form-data")
    @ApiOperation(value = "上传音频", notes = "仅支持音频格式：mp3, wav, flac, aac, ogg, wma, m4a。请在下方选择音频文件进行上传。")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form")
    })
    public Result<FileUploadResult> uploadAudio(
            @ApiParam(value = "选择要上传的音频文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        log.info("开始上传音频: {}", file.getOriginalFilename());
        
        FileUploadResult result = fileService.uploadFile(file);
        
        log.info("音频上传成功: {}", result.getFileName());
        return Result.success(result);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除文件", notes = "根据文件路径删除文件")
    public Result<Boolean> deleteFile(
            @ApiParam(value = "文件路径", required = true, example = "tourism/files/image_20250106_123456.jpg")
            @RequestParam("filePath") String filePath) {
        
        log.info("开始删除文件: {}", filePath);
        
        boolean success = fileService.deleteFile(filePath);
        
        if (success) {
            log.info("文件删除成功: {}", filePath);
            return Result.success(true);
        } else {
            log.warn("文件删除失败: {}", filePath);
            return Result.error("文件删除失败");
        }
    }

    @GetMapping("/download-url")
    @ApiOperation(value = "生成下载链接", notes = "生成带有效期的预签名下载URL")
    public Result<String> generateDownloadUrl(
            @ApiParam(value = "文件路径", required = true, example = "tourism/files/image_20250106_123456.jpg")
            @RequestParam("filePath") String filePath,
            @ApiParam(value = "有效期（秒）", example = "3600")
            @RequestParam(value = "expireTime", required = false) Long expireTime) {
        
        log.info("生成下载链接: {}, 有效期: {}秒", filePath, expireTime);
        
        String downloadUrl;
        if (expireTime != null && expireTime > 0) {
            downloadUrl = fileService.generatePresignedUrl(filePath, expireTime);
        } else {
            downloadUrl = fileService.generatePresignedUrl(filePath);
        }
        
        log.info("下载链接生成成功: {}", downloadUrl);
        return Result.success(downloadUrl);
    }

    @GetMapping("/info")
    @ApiOperation(value = "获取文件信息", notes = "根据文件路径获取文件详细信息")
    public Result<FileUploadResult> getFileInfo(
            @ApiParam(value = "文件路径", required = true, example = "tourism/files/image_20250106_123456.jpg")
            @RequestParam("filePath") String filePath) {
        
        log.info("获取文件信息: {}", filePath);
        
        FileUploadResult fileInfo = fileService.getFileInfo(filePath);
        
        log.info("文件信息获取成功: {}", fileInfo.getFileName());
        return Result.success(fileInfo);
    }

    @GetMapping("/exists")
    @ApiOperation(value = "检查文件是否存在", notes = "根据文件路径检查文件是否存在")
    public Result<Boolean> fileExists(
            @ApiParam(value = "文件路径", required = true, example = "tourism/files/image_20250106_123456.jpg")
            @RequestParam("filePath") String filePath) {
        
        log.info("检查文件是否存在: {}", filePath);
        
        boolean exists = fileService.fileExists(filePath);
        
        log.info("文件存在检查结果: {}, 存在: {}", filePath, exists);
        return Result.success(exists);
    }
}
