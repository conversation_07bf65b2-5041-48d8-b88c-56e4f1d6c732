package com.tourism.exception;

import com.tourism.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 全局异常处理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数验证异常
     * 
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        
        for (FieldError fieldError : fieldErrors) {
            errorMsg.append(fieldError.getField())
                    .append(": ")
                    .append(fieldError.getDefaultMessage())
                    .append("; ");
        }
        
        return Result.badRequest("参数验证失败: " + errorMsg.toString());
    }

    /**
     * 处理绑定异常
     * 
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(BindException.class)
    public Result<String> handleBindException(BindException e) {
        log.warn("参数绑定异常: {}", e.getMessage());
        
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        
        for (FieldError fieldError : fieldErrors) {
            errorMsg.append(fieldError.getField())
                    .append(": ")
                    .append(fieldError.getDefaultMessage())
                    .append("; ");
        }
        
        return Result.badRequest("参数绑定失败: " + errorMsg.toString());
    }

    /**
     * 处理文件上传大小超限异常
     * 
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Result<String> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限: {}", e.getMessage());
        return Result.badRequest("文件大小超出限制，请选择较小的文件");
    }

    /**
     * 处理非法参数异常
     * 
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<String> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        return Result.badRequest(e.getMessage());
    }

    /**
     * 处理运行时异常
     * 
     * @param e 异常
     * @param request 请求
     * @return 响应结果
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<String> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常 - URL: {}, 异常信息: {}", request.getRequestURL(), e.getMessage(), e);
        return Result.error("系统异常: " + e.getMessage());
    }

    /**
     * 处理其他异常
     * 
     * @param e 异常
     * @param request 请求
     * @return 响应结果
     */
    @ExceptionHandler(Exception.class)
    public Result<String> handleException(Exception e, HttpServletRequest request) {
        log.error("系统异常 - URL: {}, 异常信息: {}", request.getRequestURL(), e.getMessage(), e);
        return Result.error("系统异常，请联系管理员");
    }
}
