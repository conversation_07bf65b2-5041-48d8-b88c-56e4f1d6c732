package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.DistributionWithdrawalRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销提现记录服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface DistributionWithdrawalRecordService extends IService<DistributionWithdrawalRecord> {

    /**
     * 根据分销员ID获取提现记录列表
     *
     * @param distributorId 分销员ID
     * @return 提现记录列表
     */
    List<DistributionWithdrawalRecord> getByDistributorId(Integer distributorId);

    /**
     * 根据状态获取提现记录列表
     *
     * @param status 提现状态
     * @return 提现记录列表
     */
    List<DistributionWithdrawalRecord> getByStatus(String status);

    /**
     * 根据分销员ID和状态获取提现记录列表
     *
     * @param distributorId 分销员ID
     * @param status 提现状态
     * @return 提现记录列表
     */
    List<DistributionWithdrawalRecord> getByDistributorIdAndStatus(Integer distributorId, String status);

    /**
     * 创建提现申请
     *
     * @param distributorId 分销员ID
     * @param amount 提现金额
     * @return 提现记录
     */
    DistributionWithdrawalRecord createWithdrawalRequest(Integer distributorId, BigDecimal amount);

    /**
     * 审核提现申请
     *
     * @param withdrawalId 提现记录ID
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（如果不通过）
     * @return 是否成功
     */
    boolean reviewWithdrawalRequest(Integer withdrawalId, boolean approved, String rejectReason);

    /**
     * 完成提现处理
     *
     * @param withdrawalId 提现记录ID
     * @param transactionNo 交易流水号
     * @return 是否成功
     */
    boolean completeWithdrawal(Integer withdrawalId, String transactionNo);

    /**
     * 获取分销员待处理提现金额总计
     *
     * @param distributorId 分销员ID
     * @return 待处理提现金额
     */
    BigDecimal getPendingWithdrawalAmount(Integer distributorId);

    /**
     * 获取分销员已完成提现金额总计
     *
     * @param distributorId 分销员ID
     * @return 已完成提现金额
     */
    BigDecimal getCompletedWithdrawalAmount(Integer distributorId);

    /**
     * 根据交易流水号获取提现记录
     *
     * @param transactionNo 交易流水号
     * @return 提现记录
     */
    DistributionWithdrawalRecord getByTransactionNo(String transactionNo);

    /**
     * 获取系统总提现金额统计
     *
     * @return 总提现金额
     */
    BigDecimal getTotalWithdrawalAmount();

    /**
     * 获取待处理提现记录数量
     *
     * @return 待处理提现记录数量
     */
    Integer getPendingWithdrawalCount();

    /**
     * 生成交易流水号
     *
     * @return 交易流水号
     */
    String generateTransactionNo();

    /**
     * 检查分销员是否可以提现
     *
     * @param distributorId 分销员ID
     * @param amount 提现金额
     * @return 是否可以提现
     */
    boolean canWithdraw(Integer distributorId, BigDecimal amount);
}
