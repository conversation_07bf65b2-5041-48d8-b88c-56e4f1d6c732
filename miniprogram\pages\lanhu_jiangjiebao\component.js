// pages/lanhu_jiangjiebao/component.js
const apiService = require('../../utils/apiService');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    bundleId: null,
    bundleDetail: null,
    loading: true,
    error: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('讲解包页面加载，参数:', options);

    // 获取组合包ID
    const bundleId = options.product_bundles_id || options.bundleId || options.id;

    if (bundleId) {
      this.setData({
        bundleId: parseInt(bundleId),
      });
      this.loadBundleDetail();
    } else {
      console.error('未获取到组合包ID');
      this.setData({
        error: '参数错误：未找到组合包ID',
        loading: false
      });

      wx.showToast({
        title: '参数错误',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 加载组合包详情
   */
  async loadBundleDetail() {
    try {
      this.setData({ loading: true, error: null });

      console.log(`开始加载组合包详情，ID: ${this.data.bundleId}`);

      // 获取组合包详情
      const bundleDetail = await apiService.getProductBundleDetail(this.data.bundleId);

      console.log('组合包详情数据:', bundleDetail);

      if (!bundleDetail) {
        throw new Error('未找到组合包信息');
      }

      // 处理组合包数据
      const processedBundle = this.processBundleData(bundleDetail);

      this.setData({
        bundleDetail: processedBundle,
        loading: false
      });

      // 设置页面标题
      if (processedBundle.name) {
        wx.setNavigationBarTitle({
          title: processedBundle.name
        });
      }

    } catch (error) {
      console.error('加载组合包详情失败:', error);

      this.setData({
        error: error.message || '加载失败，请重试',
        loading: false
      });

      wx.showToast({
        title: '加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 处理组合包数据
   */
  processBundleData(bundle) {
    return {
      ...bundle,
      // 处理状态
      statusText: bundle.status === 1 ? '可购买' : '暂不可购买'
    };
  },



  /**
   * 预览组合包长图
   */
  onPreviewImage() {
    const imageUrl = this.data.bundleDetail?.description;
    if (imageUrl) {
      wx.previewImage({
        urls: [imageUrl],
        current: imageUrl
      });
    }
  },

  /**
   * 购买组合包
   */
  onBuyBundle() {
    const bundle = this.data.bundleDetail;
    if (!bundle) return;

    if (bundle.status !== 1) {
      wx.showToast({
        title: '该组合包暂不可购买',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/lanhu_querendingdan/component?bundleId=${bundle.id}`
    });
  },

  /**
   * 重新加载
   */
  onRetry() {
    if (this.data.bundleId) {
      this.loadBundleDetail();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('讲解包页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('讲解包页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('讲解包页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('讲解包页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadBundleDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const bundle = this.data.bundleDetail;
    return {
      title: bundle ? `${bundle.name} - 讲解包` : '精彩讲解包',
      path: `/pages/lanhu_jiangjiebao/component?product_bundles_id=${this.data.bundleId}`,
      imageUrl: bundle?.imageUrl || ''
    };
  }
});
